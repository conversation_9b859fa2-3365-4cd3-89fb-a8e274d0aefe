from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('交易/紫金理财/产品签约/合同管理')
def _6ee5484c39c08e4da697aa1bbfda8cfe(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119', 预期结果='已签署'):
    # print(1)
    # hxf 生产账户
    # 初始化
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/紫金理财')
    目录校验(device, '二', '紫金理财', '/页面标题')
    点击(device, '/交易/普通交易/紫金理财/紫金理财首页/产品签约')
    sleep(1)
    实际结果 = 获取文本信息(device, '/交易/普通交易/紫金理财/产品签约/第一条合同状态')
    错误校验点 = 非空校验(device, 预期结果, 实际结果)
    结果 = 预期结果 in 实际结果
    return 结果, 错误校验点


