from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('交易/基金交易/紫金基金/产品签约_基金开户')
def _5839f53c605ee58d3e377149d0dcde5e(device, 账号='666630296771', 交易密码='124369', 通信密码='159753', 预期结果1='提交', 预期结果2='已存在'):
    # hxf 生产账户
    # 真实账号
    # 账号 =  '666621794831'
    # 交易密码 =  '110119'
    # 通信密码 = 'rf110119'
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    上滑(device)
    刷新页面_交易(device)
    点击(device, '/交易/普通交易/普通交易首页/基金交易')
    点击(device, '/交易/普通交易/基金交易/紫金基金')
    目录校验(device, '二', '紫金基金', '/页面标题')
    点击(device, '/交易/普通交易/基金交易/紫金基金页面/产品签约')
    sleep(3)
    点击(device, '/交易/普通交易/基金交易/紫金基金/产品签约_基金开户页面/选择基金公司')
    点击(device, '/交易/普通交易/基金交易/紫金基金/基金开户/选择公司/第一个公司')
    点击(device, '/交易/普通交易/基金交易/紫金基金/基金开户/选择公司/确定')
    实际结果 = 获取文本信息(device, '/交易/普通交易/更多/网络投票/投票查询/投票记录详细页面/提示框内容')
    # 点击('/底部确定/确定按钮')
    print("实际结果为", 实际结果)
    错误校验点 = 非空校验(device, 预期结果1 + '或者' + 预期结果2, 实际结果)
    结果 = 预期结果1 in 实际结果 or 预期结果2 in 实际结果
    return 结果, 错误校验点


@casescript('交易/基金交易/紫金基金/分红设置')
def _d5e69f797738bc0a4bae5d6e4d761d82(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    错误校验点 = ''
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    上滑(device)
    刷新页面_交易(device)
    点击(device, '/交易/普通交易/普通交易首页/基金交易')
    点击(device, '/交易/普通交易/基金交易/紫金基金')
    目录校验(device, '二', '紫金基金', '/页面标题')
    点击(device, '/交易/普通交易/基金交易/紫金基金页面/分红设置')
    sleep(3)
    点击(device, '/交易/普通交易/基金交易/紫金基金/分红设置页面/请选择产品')
    try:
        点击(device, '/交易/普通交易/基金交易/紫金基金/分红设置/选择产品/第一个产品')
        sleep(1)
        点击(device, '/交易/普通交易/基金交易/紫金基金/分红设置页面/请选择分红方式')
        点击(device, '/交易/普通交易/基金交易/紫金基金/分红设置/分红方式/现金分红')
        点击(device, '/交易/普通交易/基金交易/紫金基金/分红设置页面/确定')
        实际结果 = 获取文本信息(device, '/交易/普通交易/更多/网络投票/投票查询/投票记录详细页面/提示框内容')
        # print("实际结果为", 实际结果)
        预期结果 = '成功'
        结果 = 预期结果 in 实际结果 or '不可以' in 实际结果
        错误校验点 = 非空校验(device, 预期结果, 实际结果)
        if 预期结果 in 实际结果:
            返回(device)
            sleep(3)
            返回(device)
            sleep(3)
            点击(device, '/交易/普通交易/基金交易/紫金基金页面/撤销委托')
            点击(device, '/交易/普通交易/基金交易/紫金基金/撤单页面/刷新')
            预期结果 = '成功'
            try:
                # 实际结果 = 获取文本信息('/交易/普通交易/基金交易/紫金基金/撤单页面/第一条记录代码')
                print(实际结果)
                sleep(3)
                点击(device, '/交易/普通交易/基金交易/公募及私募基金/撤销委托/撤单页面/分红撤单')
                点击(device, '/交易/普通交易/基金交易/紫金基金/撤单/撤单确认/确定')
                实际结果 = 获取文本信息(device, '/交易/普通交易/基金交易/紫金基金/撤单/提示信息/结果')
                错误校验点 = 非空校验(device, 预期结果, 实际结果)
                # 点击('/交易/普通交易/基金交易/紫金基金/撤单/提示信息/确定')
            except:
                实际结果 = 获取文本信息(device, '/交易/普通交易/基金交易/紫金基金/撤单页面/无记录')
                错误校验点 = 非空校验(device, 预期结果, 实际结果)
    except:
        实际结果 = 获取文本信息(device, '/提示')
        结果 = 实际结果 != ''
        错误校验点 = 为空校验(device)
    return 结果, 错误校验点


@casescript('交易/基金交易/紫金基金/基金购买_认购申购')
def _6fc9442921c7184dd466a69668a3b2e4(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119', 证券代码='004860', 购买金额='1'):
    错误校验点 = ''
    # 证券代码 = '005467'
    # 初始化
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    上滑(device)
    刷新页面_交易(device)
    点击(device, '/交易/普通交易/普通交易首页/基金交易')
    点击(device, '/交易/普通交易/基金交易/紫金基金')
    目录校验(device, '二', '紫金基金', '/页面标题')
    点击(device, '/交易/普通交易/基金交易/紫金基金页面/我的紫金基金')
    sleep(3)
    申购前基金持仓 = 获取文本信息(device, '/交易/普通交易/基金交易/紫金基金页面/我的紫金基金/获取资金金额')
    返回(device)
    点击(device, '/交易/普通交易/基金交易/紫金基金页面/基金购买')
    输入文本(device, '/交易/普通交易/基金交易/紫金基金/基金购买页面/基金代码', 证券代码)
    sleep(1)
    点击(device, '/交易/普通交易/基金交易/紫金基金/基金购买页面/立即购买')
    结果 = False
    try:
        实际结果 = 获取文本信息(device, '/交易/普通交易/基金交易/紫金基金/基金购买页面/提示信息')
        预期结果 = '不可购买'
        错误校验点 = 非空校验(device, 预期结果, 实际结果)
        结果 = 预期结果 in 实际结果
    except:
        点击(device, '/交易/普通交易/基金交易/紫金基金/基金购买/适当性确认/同意')
        点击(device, '/交易/普通交易/基金交易/紫金基金/基金购买/适当性确认/同意')  # 这里可以取巧用上一个按钮
        # 点击('/交易/普通交易/基金交易/紫金基金/基金购买/适当性确认/投资者确认书/同意')
        点击(device, '/交易/普通交易/基金交易/紫金基金/基金购买/合同签署页面/同意勾选框')
        点击(device, '/交易/普通交易/基金交易/紫金基金/基金购买/合同签署页面/确定')
        基金申购 = 获取文本信息(device, '/交易/普通交易/基金交易/紫金基金/基金购买/基金申购页面/基金申购')
        print('==============', 基金申购)
        if 基金申购 == '基金申购':
            # time.sleep(2)
            输入文本(device, '/交易/普通交易/基金交易/紫金基金/基金购买/基金申购页面/购买金额', 购买金额)
            点击(device, '/交易/普通交易/基金交易/紫金基金/基金购买/基金申购页面/最低')
            点击(device, '/交易/普通交易/基金交易/紫金基金/基金购买/基金申购页面/马上申购')
            点击(device, '/交易/普通交易/基金交易/紫金基金/基金购买/基金申购/提示信息/确定')
            sleep(2)
            预期结果 = '成功'
            实际结果 = 获取文本信息(device, '/交易/普通交易/基金交易/紫金基金/基金购买/基金购买页面/结果')
            print('实际结果', 实际结果)
            错误校验点 = 非空校验(device, 预期结果, 实际结果)
            结果 = 预期结果 in 实际结果
            点击(device, '/交易/普通交易/基金交易/紫金基金/基金购买/基金购买页面/完成')
            if 结果:
                返回(device)
                点击(device, '/交易/普通交易/基金交易/紫金基金页面/我的紫金基金')
                sleep(3)
                申购后基金持仓 = 获取文本信息(device, '/交易/普通交易/基金交易/紫金基金页面/我的紫金基金/获取资金金额')
                结果 = 申购前基金持仓 < 申购后基金持仓
                if not 结果:
                    截屏(device)
                    错误校验点 = '申购前基金持仓：%s，申购后基金持仓：%s' % (申购前基金持仓, 申购后基金持仓)
        else:
            点击(device, '/交易/普通交易/基金交易/公募及私募基金/基金购买/认购申购页面/金额输入框1')
            点击(device, '/交易/普通交易/基金交易/紫金基金/基金购买/认购申购页面/金额确定')
            点击(device, '/交易/普通交易/基金交易/公募及私募基金/基金购买/认购申购页面/确定购买')
            sleep(2)
            预期结果 = '已提交'
            实际结果 = 获取文本信息(device, '/交易/普通交易/基金交易/紫金基金/基金购买/委托提交结果页面/结果')
            错误校验点 = 非空校验(device, 预期结果, 实际结果)
            点击(device, '/交易/普通交易/基金交易/紫金基金/基金购买/委托提交结果页面/确定')
            结果 = 预期结果 in 实际结果
        print('结果', 结果)
    return 结果, 错误校验点


@casescript('交易/基金交易/紫金基金/基金赎回')
def _22a2ed157a9cf5fdb993620bcb04da83(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119', 证券代码='004860', 赎回份额='1'):
    错误校验点 = ''
    # 业务交易数据 = 获取业务交易信息('交易/基金交易/紫金基金/基金赎回')
    # 证券代码 = 业务交易数据['证券代码']
    # 初始化
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    上滑(device)
    刷新页面_交易(device)
    点击(device, '/交易/普通交易/普通交易首页/基金交易')
    点击(device, '/交易/普通交易/基金交易/紫金基金')
    目录校验(device, '二', '紫金基金', '/页面标题')
    点击(device, '/交易/普通交易/基金交易/紫金基金页面/基金赎回')
    sleep(3)
    输入文本(device, '/交易/普通交易/基金交易/紫金基金/基金赎回页面/基金代码', 证券代码)
    sleep(1)
    输入文本(device, '/交易/普通交易/基金交易/紫金基金/基金赎回页面/赎回份额', 赎回份额)
    赎回前份额 = 截取合同号(device, 获取文本信息(device, '/交易/基金交易/公募及私募基金/基金赎回/可赎回份额'))
    点击(device, '/交易/普通交易/基金交易/紫金基金/基金赎回页面/立即赎回')
    sleep(2)
    # 份额 = 获取文本信息('/交易/普通交易/基金交易/紫金基金/基金赎回/普通赎回/赎回份额')
    # print('份额：%s,比较：%s' % (份额, 赎回份额 == 份额))
    点击(device, '/交易/普通交易/基金交易/紫金基金/基金赎回/普通赎回/立即赎回')
    # 总份额 = 获取文本信息('/交易/普通交易/基金交易/紫金基金/基金赎回/基金赎回/总份额')
    # print('总份额：%s,比较：%s' % (总份额, 赎回份额 == 总份额))
    点击(device, '/交易/普通交易/基金交易/紫金基金/基金赎回/基金赎回/确认')
    try:
        实际结果 = 获取文本信息(device, '/交易/普通交易/基金交易/紫金基金/基金赎回/基金赎回/提示信息')
        预期结果 = '不可以交易'
        错误校验点 = 非空校验(device, 预期结果, 实际结果)
        结果 = 预期结果 in 实际结果
    except:
        预期结果 = '已提交'
        实际结果 = 获取文本信息(device, '/交易/普通交易/基金交易/紫金基金/基金赎回/取出页面/结果')
        错误校验点 = 非空校验(device, 预期结果, 实际结果)
        结果 = 预期结果 in 实际结果
        if 结果:
            点击(device, '/交易/普通交易/基金交易/紫金基金/基金赎回/取出页面/完成')
            点击(device, '/交易/普通交易/基金交易/紫金基金页面/基金赎回')
            sleep(3)
            输入文本(device, '/交易/普通交易/基金交易/紫金基金/基金赎回页面/基金代码', 证券代码)
            赎回后份额 = 截取合同号(device, 获取文本信息(device, '/交易/基金交易/公募及私募基金/基金赎回/可赎回份额'))
            print('赎回前份额为：%s；赎回后份额为：%s' % (赎回前份额, 赎回后份额))
            结果 = 赎回前份额 > 赎回后份额
            if not 结果:
                截屏(device)
                错误校验点 = '赎回前份额为：%s；赎回后份额为：%s' % (赎回前份额, 赎回后份额)
    return 结果, 错误校验点


@casescript('交易/基金交易/紫金基金/我的紫金基金')
def _a60293b080a1d27f71949e9c8ac9f42b(device, 账号='666630296771', 交易密码='124369', 通信密码='159753'):
    错误校验点 = ''
    # hxf 生产账户
    # 初始化
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    上滑(device)
    刷新页面_交易(device)
    点击(device, '/交易/普通交易/普通交易首页/基金交易')
    点击(device, '/交易/普通交易/基金交易/紫金基金')
    目录校验(device, '二', '紫金基金', '/页面标题')
    点击(device, '/交易/普通交易/基金交易/紫金基金页面/我的紫金基金')
    sleep(5)
    实际结果 = 获取文本信息(device, '/交易/普通交易/基金交易/紫金基金页面/我的紫金基金/获取资金金额')
    print('基金总持仓：', 实际结果)
    try:
        结果 = float(实际结果) > 0
        错误校验点 = 数字校验(device, 实际结果)
    except:
        结果 = 实际结果 != ''
        错误校验点 = 为空校验(device)
    return 结果, 错误校验点


@casescript('交易/基金交易/紫金基金/撤销委托')
def _e8be30380dd67e5aa2e958a03735db90(device, 账号='666630296771', 交易密码='124369', 通信密码='159753', 预期结果='成功'):
    错误校验点 = ''
    # hxf 生产账户
    # 真实账号
    # 账号 =  '666621794831'
    # 交易密码 =  '110119'
    # 通信密码 = 'rf110119'
    # 初始化
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    上滑(device)
    刷新页面_交易(device)
    点击(device, '/交易/普通交易/普通交易首页/基金交易')
    点击(device, '/交易/普通交易/基金交易/紫金基金')
    目录校验(device, '二', '紫金基金', '/页面标题')
    点击(device, '/交易/普通交易/基金交易/紫金基金页面/撤销委托')
    点击(device, '/交易/普通交易/基金交易/紫金基金/撤单页面/刷新')
    try:
        实际结果 = 获取文本信息(device, '/交易/普通交易/基金交易/紫金基金/撤单页面/第一条记录代码')
        print(实际结果)
        点击(device, '/交易/普通交易/基金交易/紫金基金/撤单页面/第一条记录撤单')
        点击(device, '/交易/普通交易/基金交易/紫金基金/撤单/撤单确认/确定')
        实际结果 = 获取文本信息(device, '/交易/普通交易/基金交易/紫金基金/撤单/提示信息/结果')
        错误校验点 = 非空校验(device, 预期结果, 实际结果)
        点击(device, '/交易/普通交易/基金交易/紫金基金/撤单/提示信息/确定')
    except:
        实际结果 = 获取文本信息(device, '/交易/普通交易/基金交易/紫金基金/撤单页面/无记录')
        错误校验点 = 非空校验(device, 预期结果, 实际结果)
    结果 = 预期结果 in 实际结果 or 实际结果 != ''
    return 结果, 错误校验点


@casescript('交易/基金交易/紫金基金/更多_基金转换')
def _1a507bb6cd1a28652b6f00acbcb2e81b(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119', 转换份额='1'):
    错误校验点 = ''
    # 转出基金 = '160222'
    # 证券代码 = '160211'
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    上滑(device)
    刷新页面_交易(device)
    点击(device, '/交易/普通交易/普通交易首页/基金交易')
    点击(device, '/交易/普通交易/基金交易/紫金基金')
    目录校验(device, '二', '紫金基金', '/页面标题')
    点击(device, '/交易/普通交易/基金交易/紫金基金页面/更多')
    点击(device, '/交易/普通交易/基金交易/紫金基金/更多页面/基金转换')
    sleep(3)
    点击(device, '/交易/普通交易/基金交易/紫金基金/更多/基金转换页面/转出基金')
    点击(device, '/交易/普通交易/基金交易/紫金基金/更多/基金转换/转出基金/第三个基金')
    # 输入文本('/交易/普通交易/基金交易/紫金基金/更多/基金转换页面/转出基金', 转出基金)
    转换前份额 = 获取文本信息(device, '/交易/基金交易/紫金基金/更多/基金转换/可转份额')
    # 输入文本('/交易/普通交易/基金交易/紫金基金/更多/基金转换页面/转入基金', 证券代码)
    点击(device, '/交易/普通交易/基金交易/紫金基金/更多/基金转换页面/转入基金')
    点击(device, '/交易/普通交易/基金交易/紫金基金/更多/基金转换/转入基金/第一个基金')
    输入文本(device, '/交易/普通交易/基金交易/紫金基金/更多/基金转换页面/转换份额', 转换份额)
    点击(device, '/交易/普通交易/基金交易/紫金基金/更多/基金转换页面/空白处')
    点击(device, '/交易/普通交易/基金交易/紫金基金/更多/基金转换页面/确定')
    try:
        实际结果 = 获取文本信息(device, '/交易/普通交易/基金交易/紫金基金/更多/基金转换页面/提示信息')
        错误校验点 = 为空校验(device)
        结果 = 实际结果 != ''
    except:
        点击(device, '/交易/普通交易/基金交易/紫金基金/更多/基金转换/适当性确认/同意')
        点击(device, '/交易/普通交易/基金交易/紫金基金/更多/基金转换/适当性确认/投资者确认书/同意')  # 这里可以取巧用上一个按钮
        点击(device, '/交易/普通交易/基金交易/紫金基金/更多/基金转换/合同签署页面/同意勾选框')
        点击(device, '/交易/普通交易/基金交易/紫金基金/更多/基金转换/合同签署页面/确定')
        点击(device, '/确定/确定按钮')
        实际结果 = 获取文本信息(device, '/交易/普通交易/基金交易/公募及私募基金/更多/基金转换页面/结果')
        预期结果 = '成功'
        原因 = ''
        if 预期结果 in 实际结果:
            点击(device, '/底部确认/确认按钮')
            返回(device)
            点击(device, '/交易/普通交易/基金交易/紫金基金/更多页面/基金转换')
            sleep(3)
            点击(device, '/交易/普通交易/基金交易/紫金基金/更多/基金转换页面/转出基金')
            点击(device, '/交易/普通交易/基金交易/紫金基金/更多/基金转换/转出基金/第三个基金')
            转换后份额 = 获取文本信息(device, '/交易/基金交易/紫金基金/更多/基金转换/可转份额')
            结果 = 转换前份额 > 转换后份额
            if not 结果:
                截屏(device)
                错误校验点 = '转换前份额为：%s；转换后份额为：%s' % (转换前份额, 转换后份额)
        else:
            结果 = False
            原因 = 获取文本信息(device, '/交易/普通交易/基金交易/公募及私募基金/更多/基金转换页面/失败原因')
        错误校验点 = 非空校验(device, 预期结果, 原因)
    return 结果, 错误校验点


@casescript('交易/基金交易/紫金基金/更多_查询基金账户')
def _b20ad90de56a5a9cf5074890e7694d14(device, 账号='666630296771', 交易密码='124369', 通信密码='159753', 预期结果='正常'):
    # hxf 生产账户
    # 真实账号
    # 账号 =  '666621794831'
    # 交易密码 =  '110119'
    # 通信密码 = 'rf110119'
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    上滑(device)
    刷新页面_交易(device)
    点击(device, '/交易/普通交易/普通交易首页/基金交易')
    点击(device, '/交易/普通交易/基金交易/紫金基金')
    目录校验(device, '二', '紫金基金', '/页面标题')
    点击(device, '/交易/普通交易/基金交易/紫金基金页面/更多')
    sleep(1)
    点击(device, '/交易/普通交易/基金交易/紫金基金/更多页面/查询基金账户')
    sleep(1)
    实际结果 = 获取文本信息(device, '/交易/普通交易/基金交易/紫金基金/更多/基金账户页面/状态')
    错误校验点 = 非空校验(device, 预期结果, 实际结果)
    结果 = 预期结果 in 实际结果
    return 结果, 错误校验点


@casescript('交易/基金交易/紫金基金/查询我的委托_历史委托')
def _6c9ca17d71965f633d9d035f598dabcc(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    错误校验点 = ''
    # hxf 生产账户
    # 初始化
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    上滑(device)
    刷新页面_交易(device)
    点击(device, '/交易/普通交易/普通交易首页/基金交易')
    点击(device, '/交易/普通交易/基金交易/紫金基金')
    目录校验(device, '二', '紫金基金', '/页面标题')
    点击(device, '/交易/普通交易/基金交易/紫金基金页面/查询我的委托')
    if 获取控件对象(device, '/交易/普通交易/基金交易/紫金基金页面/查询我的委托/历史委托') == '':
        点击(device, '/交易/普通交易/紫金理财/查询我的委托页面/刷新')
    点击(device, '/交易/普通交易/基金交易/紫金基金页面/查询我的委托/历史委托')
    结果 = False
    if 获取文本信息(device, '/交易/普通交易/基金交易/公募及私募基金/公募及私募基金首页/查询我的委托/历史委托/历史委托第一条记录') != '':
        # 滑动控件至屏幕内('/交易/普通交易/基金交易/公募及私募基金/公募及私募基金首页/查询我的委托/历史委托/成功记录')
        # 点击('/交易/普通交易/基金交易/公募及私募基金/公募及私募基金首页/查询我的委托/历史委托/成功记录')
        点击(device, '/交易/普通交易/基金交易/公募及私募基金/公募及私募基金首页/查询我的委托/历史委托/历史委托第一条记录')
        实际结果 = 获取文本信息(device, '/交易/普通交易/基金交易/公募及私募基金/公募及私募基金首页/查询我的委托/历史委托/成功记录/基金代码')
        错误校验点 = 为空校验(device)
        结果 = 实际结果 != ''
    else:
        实际结果 = 获取控件对象(device, '/交易/普通交易/基金交易/紫金基金/查询我的委托/历史委托页面/无记录')
        错误校验点 = 为空校验(device)
        结果 = 实际结果 != ''
    return 结果, 错误校验点


@casescript('交易/基金交易/紫金基金/查询我的委托_当前委托')
def _5d043c36f0fa7123ca5161be06a332d5(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    错误校验点 = ''
    # hxf 生产账户
    # 初始化
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    上滑(device)
    刷新页面_交易(device)
    点击(device, '/交易/普通交易/普通交易首页/基金交易')
    点击(device, '/交易/普通交易/基金交易/紫金基金')
    目录校验(device, '二', '紫金基金', '/页面标题')
    点击(device, '/交易/普通交易/基金交易/紫金基金页面/查询我的委托')
    点击(device, '/交易/普通交易/基金交易/紫金基金页面/查询我的委托/当前委托')
    结果 = False
    if 获取控件对象(device, '/交易/普通交易/基金交易/紫金基金页面/查询我的委托/当前委托第一条记录'):
        买卖方向 = 获取文本信息(device, '/交易/基金交易/公募及私募基金/查询我的委托/当前/第一条/买卖方向')
        if 买卖方向 == '其它':
            证券代码 = 获取文本信息(device, '/交易/普通交易/基金交易/公募及私募基金/撤销委托/撤单页面/其它代码')
        elif 买卖方向 == '申购':
            证券代码 = 获取文本信息(device, '/交易/普通交易/基金交易/公募及私募基金/撤销委托/撤单页面/申购代码')
        elif 买卖方向 == '赎回':
            证券代码 = 获取文本信息(device, '/交易/普通交易/基金交易/公募及私募基金/撤销委托/撤单页面/赎回代码')
        结果 = 证券代码 != ''
        if not 结果:
            错误校验点 = 为空校验(device)
    else:
        实际结果 = 获取控件对象(device, '/交易/普通交易/基金交易/公募及私募基金/公募及私募基金首页/撤销委托/无记录')
        # 预期结果 = '暂无当前委托订单'
        # 全局变量.错误校验点 = 非空校验(预期结果, 实际结果)
        # 结果 = 预期结果 in 实际结果
        错误校验点 = 为空校验(device)
        结果 = 实际结果 != ''
    return 结果, 错误校验点


