from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('行情/市场/全球/全球重要指数/亚太市场/富时中国50')
def _8083b34f92c9b2854bfdde66a2a698ea(device):
    sleep(3)
    点击(device, '/安信手机证券/行情界面跳转按钮')
    点击(device, '/安信手机证券/行情界面/行情')
    # if 是否存在控件(device, '/安信手机证券/浮动弹窗/关闭今日打新'):
    #     点击(device, '/安信手机证券/浮动弹窗/关闭今日打新')
    点击(device, '/安信手机证券/行情界面/行情/沪深')
    点击(device, '/安信手机证券/行情界面/行情/沪深/创业')
    行情点数 = 获取文本信息(device, '/安信手机证券/行情界面/行情/沪深/创业/创业板综')
    print("行情点数:::", 行情点数)
    结果 = 行情点数 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


@casescript('行情/市场/全球/全球重要指数/亚太市场/富时中国A50')
def _a3159562c3b102893d2cf165811e2f8c(device):
    sleep(3)
    点击(device, '/安信手机证券/行情界面跳转按钮')
    点击(device, '/安信手机证券/行情界面/行情')

    点击(device, '/安信手机证券/行情界面/行情/沪深')
    点击(device, '/安信手机证券/行情界面/行情/沪深/创业')
    行情点数 = 获取文本信息(device, '/安信手机证券/行情界面/行情/沪深/创业/创业板50')
    print("行情点数:::", 行情点数)
    结果 = 行情点数 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


@casescript('行情/市场/全球/全球重要指数/亚太市场/富时台湾50')
def _e50189e45917b034354bb15749dd1143(device):
    登录前页面处理(device)
    点击(device, '/底部导航栏/行情界面跳转按钮')
    目录校验(device, '一', '行情', '/行情/选中状态')
    点击(device, '/行情/市场/市场标题栏')
    控件左滑(device, '/行情/市场/沪深按钮标签')
    点击(device, '/行情/市场/全球/全球标签')
    目录校验(device, '二', '全球', '/行情/更多/选中状态')
    点击(device, '/行情/市场/全球/全球重要指数/更多')
    sleep(2)
    滑动控件至屏幕内(device, '/行情/市场/全球/全球重要指数/更多/亚太市场/富时台湾50')
    闭市 = 获取文本信息(device, '/行情/市场/全球/全球重要指数/更多/亚太市场/富时台湾50/是否闭市')
    点击(device, '/行情/市场/全球/全球重要指数/更多/亚太市场/富时台湾50')
    sleep(5)
    实际结果 = 获取文本信息(device, '/行情/市场/股指/股指期货/成分品种校验/价格')
    错误校验点 = 为空校验(device)
    # 结果 = 实际结果 != '' and 实际结果 != '--'
    结果 = 实际结果 != '' and (实际结果 != '--' or 闭市 == '闭市')
    return 结果, 错误校验点


@casescript('行情/市场/全球/全球重要指数/亚太市场/富时新加坡全股')
def _0f37b5aac513924f1156820973a919b2(device):
    登录前页面处理(device)
    点击(device, '/底部导航栏/行情界面跳转按钮')
    目录校验(device, '一', '行情', '/行情/选中状态')
    点击(device, '/行情/市场/市场标题栏')
    控件左滑(device, '/行情/市场/沪深按钮标签')
    点击(device, '/行情/市场/全球/全球标签')
    目录校验(device, '二', '全球', '/行情/更多/选中状态')
    点击(device, '/行情/市场/全球/全球重要指数/更多')
    sleep(2)
    滑动控件至屏幕内(device, '/行情/市场/全球/全球重要指数/更多/亚太市场/富时新加坡全股')
    闭市 = 获取文本信息(device, '/行情/市场/全球/全球重要指数/更多/亚太市场/富时新加坡全股/是否闭市')
    点击(device, '/行情/市场/全球/全球重要指数/更多/亚太市场/富时新加坡全股')
    sleep(5)
    实际结果 = 获取文本信息(device, '/行情/市场/股指/股指期货/成分品种校验/价格')
    错误校验点 = 为空校验(device)
    # 结果 = 实际结果 != '' and 实际结果 != '--'
    结果 = 实际结果 != '' and (实际结果 != '--' or 闭市 == '闭市')
    return 结果, 错误校验点


@casescript('行情/市场/全球/全球重要指数/亚太市场/富时新加坡航运')
def _4cfb0cc85614f858263c5255aa5f6939(device):
    登录前页面处理(device)
    点击(device, '/底部导航栏/行情界面跳转按钮')
    目录校验(device, '一', '行情', '/行情/选中状态')
    点击(device, '/行情/市场/市场标题栏')
    控件左滑(device, '/行情/市场/沪深按钮标签')
    点击(device, '/行情/市场/全球/全球标签')
    目录校验(device, '二', '全球', '/行情/更多/选中状态')
    点击(device, '/行情/市场/全球/全球重要指数/更多')
    sleep(2)
    滑动控件至屏幕内(device, '/行情/市场/全球/全球重要指数/更多/亚太市场/富时新加坡航运')
    闭市 = 获取文本信息(device, '/行情/市场/全球/全球重要指数/更多/亚太市场/富时新加坡航运/是否闭市')
    点击(device, '/行情/市场/全球/全球重要指数/更多/亚太市场/富时新加坡航运')
    sleep(5)
    实际结果 = 获取文本信息(device, '/行情/市场/股指/股指期货/成分品种校验/价格')
    错误校验点 = 为空校验(device)
    # 结果 = 实际结果 != '' and 实际结果 != '--'
    结果 = 实际结果 != '' and (实际结果 != '--' or 闭市 == '闭市')
    return 结果, 错误校验点


@casescript('行情/市场/全球/全球重要指数/亚太市场/富时泰国')
def _2317ec912bc720a410a7570757067a05(device):
    登录前页面处理(device)
    点击(device, '/底部导航栏/行情界面跳转按钮')
    目录校验(device, '一', '行情', '/行情/选中状态')
    点击(device, '/行情/市场/市场标题栏')
    控件左滑(device, '/行情/市场/沪深按钮标签')
    点击(device, '/行情/市场/全球/全球标签')
    目录校验(device, '二', '全球', '/行情/更多/选中状态')
    点击(device, '/行情/市场/全球/全球重要指数/更多')
    sleep(2)
    滑动控件至屏幕内(device, '/行情/市场/全球/全球重要指数/更多/亚太市场/富时泰国')
    闭市 = 获取文本信息(device, '/行情/市场/全球/全球重要指数/更多/亚太市场/富时泰国/是否闭市')
    点击(device, '/行情/市场/全球/全球重要指数/更多/亚太市场/富时泰国')
    sleep(5)
    实际结果 = 获取文本信息(device, '/行情/市场/股指/股指期货/成分品种校验/价格')
    错误校验点 = 为空校验(device)
    # 结果 = 实际结果 != '' and 实际结果 != '--'
    结果 = 实际结果 != '' and (实际结果 != '--' or 闭市 == '闭市')
    return 结果, 错误校验点


@casescript('行情/市场/全球/全球重要指数/亚太市场/富时马来西亚')
def _7260c251e09745f9ce4c27140d790000(device):
    登录前页面处理(device)
    点击(device, '/底部导航栏/行情界面跳转按钮')
    目录校验(device, '一', '行情', '/行情/选中状态')
    点击(device, '/行情/市场/市场标题栏')
    控件左滑(device, '/行情/市场/沪深按钮标签')
    点击(device, '/行情/市场/全球/全球标签')
    目录校验(device, '二', '全球', '/行情/更多/选中状态')
    点击(device, '/行情/市场/全球/全球重要指数/更多')
    sleep(2)
    滑动控件至屏幕内(device, '/行情/市场/全球/全球重要指数/更多/亚太市场/富时马来西亚')
    闭市 = 获取文本信息(device, '/行情/市场/全球/全球重要指数/更多/亚太市场/富时马来西亚/是否闭市')
    点击(device, '/行情/市场/全球/全球重要指数/更多/亚太市场/富时马来西亚')
    sleep(5)
    实际结果 = 获取文本信息(device, '/行情/市场/股指/股指期货/成分品种校验/价格')
    错误校验点 = 为空校验(device)
    # 结果 = 实际结果 != '' and 实际结果 != '--'
    结果 = 实际结果 != '' and (实际结果 != '--' or 闭市 == '闭市')
    return 结果, 错误校验点


@casescript('行情/市场/全球/全球重要指数/亚太市场/恒生国企')
def _ff008ee4592146cb505bd7265b3fd9a3(device):
    登录前页面处理(device)
    点击(device, '/底部导航栏/行情界面跳转按钮')
    目录校验(device, '一', '行情', '/行情/选中状态')
    点击(device, '/行情/市场/市场标题栏')
    控件左滑(device, '/行情/市场/沪深按钮标签')
    点击(device, '/行情/市场/全球/全球标签')
    目录校验(device, '二', '全球', '/行情/更多/选中状态')
    点击(device, '/行情/市场/全球/全球重要指数/更多')
    sleep(2)
    滑动控件至屏幕内(device, '/行情/市场/全球/全球重要指数/更多/亚太市场/恒生国企')
    闭市 = 获取文本信息(device, '/行情/市场/全球/全球重要指数/更多/亚太市场/恒生国企/是否闭市')
    点击(device, '/行情/市场/全球/全球重要指数/更多/亚太市场/恒生国企')
    sleep(5)
    实际结果 = 获取文本信息(device, '/行情/市场/股指/股指期货/成分品种校验/价格')
    错误校验点 = 为空校验(device)
    # 结果 = 实际结果 != '' and 实际结果 != '--'
    结果 = 实际结果 != '' and (实际结果 != '--' or 闭市 == '闭市')
    return 结果, 错误校验点


