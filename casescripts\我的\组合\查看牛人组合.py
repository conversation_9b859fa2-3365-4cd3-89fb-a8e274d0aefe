from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('我的/组合/查看牛人组合/常胜榜')
def _5fa5fe758f167e7947ce2fc16904037b(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # 初始化
    登录前页面处理(device)
    # 理财登陆(账号, 交易密码, 通信密码)
    # 交易登录(账号, 交易密码, 通信密码)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    # time.sleep(3)
    # 上滑()
    目录校验(device, '一', '我的', '/我的/选中状态')
    循环滑动(device, '/我的/组合', "上")
    点击(device, '/我的/组合')
    目录校验(device, '二', '我的组合', '/页面标题')
    点击(device, '/我的/组合/查看牛人组合')
    sleep(5)
    点击(device, '/我的/组合/查看牛人组合/常胜榜')
    sleep(5)
    实际结果 = 获取文本信息(device, '/我的/组合/查看牛人组合/收益榜/第一个名字')
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


@casescript('我的/组合/查看牛人组合/收益榜')
def _bbe3bc8c91c630b5e3efb6b9a2dc1cef(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # 初始化
    登录前页面处理(device)
    # 理财登陆(账号, 交易密码, 通信密码)
    # 交易登录(账号, 交易密码, 通信密码)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    # time.sleep(3)
    # 上滑()
    目录校验(device, '一', '我的', '/我的/选中状态')
    循环滑动(device, '/我的/组合', "上")
    点击(device, '/我的/组合')
    目录校验(device, '二', '我的组合', '/页面标题')
    点击(device, '/我的/组合/查看牛人组合')
    sleep(5)
    点击(device, '/我的/组合/查看牛人组合/收益榜')
    sleep(5)
    实际结果 = 获取文本信息(device, '/我的/组合/查看牛人组合/收益榜/第一个名字')
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


@casescript('我的/组合/查看牛人组合/热门组合')
def _ce4b5b2a2247949d3aab050b9a704ae1(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # 初始化
    登录前页面处理(device)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    目录校验(device, '一', '我的', '/我的/选中状态')
    循环滑动(device, '/我的/组合', "上")
    点击(device, '/我的/组合')
    目录校验(device, '二', '我的组合', '/页面标题')
    点击(device, '/我的/组合/查看牛人组合')
    sleep(5)
    循环滑动(device, '/我的/组合/查看牛人组合/热门组合', '上')
    实际结果 = 获取文本信息(device, '/我的/组合/查看牛人组合/热门组合/第一个累计总收益')
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


@casescript('我的/组合/查看牛人组合/牛人热股')
def _9f47e7e41bbf4eaff27e1e7930ef02aa(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # 初始化
    登录前页面处理(device)
    # 理财登陆(账号, 交易密码, 通信密码)
    # 交易登录(账号, 交易密码, 通信密码)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    # time.sleep(3)
    # 上滑()
    目录校验(device, '一', '我的', '/我的/选中状态')
    循环滑动(device, '/我的/组合', "上")
    点击(device, '/我的/组合')
    目录校验(device, '二', '我的组合', '/页面标题')
    点击(device, '/我的/组合/查看牛人组合')
    sleep(5)
    上滑(device)
    # 循环滑动('/我的/组合/查看牛人组合/牛人热股', '上')
    实际结果 = 获取文本信息(device, '/我的/组合/查看牛人组合/牛人热股/第一个热股名称')
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


@casescript('我的/组合/查看牛人组合/牛人讨论区')
def _4234328d4e3bcbdf4fd5a682ca0c3087(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # 初始化
    登录前页面处理(device)
    # 理财登陆(账号, 交易密码, 通信密码)
    # 交易登录(账号, 交易密码, 通信密码)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    # time.sleep(3)
    # 上滑()
    目录校验(device, '一', '我的', '/我的/选中状态')
    循环滑动(device, '/我的/组合', "上")
    点击(device, '/我的/组合')
    目录校验(device, '二', '我的组合', '/页面标题')
    点击(device, '/我的/组合/查看牛人组合')
    sleep(5)
    # 循环滑动('/我的/组合/查看牛人组合/牛人讨论区', '上')
    上滑(device)
    上滑(device)
    实际结果 = 获取文本信息(device, '/我的/组合/查看牛人组合/牛人讨论区/第一个讨论区标题')
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


