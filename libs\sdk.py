import configparser
import functools
import logging
import os
import time
import platform
import minio
import oss2
import json
import base64
import requests
import urllib3
import signal
import socket
import subprocess
# import psutil
import zlib
import re


urllib3.disable_warnings()

from libs.cache import caches
from libs.runcase import define_get_callback

buffer = {}
test_cfg = "./config.ini"
config_raw = configparser.ConfigParser()
config_raw.read(test_cfg,encoding='utf-8')

API_URL = config_raw.get('cloud', 'API_URL', fallback='http://127.0.0.1:8000/api/')
API_TOKEN = config_raw.get('cloud', 'API_TOKEN', fallback='')
EU_ID = config_raw.get('cloud', 'EU_ID', fallback='')
NAS_URL = config_raw.get('cloud', 'NAS_URL', fallback='')
UPLOAD_STATUS = config_raw.get('cloud', 'UPLOAD_STATUS', fallback='fail')
UPLOAD_STATUS_LIST = UPLOAD_STATUS.split(',')
WIRED=config_raw.get('cloud', 'WIRED', fallback='')
AUTO_RESTART = config_raw.get('cloud', 'AUTO_RESTART', fallback='')
AI_POLICY = config_raw.get('cloud', 'AI_POLICY', fallback='false')
FIND_IMAGE = config_raw.get('cloud', 'FIND_IMAGE', fallback='false')
MARK_SCREEN = config_raw.get('cloud', 'MARK_SCREEN', fallback='false')
SOCKET_HOST = config_raw.get('cloud', 'SOCKET_HOST', fallback='*************:3000')
time_cost = config_raw.get('cloud', 'time_cost', fallback="false")
video_compu = config_raw.get('cloud', 'video_compu', fallback="false")
is_track = config_raw.get('cloud', 'is_track', fallback="false")
record_type = config_raw.get('cloud', 'record_type', fallback="app")
logcat = config_raw.get('cloud', 'logcat', fallback="false")
upload_logcat = config_raw.get('cloud', 'upload_logcat', fallback="false")
perf_log = config_raw.get('cloud', 'perf_log', fallback="false")
monkey_open = config_raw.get('cloud', 'monkey_open', fallback="false")
log_join = config_raw.get('cloud', 'log_join', fallback="false")
mitm = config_raw.get('cloud', 'MITM', fallback="false")
start_check_pop = config_raw.get('cloud', 'start_check_pop', fallback="false")
ADB_PATH = config_raw.get('cloud', 'ADB_PATH', fallback="")
LLM = config_raw.get('cloud', 'LLM', fallback='false')

APP_RESTART = config_raw.get('cloud', 'APP_RESTART', fallback='true')

doubao_api_key = config_raw.get('cloud', 'doubao_api_key', fallback='')

max_ai_times = 500
ai_times = 0
doubao_model = "doubao-1-5-thinking-vision-pro-250428"

if doubao_api_key:
    # pip install volcengine-python-sdk[ark]
    from volcenginesdkarkruntime import Ark
    doubao_client = Ark(api_key=doubao_api_key)


if perf_log == 'true':
    import asyncio
    import threading
    import performance
    import psutil
    import dashscope
    import inspect

# 忽略appium解析界面超时
ignore_timeout = config_raw.get('cloud', 'ignore_timeout', fallback='false')

# 文字识别服务地址
# ai_server_url = config_raw.get('cloud', 'ai_server_url', fallback='http://************:8868/predict/ocr_system')
ai_server_url = config_raw.get('cloud', 'ai_server_url', fallback='http://************:8868/predict/ocr_system')

# 滑块缺口识别
slider_url = config_raw.get('cloud', 'slider_url', fallback='http://***********:5000/img')
# 获取实时行情
current_market_url = config_raw.get('cloud', 'current_market_url', fallback='http://*************:8008/market')
# 获取实时指数
current_index_url = config_raw.get('cloud', 'current_index_url', fallback='http://*************:8010/get_index')
# 获取实时指数涨跌信息
index_info_url = config_raw.get('cloud', 'index_info_url', fallback="http://*************:8008/index_info")
# 获取指数瞬时信息
ws_index_url = config_raw.get('cloud', 'ws_index_url', fallback="http://*************:8007/ws_index_info")
# 指数股票全量数据
total_info_url = config_raw.get('cloud', 'ws_index_url', fallback="http://*************:9000/index_all_info")

punctuation = r"""!"#$%&'()*+,-./:;<=>?@[\]^_`{|}~，。、丶；‘’：“”"""

def upload_file(file_path,storageName,filename,authorization):
    """
    上传文件
    """
    try:
        with open(file_path, 'rb') as file:
            res = requests.post('http://18*************:8077' + "/upload",
                                headers={'authorization': authorization},
                                data={'storageName': storageName,
                                      'filename': filename},
                                files={'file': file})
            result = res.json()
            res.close()
            # 注意，这里的 url 是不包含域名的。/admin/default/2022-10-31/wx.png
            return result['url']
    except:
        return None

def url_host(url):
	url = urllib3.util.parse_url(url)
	return url.host

cloud_host = url_host(API_URL)

clf = ''
paddle_ocr = ''
x_model = ''
TIP_KEYWORDS = ['确定','关闭','取消','继续定位','以后再说','退出','不再提醒','我知道了','知道了','朕知道了']
record_p_map = {}

logcat_map = {}

# TIP_KEYWORDS = ['确定','关闭','取消','继续定位','以后再说','退出','不再提醒','X','我知道了','知道了','朕知道了']

if start_check_pop == 'true':
    import cv2
    import numpy
    from libs.all_config import *
    from libs.image_utils import *
    import selectivesearch.selectivesearch

    img_rows, img_cols = IMG_ROW, IMG_COL


if AI_POLICY=='true':
    import cv2
    import numpy
    from libs.all_config import *
    from libs.image_utils import *
    import selectivesearch.selectivesearch
    from paddleocr import PaddleOCR
    # from sklearn.externals import joblib
    # import joblib
    # clf=joblib.load('./models/classification.pkl')

    # 飞桨
    paddle_ocr = PaddleOCR(use_angle_cls=True, lang='ch')

    img_rows, img_cols = IMG_ROW, IMG_COL

    try:
        os.environ['TF_CPP_MIN_LOG_LEVEL'] = '3'
        import logging
        logging.getLogger("tensorflow").setLevel(logging.ERROR)
        import tensorflow as tf
        tf.autograph.set_verbosity(0)
        from keras.models import load_model
        x_model = load_model(model_name)
        x_model._make_predict_function()

    except Exception as e:
        print(e)

if FIND_IMAGE=='true':
    import cv2
    import numpy as np


request = requests.session()
request.headers['Authorization'] = 'token %s' % API_TOKEN

isWin = platform.system().lower().startswith('win')

def kill_child_processes(parent_pid, sig=signal.SIGTERM):
    """给定父进程id，杀掉该进程的所有子进程"""
    parent = psutil.Process(parent_pid)
    children = parent.children(recursive=True)
    for process in children:
        process.send_signal(sig)


def scrcpy_port(device_id):
    crc32 = zlib.crc32(device_id.encode())
    return (crc32 % 15000) * 4 + 8214


# 检查 ip 类型
def check_ip_type(ip):
    import ipaddress
    try:
        ipaddress.IPv4Address(ip)
        return "IPv4"
    except ipaddress.AddressValueError:
        try:
            ipaddress.IPv6Address(ip)
            return "IPv6"
        except ipaddress.AddressValueError:
            return "Invalid IP"


def encode_image(image_path):
  with open(image_path, "rb") as image_file:
    return base64.b64encode(image_file.read()).decode('utf-8')


def cv2_to_base64(image):
    return base64.b64encode(image).decode('utf8')

def base64_to_cv2(base64_str):
    imgString = base64.b64decode(base64_str)
    nparr = np.fromstring(imgString,np.uint8)  
    image = cv2.imdecode(nparr,cv2.IMREAD_COLOR)
    return image

def paddle_server(filename):
    headers = {"Content-type": "application/json"}
    fo = open(filename, 'rb')
    img_btye = fo.read()
    if img_btye is None:
        print("error in loading image:{}".format(filename))
        return None
    data = {'images': [cv2_to_base64(img_btye)]}
    ai_response = requests.post(url=ai_server_url, headers=headers, data=json.dumps(data))
    fo.close()
    if str(ai_response.status_code) == '200':
        result = ai_response.json()["results"][0]
        return result
    else:
        return None

def _filter_key_logs(logs: str) -> str:
    """过滤关键错误日志"""
    patterns = [
        r"E/AndroidRuntime",
        r"FATAL EXCEPTION",
        r"Process: .*\.financial",
        r"CrashReport:",
        r"SetupPreprocess:",
        r"NetworkError",
        r"u4java_CanvasHelper:",
        r"NullPointerException",
        r"TimeoutException"
    ]
    filtered = []
    for line in logs.split("\n"):
        if any(re.search(p, line) for p in patterns):
            filtered.append(line)
    return "\n".join(filtered[-50:])  # 最多保留50条关键日志

def generate_diagnosis_report(context):
    from http import HTTPStatus
    """调用Qwen生成诊断报告"""
    prompt = f"""你是一名移动金融APP测试专家，请分析以下自动化测试失败原因：

                [界面文字]
                {context['screen_text']}

                [应用日志]
                {context['appium_logs']}

                [系统日志]
                {context['logcat_logs']}

                [用例报错信息]
                {context['error_msg']}

                请按以下结构化格式响应：
                ### 可能原因
                1. (可能性: 高/中/低) 原因描述，需包含具体日志证据
                ...
                """
    response = dashscope.Generation.call(
        model="qwen-max",
        messages=[{
            "role": "user",
            "content": prompt
        }],
        result_format='message'  # 使用消息格式
    )
    
    if response.status_code == HTTPStatus.OK:
        return {
            "report": response.output.choices[0].message.content,
            "usage": response.usage
        }
    else:
        return {"error": f"API调用失败: {response.code} {response.message}"}

def _build_prompt(case_name):
    """优化后的结构化提示词"""
    return f"""## 角色
                你是一个具有经验的移动自动化测试专家：
                
                ## 背景信息
                UI自动化测试执行过程中用例报错时的一张手机屏幕截图。

                ## 输入数据
                1. 屏幕截图
                2. Appium服务日志
                3. 异常堆栈信息
                4. 自动化用例脚本内容
                5. 用例名：{case_name}

                ## 分析要求
                1. 分析设备截图，理解图片界面内容，仅保留3个主要论点，识别页面关键文本，字数200以内
                2. 对当前用例执行错误的可能性分类，包括但不限于一下几种情况：
                - 页面异常弹窗
                - 手机页面锁屏
                - 界面渲染异常
                - 元素定位问题（ID变更、延迟加载）
                - 应用崩溃（ANR/OOM）
                - 业务逻辑问题
                - 环境问题（设备离线、证书过期、设备网络问题）

                ### 输出格式
                以长文本输出200字以内，不要有扩展发散性的建议，输出内容仅包括：
                - 判断：简要说明最大可能的页面问题和导致异常的原因，分类这个异常是可忽略的页面误告还是真实的业务用例错误"""
                # - 思考：根据输入内容和分析要求完成思考路径，忽略logcat日志中无关紧要的异常

def _capture_screenshot(driver):
    """捕获并返回base64编码的截图（适配SDK格式要求）"""
    try:
        screenshot = driver.get_screenshot_as_base64()
        return {"image": f"data:image/png;base64,{screenshot}"}
    except Exception as e:
        logging.error(f"截图失败: {str(e)}")
        return None
            

def analyze_failure(context):
    """使用SDK进行多模态分析"""
    messages = [{
        "role": "user",
        "content": [
            {"text": _build_prompt(context["case_name"])},
            _capture_screenshot(context["driver"]),
            # {"text": f"设备日志：\n{context['logcat_logs']}"},
            {"text": f"Appium日志：\n{context['appium_logs']}"},
            {"text": f"错误日志：\n{str(context['error_msg'])}"}
        ]
    }]

    # 清理空内容并验证格式
    messages[0]["content"] = [
        item for item in messages[0]["content"] 
        if item and (item.get("text") or item.get("image"))
    ]

    try:
        response = dashscope.MultiModalConversation.call(
            model="qwen-vl-plus",
            messages=messages,
            # plugins=self.plugins,  # 通过参数启用插件
            temperature=0.1,
            max_tokens=2000,
            seed=1234,
            # 新增流式输出支持（可选）
            stream=False,
            timeout = 10
        )
        
        if response.status_code == 200:
            return response.output.choices[0].message.content
        else:
            logging.error(f"API错误: {response.code} - {response.message}")
            return f"分析失败：{response.message}"
        
    except dashscope.AuthenticationError as e:
        logging.error("认证失败，请检查API Key")
        return "认证错误"
    except dashscope.RateLimitExceeded as e:
        logging.warning("速率限制触发，建议添加重试逻辑")
        return "请求过于频繁"
    except Exception as e:
        logging.error(f"SDK异常: {str(e)}")
        return "分析服务不可用"


def get_prediction(img_binary, rects):
    """
    predict roi of images, gives two scores for both class
    :param img_binary: image of binary type
    :param rects: rects of proposals
    :return: rects,score list of rects
    """
    rectangles = []
    score_list = []
    img_binary = cv2.cvtColor(img_binary, cv2.COLOR_BGR2GRAY)
    for rect in rects:
        roi = get_roi_image(img_binary, rect)
        roi = cv2.resize(roi, (img_rows, img_cols))
        score = x_model.predict(numpy.asarray([roi], numpy.float32).reshape(1, img_rows, img_cols, 1) / 255)
        # score={}
        if score[0][1] > 0.8:
            # print(score)
            rectangles.append(rect)
            score_list.append(score[0][1])
    return rectangles, score_list

def get_location(res):
    x0 = res[0][0][0]
    y0 = res[0][0][1]

    x_t = (res[0][1][0] - res[0][0][0]) / 2
    y_t = (res[0][3][1] - res[0][0][1]) / 2

    x = x0 + x_t
    y = y0 + y_t
    return x, y

def get_location_online_pd(res):
    x0 = res[0][0]
    y0 = res[0][1]

    x_t = (res[1][0] - res[0][0]) / 2
    y_t = (res[3][1] - res[0][1]) / 2

    x = x0 + x_t
    y = y0 + y_t
    return x, y

def get_content_type(file):
    mime = {
        ".3gp": "video/3gpp",
        ".apk": "application/vnd.android.package-archive",
        ".bmp": "image/bmp",
        ".bz2": "application/x-bzip2",
        ".css": "text/css",
        ".doc": "application/msword",
        ".docx": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        ".dwg": "application/x-autocad",
        ".epub": "application/epub+zip",
        ".gtar": "application/x-gtar",
        ".gz": "application/x-gzip",
        ".htm": "text/html",
        ".html": "text/html",
        ".jar": "application/java-archive",
        ".java": "text/plain",
        ".jpeg": "image/jpeg",
        ".jpg": "image/jpeg",
        ".m3u": "audio/x-mpegurl",
        ".m4a": "audio/mp4a-latm",
        ".mov": "video/quicktime",
        ".mp3": "audio/mpeg",
        ".mp4": "video/mp4",
        ".ogg": "audio/ogg",
        ".pgm": "image/x-portable-graymap",
        ".pict": "image/x-pict",
        ".png": "image/png",
        ".pps": "application/vnd.ms-powerpoint",
        ".ppt": "application/vnd.ms-powerpoint",
        ".pptx": "application/vnd.openxmlformats-officedocument.presentationml.presentation",
        ".pqf": "application/x-cprplayer",
        ".rmvb": "audio/x-pn-realaudio",
        ".svg": "image/svg+xml",
        ".tar": "application/x-tar",
        ".taz": "application/x-tar",
        ".tif": "image/tiff",
        ".tiff": "image/tiff",
        ".tki": "application/x-tkined",
        ".tkined": "application/x-tkined",
        ".toc": "application/toc",
        ".txt": "text/plain",
        ".wav": "audio/x-wav",
        ".wbmp": "image/vnd.wap.wbmp",
        ".wps": "application/vnd.ms-works",
        ".xls": "application/vnd.ms-excel",
        ".xlsx": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        ".zip": "application/zip",
        ".json": "application/json"
    }
    ext = '.' + file.split('.')[-1]
    m = mime.get(ext.lower())
    if m is None:
        return "application/octet-stream"
    return m


# 找图 返回最近似的点
def search_returnPoint(img, template, template_size):
    img_gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    template_ = cv2.cvtColor(template, cv2.COLOR_BGR2GRAY)
    result = cv2.matchTemplate(img_gray, template_, cv2.TM_CCOEFF_NORMED)
    threshold = 0.73
    # res大于70%
    loc = np.where(result >= threshold)
    # 使用灰度图像中的坐标对原始RGB图像进行标记
    point = ()
    for pt in zip(*loc[::-1]):
        cv2.rectangle(img, pt, (pt[0] + template_size[1], pt[1] + + template_size[0]), (7, 249, 151), 2)
        point = pt
    if point == ():
        return None, None, None
    return True, point[0] + template_size[1] / 2, point[1]


def find_element_image(element_image_cv,parent_image_cv):
    """
    element_image 控件小图
    parent_image 初始大图
    """
    scale = 1
    # parent_image_cv = cv2.imread(parent_image)  # 要找的大图
    parent_image_cv = cv2.resize(parent_image_cv, (0, 0), fx=scale, fy=scale)
    # element_image_cv = cv2.imread(element_image)  # 图中的小图
    element_image_cv = cv2.resize(element_image_cv, (0, 0), fx=scale, fy=scale)
    template_size = element_image_cv.shape[:2]
    has_image, x_, y_ = search_returnPoint(parent_image_cv, element_image_cv, template_size)

    if (has_image is None):
        return False,(0, 0)
    return has_image,(x_, y_)


# aircv 方式查找图片
# def find_element_image_aircv():
#     import aircv as ac
#     imsrc = ac.imread('2.png')
#     imobj = ac.imread('22_1.png')

#     confidence=0.7
#     # find the match position
#     pos = ac.find_all_template(imsrc, imobj,confidence)
#     circle_center_pos = pos[0]['result']


def upload_storage(remote, local, tags):
    if not os.path.exists(local):
        return None
    upload_type = caches.get('config.IMAGE_UPLOAD_USE', 'oss')
    type = caches.get('config.S3_IMAGE_BUCKET', 'poc')
    authorization = caches.get('config.AUTHORIZATION',
                               'admin.xcWigjiDhDTaBGRXeJ6ZzZ7qxGEusJi5LtiVQQpBz3mGkv7VUpxptnoLddGEC7g5')

    if upload_type == 's3':
        # upload_file(local, type, remote.split('/')[-2] + "_" + remote.split('/')[-1], authorization)
        # return remote
        bucket_name = caches.get('config.S3_IMAGE_BUCKET')

        # 先取eu本机配置（局域网）
        endpoint = NAS_URL if NAS_URL else caches.get('config.S3_IMAGE_ENDPOINT')
        minioClient = minio.Minio(endpoint,
                                  access_key=caches.get('config.S3_IMAGE_ACCESS_KEY_ID'),
                                  secret_key=caches.get('config.S3_IMAGE_ACCESS_KEY_SECRET'),
                                  secure=False)
        from minio.commonconfig import Tags
        for i in range(3):
            try:
                with open(local, 'rb') as file_data:
                    _tags = Tags(for_object=True)
                    for _k in tags:
                        _tags[_k] = tags.get(_k)
                    file_stat = os.stat(local)
                    # minioClient.put_object(bucket_name, remote, file_data,file_stat.st_size, get_content_type(local), tags=_tags)
                    minioClient.put_object(bucket_name, remote, file_data,file_stat.st_size, get_content_type(local))
                    return '%s/%s' % (caches.get('config.S3_IMAGE_ENDPOINT_VIEW').strip('/'), remote)
            except:
                pass
        return remote

    else:
        oss_image_access_key_id = caches.get('config.OSS_IMAGE_ACCESS_KEY_ID', 'LTAI5t7MSZ6uKgzq1Hny5mXM')
        oss_image_access_key_secret = caches.get('config.OSS_IMAGE_ACCESS_KEY_SECRET', '******************************')
        oss_image_bucket = caches.get('config.OSS_IMAGE_BUCKET', 'zfkj-cps')
        oss_image_endpoint = caches.get('config.OSS_IMAGE_ENDPOINT', 'http://oss-cn-hangzhou.aliyuncs.com')
        oss_image_auth = oss2.Auth(oss_image_access_key_id, oss_image_access_key_secret)
        oss_bucket = oss2.Bucket(oss_image_auth, oss_image_endpoint, oss_image_bucket)

        # 新oss id
        if oss_image_access_key_id=='LTAI5t7MSZ6uKgzq1Hny5mXM':
            remote = type + '/' + remote

        for i in range(3):
            try:
                oss_bucket.put_object_from_file(remote, local)
                oss_image_endpoint_view = caches.get('config.OSS_IMAGE_ENDPOINT_VIEW', 'https://zfkj-cps.oss-cn-hangzhou.aliyuncs.com')
                return '%s/%s' % (oss_image_endpoint_view.strip('/'), remote)
            except:
                pass
        return None


def hide_error(func):
    def f(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            import traceback
            print('自动忽略的错误：', e, traceback.format_exc())
            pass
        return None

    return f


def cloud_api(api, post_data=None, files=None, replay=3, **kwargs):
    try:
        if post_data is None and files is None:
            r = request.get(API_URL + api, params=kwargs, timeout=(3.05, 10), verify=False)
        else:
            r = request.post(API_URL + api, data=post_data, params=kwargs, files=files, timeout=(3.05, 10),verify=False)
        res = r.json()
        return res
    except requests.ConnectionError:
        if replay < 0:
            raise
        time.sleep(1)
        return cloud_api(api, post_data=post_data, files=files, replay=replay - 1, **kwargs)
    except requests.RequestException:
        if replay < 0:
            raise
        time.sleep(1)
        return cloud_api(api, post_data=post_data, files=files, replay=replay - 1, **kwargs)
    except Exception:
        print(api, post_data, kwargs)
        print(r.text)


def register_device(local_device):
    cloud_api('device-reg', {
        'device': local_device.device_id,
        'version': local_device.level,
        'name': local_device.model,
        'eu': EU_ID,
        'eu_adb_port':local_device.eu_adb_port
    })


def clear_log(device, case_id):
    """
    :param device LocalDevice
    :param case_id:
    :return:
    """
    _t = time.strftime('%Y/%m/%d')
    video_path = 'log/video/%s/%s' % (_t, device.device_id.replace(':', ''))
    if not os.path.exists(video_path):
        os.makedirs(video_path)
    if case_id is None:
        log_file = '%s.log' % video_path
    else:
        log_file = '%s/%s.log' % (video_path, case_id)
    log = device.log if device.log else logging.getLogger(device.device_id)

    [log.removeHandler(_) for _ in log.handlers]

    _format = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    handler = logging.FileHandler(log_file, mode='a', encoding='utf-8', delay=False)
    handler.setFormatter(_format)

    log.addHandler(handler)


def cloud_start_case(device, case_id):
    """
    :param device LocalDevice
    :param case_id:
    :return:
    """
    # _t = time.strftime('%Y/%m/%d')
    # video_path = 'log/video/%s/%s' % (_t, device.device_id)
    # if not os.path.exists(video_path):
    #     os.makedirs(video_path)
    # if case_id is None:
    #     log_file = '%s.log' % video_path
    # else:
    #     log_file = '%s/%s.log' % (video_path, case_id)
    # log = device.log if device.log else logging.getLogger(device.device_id)

    # [log.removeHandler(_) for _ in log.handlers]

    # _format = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    # handler = logging.FileHandler(log_file, mode='a', encoding='utf-8', delay=False)
    # handler.setFormatter(_format)

    # log.addHandler(handler)
    if case_id is not None:
        cloud_api('start-job', id=case_id)
    device.find_element_search_map = dict()

def handel_log_text(text):
    import json
    import datetime

    l_list = text.split('\n')
    num = 1
    result = {'data': []}
    for i,j in enumerate(l_list):
        if i >= 1:
            row_list = j.split(' - ')
            if len(row_list) >= 4 and "#save" in row_list[3]:
                time_diff = 0
                if '登录activity切换' in row_list[3]:
                    match = re.search(r'\d+\.\d+', row_list[3])
                    if match:
                        number = match.group(0)
                        time_diff = number
                else:
                    current_time = datetime.datetime.strptime(row_list[0], "%Y-%m-%d %H:%M:%S,%f")
                    last_time = datetime.datetime.strptime(l_list[i-1].split(' - ')[0], "%Y-%m-%d %H:%M:%S,%f")
                    time_diff = (current_time - last_time).total_seconds()

                if '累计' not in row_list[3] and "执行结果" not in row_list[3]:
                    result['data'].append({
                        'step': ''.join(row_list[3].split(' ')[0:2]),
                        'time': time_diff,
                        'num': num
                    })
                    num += 1

    return json.dumps(result)

def cloud_end_case(device, case_id, success, msg,has_success):
    import logging

    _find_element_search_map = device.find_element_search_map
    element_path_last = device.find_element_search_last
    print(_find_element_search_map)
    _list = sorted(_find_element_search_map.items(), key=lambda x: x[1], reverse=True)
    _sum = sum(x[1] for x in _list)
    device.log.info('================ 累计查找控件 %s 次================' % _sum)
    [device.log.info('累计查找控件 <%s> 共 %s 次' % _item) for _item in _list if _item[1] > 2]
    device.find_element_search_map = dict()
    device.find_element_search_last = None

    log = device.log if device.log else logging.getLogger(device.device_id)
    _t = time.strftime('%Y/%m/%d')
    video_path = 'log/video/%s/%s' % (_t, device.device_id.replace(':', ''))
    if case_id is None:
        return
    else:
        log_file = '%s/%s.log' % (video_path, case_id)
        if not os.path.exists(log_file):
            log_file = None
    [log.removeHandler(_) for _ in log.handlers]

    log_msg = str(msg)
    if device.msg_result:
        log_msg = log_msg + '|' + device.msg_result
    if type(success) == str:
        status = success
    elif success is True:
        status = 'success'
    elif success is False:
        status = 'fail'
    else:
        status = 'error'
    # app运行版本
    app_version = device.app_level
    # 核心步骤
    customer = caches.get('config.customer')
    core_steps = None
    if log_file:
        if customer == 'xingye' or customer == 'huatai':
            with open(log_file, 'r', encoding='utf-8') as f:
                text = f.read()
                core_steps = handel_log_text(text)
                f.close()
    if case_id is not None:
        cloud_api('end-job?id=%s' % case_id, {
            'status': status,
            'log_msg': log_msg,
            'sum_element': _sum,
            'last_element': element_path_last,
            'today_has_success' :has_success,
            'cold_start_time' :device.cold_start_time,
            'app_version': app_version,
            'core_steps': core_steps
        }, {'log': open(log_file, 'rb')} if log_file else None)
    device.msg_result = ''
    pass


# @functools.lru_cache(None)
def get_host_ip():
    import socket
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(('***********', 80))
        return s.getsockname()[0]
    except:
        return None


@hide_error
def eu_online():
    from libs.adb import DEVICES_LIST
    result = cloud_api('eu-online', {'devices': ','.join(DEVICES_LIST), 'ip': get_host_ip()}, id=EU_ID)
    _view_element_version = caches.get('config._view_element_version')
    _eu_version = caches.get('config._eu_version')
    if 'config' in result:
        c = result['config']
        for k in c:
            caches.set('config.%s' % k, c[k], 86400)
    if _view_element_version and _view_element_version != caches.get('config._view_element_version'):
        caches.remove('element_path')
    if _eu_version and _eu_version != caches.get('config._eu_version'):
        if 'poc' in API_URL or 'cpssjzq' in API_URL or 'cpsdycyzq' in API_URL:
            for _device in DEVICES_LIST:
                if os.path.exists('env/bin/activate'):
                    _shell = 'source env/bin/activate && adb -s %s shell reboot' % _device
                else:
                    _shell = 'adb -s %s shell reboot' % _device

                os.system(_shell)
                time.sleep(1)
        os.system('(./reload.sh 1>/dev/null 2>&1 &)')


CASES_CRIPT_MAP = {}


def is_screen_on_using_display(device_id):
    # 检查设备是否息屏
    sys_grep = 'findStr' if platform.system().lower().startswith('win') else 'grep'
    result = subprocess.run(['adb', "-s",device_id,'shell', 'dumpsys', 'display', '|', sys_grep, 'mScreenState'], capture_output=True, text=True)
    output = result.stdout
    return 'mScreenState=ON' in output


def is_screen_on_systemui_display(device_id):
    # 检查设备系统系统锁屏
    sys_grep = 'findStr' if platform.system().lower().startswith('win') else 'grep'
    result = subprocess.run(['adb', "-s",device_id,'shell', 'dumpsys', 'window', '|', sys_grep, 'mCurrentFocus=W'], capture_output=True, text=True)
    output = result.stdout
    return 'com.android.systemui' in output and 'NotificationShade' in output


def send_device_keyevent(device_id,key_code):
    result = subprocess.run(['adb', "-s",device_id,'shell', 'input', 'keyevent', key_code], capture_output=True, text=True)
    output = result.stdout
    return output


def casescript(script_name, need_login=False):
    def decoration(func):
        CASES_CRIPT_MAP[script_name] = func, need_login

        return func

    return decoration


class CaseScriptNotExist(Exception):
    def __init__(self, name):
        self.name = name

    def __str__(self):
        return '用例%s不存在或者无法载入'

    pass


class TestCaseFail(RuntimeWarning):
    def __init__(self, msg=None):
        self.msg = msg if msg else '失败'

    def __str__(self):
        return str(self.msg)


class NotEnterError(TestCaseFail):
    def __init__(self, level=None, module=None):
        self.msg = "未进入 %s 级目录：%s" % (level, module)

    def __str__(self):
        return str(self.msg)


class CanNotLogin(TestCaseFail):
    def __init__(self, msg):
        self.msg = str(msg)

    def __str__(self):
        return "登陆失败:%s" % self.msg


class DialogMessageException(Exception):
    def __init__(self, title, message):
        self.title = str(title)
        self.message = str(message)

    def __str__(self):
        return "提示【%s】:%s" % (self.title, self.message)


def _runcasescript(device, name, case_id, func, **kwargs):
    try:
        from libs.driver import ElementNotExist
        _t = time.strftime('%Y/%m/%d')
        video_path = 'log/video/%s/%s' % (_t, device.device_id.replace(':', ''))
        if not os.path.exists(video_path):
            os.makedirs(video_path)
        try:
            cloud_start_case(device, case_id)
            device.log.info('================ %s ===========' % name)

            open_app_version = caches.get('config.OPEN_APP_VERSION', 'true')
            if open_app_version != 'false' and device.app_level:
                device.log.info('当前app版本：' + device.app_level)

            device.case_start({
                'case_id': case_id,
                'name': name,
                'func': func,
                'args': kwargs
            })
            '''
            执行用例
            '''
            success, msg = func(device, **kwargs)
            device.case_finish()
            print('运行结果', success, msg)
            return success, msg
        except TestCaseFail as e:
            print('失败', e)
            # if '||' in str(e):
            #     e = str(e).split('||')[1]
            return False, e
        except ElementNotExist as e:
            print('控件未找到', e)
            return None, e
        except Exception as e:
            import traceback
            print('其他异常', e, traceback.format_exc())
            return None, e
        return success, msg
    except Exception as e:
        success, msg = None, e
        return success, msg


def _upload_device_screencap(images, status, _date, case_id, tags):
    images_urls = []
    for img in images:
        if not os.path.exists(img):
            continue
        f = '%s/%s/%s/%s' % (status, _date, case_id, os.path.basename(img))
        try:
            url = upload_storage(f, img, tags)
            if url:
                images_urls.append(url)
        except:
            pass
        os.unlink(img)
    return images_urls


def get_pid_loop(adb_path, device_id, package_name, stop_event):
    pid = None

    # 每秒获取一次pid，直到获取到为止
    while not pid and not stop_event.is_set():
        pid = performance.getPid(adb_path, device_id, package_name)
        print('当前pid'+str(pid))
        if not pid:
            time.sleep(1)

    myBuffer = []
    next_call = time.monotonic()

    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)

    # 如果获取到pid，每秒调用一次get_app_info
    while pid and not stop_event.is_set():
        start_time = time.monotonic()
        # results = asyncio.run(performance.get_app_info(adb_path, device_id, pid))
        results = loop.run_until_complete(performance.get_app_info(adb_path, device_id, pid))
        # print('当前时间戳:' + str(int(time.time())*1000))
        if results:
            myBuffer.append(results)

        next_call = next_call + 1
        sleep_time = next_call - time.monotonic()
        if sleep_time > 0:
            time.sleep(sleep_time)
        else:
            next_call = time.monotonic()
    loop.close()
    buffer[device_id] = myBuffer
    # print(str(buffer[device_id]))

def runcasescript(app, device, name, has_success, case_id=None, **kwargs):
    try:
        if name not in CASES_CRIPT_MAP:
            print('不存在此用例')
            msg = CaseScriptNotExist(name)
            success = 'cancel'
        else:
            device.app = app
            func, need_login = CASES_CRIPT_MAP[name]
            print('获取用例')
            re = define_get_callback('before_case')(device, name, case_id, func)
            print('预准备', re)
            # device.log.info('================用例准备完成===========')
            if re is None:
                if case_id:
                    device.screenrecord_start(case_id)
                    # device.has_element('/录屏/录制时显示敏感信息/立即开始')()
                
                clear_log(device,case_id)

                if device.brand and  device.brand == 'vivo':
                    # 处理vivo S18 锁屏后解锁操作
                    if not is_screen_on_using_display(device.device_id):
                        device.log.info('检测到设备息屏，电源键唤醒屏幕')
                        send_device_keyevent(device.device_id,'KEYCODE_POWER')
                        time.sleep(1)
                        device.shell('input swipe 500 1500 500 260')
                        device.shell('input tap 545 2250')
                        device.shell('input swipe 500 1500 500 260')
                    
                    # 处理vivo S18 com.android.systemui 解锁操作
                    if is_screen_on_systemui_display(device.device_id):
                        device.log.info('检测到系统锁屏，解锁屏幕')
                        device.shell('input swipe 500 1500 500 260')
                        device.shell('input tap 545 2250')
                        device.shell('input swipe 500 1500 500 260')

                if logcat == 'true':
                    device.clear_logcat()
                
                if not device.is_appium_alive():
                    _ = device.appium.driver
                    if 'cpshtzq' not in API_URL:
                        time.sleep(2)                

                # 出来邮储POC数据采集问题
                if perf_log == 'true':
                    adb_path = ADB_PATH
                    stop_event = threading.Event()
                     # 创建并启动后台线程来获取 PID，每秒调用一次，并且不影响其他代码运行，直到获取到pid为止
                    pid_thread = threading.Thread(target=get_pid_loop, args=(adb_path, device.device_id, app['package_name'], stop_event))
                    pid_thread.start()
                    # pid_thread.daemon = True  # 设置为守护线程，这样主程序结束时，这个线程也会结束
                        
                    # 标签,标志,进程号,当前时间,url,开始时间,延迟,值,execut_id,case_id,case_name,app_id
                    _log = 'moirai,00,,'+ str(int(time.time()*1000)) +',url,'+ str(int(time.time()*1000))+',,,' + device.running_case_id + ',' + case_id + ','+ name + ','+app['package_name']
                    # _log1 = 'cpsLogBegin,'+ name
                    _shell = 'log -t moirai -p w ' + _log
                    # _shell = 'log -t zyf -p w ' + _log1
                    device.shell(_shell)
                    time.sleep(2)
                    

                if APP_RESTART == 'false':
                    state = device.appium.driver.query_app_state(app['package_name'])
                    if state == 4:
                        device.log.info('App已在前台运行')
                    elif state == 3:
                        device.log.info('App在后台运行，切换至前台')
                        device.appium.driver.activate_app(app['package_name'])
                    else:
                        device.log.info('App状态异常')                    

                if need_login:
                    print('需要登陆')
                    define_get_callback('login')(device, case_id)
                    pass
                args = {}
                for k in func.__code__.co_varnames[1: func.__code__.co_argcount]:
                    if k in kwargs:
                        args[k] = kwargs[k]
                
                if start_check_pop == 'true':
                    popup_window = device.check_pop()

                    filename = device.device_id + str(int((time.time() * 10000))) + ".png"
                    device.appium.driver.save_screenshot(filename)
                    
                    if popup_window:
                        device.log.info('检测到异常弹窗')
                        device.screencap()

                        img = cv2.imread(filename)
                        scale = 500 / img.shape[1]
                        img = cv2.resize(img, (0, 0), fx=scale, fy=scale)
                        gray, binary = get_binary_image(img)
                        try:
                            mg_lbl, regions = selectivesearch.selective_search(binary, SCALE, SIGMA, MIN_SIZE)
                            regions = get_proposal(regions, img.shape)
                            rectangles, score_list = get_prediction(binary, regions)
                            if len(score_list) > 0:
                                score = round(max(score_list), 2)
                                rect = rectangles[score_list.index(max(score_list))]
                                position = get_pos(rect, scale)
                                x = position[0]
                                y = position[1]
                                device.log.info('自动识别关闭按钮,坐标(%s,%s)' % (x, y))
                                is_deal = True
                                device.appium.driver.tap([(x, y)])
                        except Exception as e:
                            device.log.info(e)


                re = _runcasescript(device, name, case_id, func, **args)
                print('结束执行', re)
            success, msg = re
    except Exception as e:
        print('异常', e)
        success, msg = None, e

    if success is not True:
        # 截屏
        device.screencap()

    if msg is None:
        msg = '无结果'
    

    if success is False and logcat == 'true' and LLM == 'true':
        if device.check_pop():
            device.log.info('检测到异常弹窗')
        else:
            print('----------------------------------------------------------------')
            dashscope.api_key = 'sk-f834ac0e6d15468b8e94c4af94c02115'
            filename = device.device_id + str(int((time.time() * 10000))) + ".png"
            device.appium.driver.save_screenshot(filename)
            result = paddle_server(filename)
            if os.path.exists(filename):
                os.remove(filename)

            appium_logs = device.appium.driver.get_log('logcat')[-50:]

            logcat_logs = subprocess.check_output(
                    ["adb", "-s", device.device_id,"logcat", "-d", "*:E", "moirai:V"],
                    stderr=subprocess.STDOUT
                ).decode("utf-8", errors="ignore")

            context = {
                "case_name":name,
                "driver": device.appium.driver,
                "logcat_logs": _filter_key_logs(logcat_logs),
                "appium_logs": _filter_key_logs("\n".join([log["message"] for log in appium_logs])),
                "error_msg": msg
            }
            report = analyze_failure(context)
            if type(report) == list:
                device.log.info(report[0]['text'])
            
    if 'cpscjzq' in API_URL and ( success is not True ) and device.check_pop():
        device.log.info('检测到异常弹窗')
        global ai_times
        try:
            if ai_times < max_ai_times:
                filename = device.device_id + str(int((time.time() * 10000))) + ".png"
                device.appium.driver.save_screenshot(filename)
                
                base64_image = encode_image(filename)

                response = doubao_client.chat.completions.create(

                model=doubao_model,
                messages=[
                    {
                    "role": "user",
                    "content": [
                        {
                        "type": "image_url",
                        "image_url": {
                        # 需要注意：传入Base64编码前需要增加前缀 data:image/{图片格式};base64,{Base64编码}：
                        "url":  f"data:image/png;base64,{base64_image}"
                        # JPEG图片："url":  f"data:image/jpeg;base64,{base64_image}"
                        # WEBP图片："url":  f"data:image/webp;base64,{base64_image}"
                        # "url":  f"data:image/<IMAGE_FORMAT>;base64,{base64_image}"
                        },         
                        },
                        {
                        "type": "text",
                        "text": "这是手机app的界面截屏，你需要帮我识别界面上是不是出现了广告弹窗，只需要回答我是还是不是",
                        },
                    ],
                    }
                ],
                )
                if response and len(response.choices) > 0:
                    print(response.choices[0].message)
                    _llm_content = response.choices[0].message.content
                    device.log.info('智能判断是否是广告弹窗：' + _llm_content)
                    if _llm_content == '是':
                        success = None
                        msg = "检测到广告弹窗"
                ai_times +=1

        except Exception as e:
            device.log.info(e)
        
    
    if 'cpshtzq' in API_URL and  device.check_pop():
        device.log.info('检测到异常弹窗')
        device.screencap()
        if success is False and device.has_element('/提示信息/消息内容', 1):
            msg = str(device.has_element('/提示信息/消息内容'))
            device.log.info('弹出提示消息：%s' % str(msg))
            error_list = ['因数据系统升级维护, 该功能使用将受到影响', '超过融券负债可还数量', '因后台数据清算维护, 查到的数据可能不准确', '不足']
            if sum(err in msg for err in error_list) > 0:
                success = None
    
    # if success is False and device.has_element('/应用提示/网络不通', 1):
    #     msg = '【应用网络异常】 请检查手机网络或重新加载'
    #     success = None
        
    if app['package_name']=='com.lphtsccft':
        # if success is False and device.has_element('/系统/通知栏/编辑按钮', 1):
        #     device.appium.driver.back()
        #     msg = '【通知栏异常】%s' % str(msg)
        #     success = None
        # if success is False and device.has_element('/系统/桌面布局', 1):
        #     device.appium.driver.back()
        #     msg = '【系统弹框异常】%s' % str(msg)
        #     success = None

        # if success is False and device.has_element('/权限通知/提示信息', 1) and device.has_element('/权限通知/始终允许', 1):
        #     try:
        #         device.has_element('/权限通知/始终允许')()
        #     except:
        #         pass
        #     msg = '【系统弹框异常】%s' % str(msg)
        #     success = None

        if success is False and not device.has_element('/系统/涨乐主框架', 2) and not name.startswith('移动端'):
            
            try:
                _curren_app_str = device.shell('dumpsys window | grep mCurrentFocus')
                if 'com.lphtsccft' not in _curren_app_str and 'com.huawei.systemmanager' not in _curren_app_str:
                    msg = '【应用闪退】%s' % str(msg)

                    _sh = 'adb -s ' + device.device_id + ' shell dumpsys window | grep mCurrentFocus=W'
                    p = subprocess.Popen(_sh, shell=True, stdout=subprocess.PIPE)
                    lines = p.stdout.readlines()
                    for line in lines:
                        _l = line.strip().decode()
                        if 'Not Responding' in _l:
                            device.shell('reboot')
                            msg = '【异常错误】%s' % str(msg)
                            success = None
                            break
                else:
                    msg = '【异常错误】%s' % str(msg)
                    success = None
                
                # _curren_app_str = device.shell('dumpsys window | grep mCurrentFocus')
                # if 'com.lphtsccft' not in _curren_app_str:
                #     msg = '【应用闪退】%s' % str(msg)
                #     success = None
            
            except Exception as e:
                device.log.info(e)
                msg = '【应用闪退】%s' % str(msg)
                success = None

    # 录屏、应用未响应处理
    t_package_name = caches.get('config.auto_restart_package_name', 'false')
    if device.app['package_name'] in t_package_name:
        package_name = device.app['package_name']
        element_path = f'//android.widget.FrameLayout[@package="{package_name}"]'
        try:
            if success is not True and not device.has_element(element_path, 2):
                _sh = 'adb -s ' + device.device_id + ' shell dumpsys window | grep mCurrentFocus=W'
                p = subprocess.Popen(_sh, shell=True, stdout=subprocess.PIPE)
                lines = p.stdout.readlines()
                for line in lines:
                    _l = line.strip().decode()
                    if 'Not Responding' in _l:
                        device.shell('reboot')
                        msg = '【未响应异常】%s' % str(msg)
                        success = None
                        break
        except:
            device.log.info('continue')

    # 方正
    if app['package_name'] == 'com.foundersc.app.xf':
        if success is False and not device.has_element('/系统/方正证券/主框架', 2):
            try:
                _curren_app_str = device.shell('dumpsys window | grep mCurrentFocus')
                if 'com.foundersc.app.xf' not in _curren_app_str and 'com.huawei.systemmanager' not in _curren_app_str and 'com.hihonor.mms' not in _curren_app_str:
                    msg = '【应用】%s' % str(msg)
                    # if 'com.hihonor.android.launcher' in _curren_app_str:
                    device.shell('reboot')
                    msg = '【异常处理】%s' % str(msg)
                    success = None
                else:
                    msg = '【异常错误】%s' % str(msg)
                    success = None
            except Exception as e:
                device.log.info(e)
                msg = '【应用闪退】%s' % str(msg)
                success = None

    # # 华安忽略告警内容
    # if app['package_name'] == 'com.hazq.huiying.android' and device.app['deviceName'] == 'AXMN6R2A13003636':
    #     if success is False:
    #         try:
    #             error_list = ['系统升级维护', '超过融券负债可还数量', '柜台请求异常', '不足', '当前时间不允许']
    #             device.log.info('转异常报错内容：%s' % str(msg))
    #             if sum(err in msg for err in error_list) > 0:
    #                 success = None
    #         except:
    #             up_base_ui_message = ''
    #             try:
    #                 up_base_ui_message = str(device.has_element('/华安提示弹窗/消息内容'))
    #                 device.log.info('弹出提示消息：%s' % str(up_base_ui_message))
    #                 error_list = ['系统升级维护', '超过融券负债可还数量', '柜台请求异常', '不足', '当前时间不允许']
    #                 if sum(err in up_base_ui_message for err in error_list) > 0:
    #                     msg = '【' + up_base_ui_message + '】' + '+' + msg
    #                     success = None
    #             except:
    #                 pass

    if app['package_name']=='com.firstcapital':
        if success is False and not device.has_element('/系统/一创智富通主框架', 2):
            try:
                if success is False:
                    _curren_app_str = device.shell('dumpsys window | grep mCurrentFocus=W')
                    if 'com.firstcapital' not in _curren_app_str and 'Not Responding' in _curren_app_str:
                        device.shell('reboot')
                        msg = '【异常错误】%s' % str('设备程序异常')
                        success = None
                    else:
                        msg = '【应用闪退】%s' % str(msg)
                        success = None
            except Exception as e:
                device.log.info(e)
                msg = '【应用闪退】%s' % str(msg)
                success = None

    if success is False and device.has_element('/系统提示/USB连接方式', 1):
        try:
            device.has_element('/系统提示/取消')()
        except:
            pass
        msg = '【系统弹框异常】%s' % str(msg)
        success = None


    # e海通财站点截图20220420
    if app['package_name'] == 'com.android.haitong':
        if name.startswith('e海通财/281-e海通财交易中台') or ('e海通财/326-新一代核心交易系统（hots）'):
            if success is False:
                # 返回我的
                home = None
                home_element = '/e海通财/我的界面'
                if device.has_element('/e海通财/确定按钮'):
                    device.has_element('/e海通财/确定按钮')()
                if device.has_element('/e海通财/提示/退出按钮'):
                    device.has_element('/e海通财/提示/退出按钮')()
                # for _ in range(9):
                #     if home:
                #         break
                #     home = device.has_element(home_element, 2)
                #     if home:
                #         device.has_element(home_element)()
                #     else:
                #         device.appium.back()
                #         device.log.info('返回上一级页面')
                # if not home:
                #     raise Exception()
                quit = None
                quit_element = '/e海通财/退出e海通财'
                for _ in range(9):
                    quit = device.has_element(quit_element, 2)
                    if quit:
                        break
                    else:
                        device.appium.back()
                        device.log.info('返回上一级页面')

                if not quit:
                    raise Exception()
                else:
                    quit()
                    if device.has_element(home_element):
                        device.has_element(home_element)()

                # e海通财 错误后的站点截屏用例20220420
                if name.startswith('e海通财/281-e海通财交易中台') or ('e海通财/326-新一代核心交易系统（hots）'):
                    device.appium.slide_up()
                    if device.has_element('/e海通财/我的界面/系统设置'):
                        device.has_element('/e海通财/我的界面/系统设置')()
                    time.sleep(3)
                    device.screencap()

    # e海通财至慧版
    if app=='com.android.ehaitongcai':
        if success is False and not device.has_element('/系统/e海通财至慧版/主框架', 2):
            msg = '【应用闪退】%s' % str(msg)
            success = None

        # e海通财至慧版 失败后站点截屏 20220302
        if success is False:
            if not device.has_element('/e海通财至慧版/通用/搜索按钮'):
                while not device.has_element('/e海通财至慧版/行情界面'):
                    device.appium.back()
                else:
                    device.has_element('/e海通财至慧版/行情界面')()
                    device.has_element('/e海通财至慧版/通用/搜索按钮')()
            else:
                device.has_element('/e海通财至慧版/通用/搜索按钮')()

            device['/e海通财至慧版/搜索页/文本框'] = 'max911'
            time.sleep(2)


    # 机构客户服务系统
    if app=='com.htsec.icspandroid':
        if success is False and not device.has_element('/系统/机构客户服务系统/主框架', 2):
            msg = '【应用闪退】%s' % str(msg)
            success = None
            
    # 安信安卓端生产
    if app['package_name']=='cn.com.essence.stock':
        if success is False and name.startswith('国投手机证券/安卓'):
            # 返回我的
            home = None
            home_element = '/安信手机证券/安卓/我的'
            for _ in range(9):
                if home:
                    break
                home = device.has_element(home_element, 2)
                if home:
                    device.has_element(home_element)()
                else:
                    device.appium.back()
                    device.log.info('返回上一级页面')
            if not home:
                raise Exception()
            # 安信 7.0错误后的站点截屏用例
            if name.startswith('国投手机证券/安卓'):
                device.has_element('/安信手机证券/安卓/我的/设置')()
                device.has_element('/安信手机证券/安卓/我的/设置/服务器设置')()
                time.sleep(3)
                device.screencap()

    # 安信安卓端测试
    if app['package_name']=='cn.com.essence.stock':
        if success is False and name.startswith('安信手机证券/测试'):
            # 返回我的
            home = None
            home_element = '/安信手机证券/安卓/我的'
            for _ in range(9):
                if home:
                    break
                home = device.has_element(home_element, 2)
                if home:
                    device.has_element(home_element)()
                else:
                    device.appium.back()
                    device.log.info('返回上一级页面')
            if not home:
                raise Exception()
            # 安信 7.0错误后的站点截屏用例
            if name.startswith('安信手机证券/测试'):
                device.has_element('/安信手机证券/安卓/我的/设置')()
                device.has_element('/安信手机证券/安卓/我的/设置/服务器设置')()
                time.sleep(3)
                device.screencap()

    # 首创番茄财富4.0站点截图
    if app['package_name'] == 'com.thinkive.android.shouchuang_invest':
        if success is False:
            # 返回我的
            home = None
            home_element = '/首创番茄财富/4.0/我的界面'
            for _ in range(9):
                if home:
                    break
                home = device.has_element(home_element, 2)
                if home:
                    device.has_element(home_element)()
                else:
                    device.appium.back()
                    device.log.info('返回上一级页面')
            if not home:
                raise Exception()
            # 首创番茄财富失败站点截图用例
            if name.startswith('首创番茄财富'):
                device.has_element('/首创番茄财富/4.0/我的/设置按钮')()
                device.has_element('/首创番茄财富/4.0/我的/系统设置/切换服务器')()
                device.has_element('/首创番茄财富/4.0/我的/系统设置/切换服务器/行情站点')()
                time.sleep(3)
                device.screencap()

    # 浙商汇金谷9.0站点截图
    if app['package_name'] == 'com.hexin.plat.android.ZheshangSecurity':
        if success is False:
            # 返回我的
            home = None
            home_element = '/浙商汇金谷/9.0/我的界面'
            for _ in range(9):
                if home:
                    break
                home = device.has_element(home_element, 2)
                if home:
                    device.has_element(home_element)()
                else:
                    device.appium.back()
                    device.log.info('返回上一级页面')
            if not home:
                raise Exception()
            # 浙商汇金谷失败站点截图用例
            if name.startswith('浙商汇金谷'):
                device.has_element('/浙商汇金谷/9.0/我的/设置按钮')()
                device.has_element('/浙商汇金谷/9.0/我的/设置按钮/切换服务器')()
                device.has_element('/浙商汇金谷/9.0/我的/设置按钮/切换服务器/行情站点服务器切换')()
                time.sleep(3)
                device.screencap()

    # 中原财升宝9.0站点截图
    if app['package_name']=='com.hexin.plat.android.ZhongyuanSecurity':
        if success is False:
            # 返回我的
            home = None
            home_element = '/中原证券财升宝/9.0/我的界面'
            for _ in range(9):
                if home:
                    break
                home = device.has_element(home_element, 2)
                if home:
                    device.has_element(home_element)()
                else:
                    device.appium.back()
                    device.log.info('返回上一级页面')
            if not home:
                raise Exception()
            # 中原财升宝9.0失败站点截图用例
            if name.startswith('中原证券财升宝'):
                device.has_element('/中原证券财升宝/9.0/我的/设置')()
                device.has_element('/中原证券财升宝/9.0/我的/设置/站点选择')()
                time.sleep(3)
                device.screencap()

    # 华龙证券站点截图
    # 财富版
    if app['package_name']=='com.thinkive.android.hualong':
        if success is False:
            # 返回首页
            home = None
            home_element = '/华龙点金财富版/首页'
            for _ in range(9):
                if home:
                    break
                home = device.has_element(home_element, 2)
                if home:
                    device.has_element(home_element)()
                else:
                    device.appium.back()
                    device.log.info('返回上一级页面')
            if not home:
                raise Exception()
            # 华龙点金财富版失败站点截图用例
            if name.startswith('华龙点金财富版'):
                device.has_element('/华龙点金财富榜/首页/设置')()
                device.has_element('/华龙点金财富榜/首页/设置/站点优选')()
                device.has_element('/华龙点金财富榜/首页/设置/站点优选/一键测速')()
                time.sleep(3)
                device.screencap()
    # 智慧版
    if app['package_name']=='hualong.szkingdom.android.newphone':
        if success is False:
            # 返回首页
            home = None
            home_element = '/华龙点金智慧版/首页'
            for _ in range(9):
                if home:
                    break
                home = device.has_element(home_element, 2)
                if home:
                    device.has_element(home_element)()
                else:
                    device.appium.back()
                    device.log.info('返回上一级页面')
            if not home:
                raise Exception()
            # 华龙点金智慧版失败站点截图用例
            if name.startswith('华龙点金智慧版'):
                device.has_element('/华龙点金智慧版/首页/个人主页')()
                device.has_element('/华龙点金智慧版/首页/个人主页/系统设置')()
                device.has_element('/华龙点金智慧版/首页/个人主页/系统设置/交易站点')()
                time.sleep(3)
                device.screencap()
                time.sleep(2)
                device.appium.back()
                device.has_element('/华龙点金智慧版/首页/个人主页/系统设置/行情站点')()
                time.sleep(3)
                device.screencap()

    # 华安证券站点截图
    if app['package_name']=='com.hazq.huiying.android':
        if success is False:
            # 返回我的
            home = None
            home_element = '/华安证券/我的跳转按钮'
            for _ in range(9):
                if home:
                    break
                home = device.has_element(home_element, 2)
                if home:
                    device.has_element(home_element)()
                else:
                    device.appium.back()
                    device.log.info('返回上一级页面')
            if not home:
                raise Exception()
            # 华龙点金智慧版失败站点截图用例
            if name.startswith('华安证券'):
                device.has_element('/华安证券/我的跳转按钮/设置')()
                device.has_element('/华安证券/我的跳转按钮/设置/交易站点')()
                time.sleep(3)
                device.screencap()
                time.sleep(2)
                device.appium.back()
                device.has_element('/华安证券/我的跳转按钮/设置/行情站点')()
                time.sleep(3)
                device.screencap()

    # 东北同花顺 应用闪退处理
    if app['package_name'] == 'com.hexin.plat.android.DongBeiSecurity':
        if success is False:
            # grep top-activity
            _curren_app_str = device.shell('dumpsys window | grep mCurrentFocus=W')
            device.log.info(_curren_app_str)
            if 'com.hexin.plat.android.DongBeiSecurity' not in _curren_app_str :
                msg = '【应用闪退】%s' % str(msg)
                device.log.info('捕获应用闪退')


    # 长江证券 应用闪退处理
    if app['package_name'] == 'com.eno.android.cj.page':
        if success is False:
            # grep top-activity
            _curren_app_str = device.shell('dumpsys window | grep mCurrentFocus=W')
            device.log.info(_curren_app_str)
            if 'com.eno.android.cj.page' not in _curren_app_str :
                msg = '【应用闪退】%s' % str(msg)
                device.log.info('捕获应用闪退')


    # 东北证券站点截图
    if app['package_name']=='com.dbscgeneral':
        # if success is False:
        # 返回我的
        home = None
        home_element = '/东北证券/我的跳转界面'
        for _ in range(9):
            if home:
                break
            home = device.has_element(home_element, 2)
            if home:
                device.has_element(home_element)()
            else:
                device.appium.back()
                device.log.info('返回上一级页面')
        if not home:
            raise Exception()
        # 东北证券失败站点截图用例
        if name.startswith('东北证券融e通') or name.startswith('APP-融e通'):
            device.has_element('/东北证券/我的/系统设置')()
            device.has_element('/东北证券/我的/系统设置/服务器设置')()
            # time.sleep(2)
            # device.screencap()

            value_market = ''
            _ele_market = device.has_element('/东北证券/我的/系统设置/服务器设置/行情服务器', 1)
            if _ele_market:
                _value_market = str(_ele_market)
                if _value_market.strip() and _value_market != '--':
                    value_market = _value_market.strip()
                    
            value_trade = ''
            _ele_trade = device.has_element('/东北证券/我的/系统设置/服务器设置/交易服务器', 1)
            if _ele_trade:
                _value_trade = str(_ele_trade)
                if _value_trade.strip() and _value_trade != '--':
                    value_trade = _value_trade.strip()

            value_information = ''
            _ele_information = device.has_element('/东北证券/我的/系统设置/服务器设置/资讯服务器', 1)
            if _ele_information:
                _value_information = str(_ele_information)
                if _value_information.strip() and _value_information != '--':
                    value_information = _value_information.strip()

            _res = '行情:' + value_market + '交易:' + value_trade + '资讯:' + value_information
            device.log.info(_res)
            device.add_msg_result(_res)


    # 国金佣金宝站点截图
    if app['package_name'] == 'cn.com.gjzq.yjb2':
        if success is False:
            # 返回我的
            home = None
            home_element = '/国金佣金宝/我的'
            for _ in range(9):
                if home:
                    break
                home = device.has_element(home_element, 2)
                if home:
                    device.has_element(home_element)()
                    time.sleep(2)
                    device.appium.driver.tap([(1006, 184)])
                    time.sleep(2)
                    device.appium.driver.tap([(500, 1610)])
                    time.sleep(2)
                    # device.has_element('/国金佣金宝/我的/系统设置/服务器设置')
                    # device.screencap()

                    _ele_market = (str(device.has_element('/国金佣金宝/我的/设置/服务器/业务站点'))).strip()
                    device.log.info(f"sds:{_ele_market}")
                    _res = '业务站点:' +  _ele_market
                    # 日志输出
                    device.log.info(_res)
                    device.add_msg_result(_res)

                else:
                    device.appium.back()
                    device.log.info('返回上一级页面')
            if not home:
                raise Exception()
            # 国金佣金宝失败站点截图用例
            # if name.startswith('国金佣金宝/'):
            #     device.appium.driver.tap([(1006, 184)])
            #     device.has_element('/国金佣金宝/我的/系统设置/服务器设置')
            #     device.screencap()

    if app['package_name'] == 'com.hexin.plat.android.DongBeiSecurity':
        if success is False:
            # 返回首页
            home = None
            home_element = '/东北同花顺/首页'
            for _ in range(9):
                if home:
                    break
                home = device.has_element(home_element, 2)
                if home:
                    device.has_element(home_element)()
                else:
                    device.appium.back()
                    device.log.info('返回上一级页面')
            if not home:
                raise Exception()

            if name.startswith('东北同花顺'):
                device.has_element('/东北同花顺/首页/个人中心')()
                device.has_element('/东北同花顺/首页/个人中心/设置')()
                device.has_element('/东北同花顺/首页/个人中心/设置/切换服务器')()
                device.has_element('/东北同花顺/首页/个人中心/设置/切换服务器/行情站点服务器切换')()
                # time.sleep(2)
                # device.screencap()

                value_information = ''
                _ele_information = device.has_element('/东北同花顺/首页/个人中心/设置/切换服务器/行情站点服务器切换/行情已选站点', 1)
                time.sleep(2)
                device.screencap()
                if _ele_information:
                    _value_information = str(_ele_information)
                    if _value_information.strip() and _value_information != '--':
                        value_information = _value_information.strip()

                _res = '行情站点：' + value_information

                device.appium.back()
                device.log.info('返回上一级页面')

                device.has_element('/东北同花顺/首页/个人中心/设置/切换服务器/增强交易站点服务器切换')()
                # time.sleep(2)
                # device.screencap()
                # value_information = ''
                _ele_information = device.has_element(
                    '/东北同花顺/首页/个人中心/设置/切换服务器/增强交易站点服务器切换/交易已选站点', 1)
                if _ele_information:
                    _value_information = str(_ele_information)
                    if _value_information.strip() and _value_information != '--':
                        value_information = _value_information.strip()

                _res += '  交易站点：' + value_information

                device.log.info(_res)
                device.add_msg_result(_res)

    # 东北-公版同花顺
    if app['package_name'] == 'com.hexin.plat.android':
        if success is False:
            # 返回首页
            home = None
            home_element = '/公版同花顺/首页'
            for _ in range(9):
                if home:
                    break
                home = device.has_element(home_element, 2)
                if home:
                    device.has_element(home_element)()
                else:
                    device.appium.back()
                    device.log.info('返回上一级页面')
            if not home:
                raise Exception()

            if name.startswith('公版同花顺'):
                device.has_element('/公版同花顺/首页/个人中心')()
                device.has_element('/公版同花顺/首页/个人中心/设置')()
                device.has_element('/公版同花顺/首页/个人中心/设置/切换服务器')()
                # time.sleep(2)
                # device.screencap()

                value_information = ''
                _ele_information = device.has_element(
                    '/公版同花顺/首页/个人中心/设置/切换服务器/选择服务器', 1)
                if _ele_information:
                    _value_information = str(_ele_information)
                    if _value_information.strip() and _value_information != '--':
                        value_information = _value_information.strip()

                _res = '当前服务器：' + value_information
                device.log.info(_res)
                device.add_msg_result(_res)

    # 上海的站点截图
    if app['package_name'] == 'com.shsc.gen':
        # if success is False:
        # 返回我的
        home = None
        home_element = '/上海证券指e通/我的'
        for _ in range(9):
            if home:
                break
            home = device.has_element(home_element, 2)
            if home:
                device.has_element(home_element)()
            else:
                device.appium.back()
                device.log.info('返回上一级页面')
        if not home:
            raise Exception()

        if name.startswith('上海证券指e通'):
            device.has_element('/上海证券指e通/我的')()
            device.has_element('/上海证券指e通/我的/设置')()
            device.has_element('/上海证券指e通/我的/设置/服务器设置')()
            time.sleep(2)
            device.screencap()

    if not isWin:
        if success is False and not device.network_check():
            if '国投手机证券/演练大屏' not in name:
                success = None
            msg = '【网络异常】%s' % str(msg)

    if app['package_name']=='com.lphtsccft':
        try:
            # 191030  新增  用例失败后查询所在服务器
            # 应用闪退错误时，不执行查询服务器
            if success is False and not name.startswith('移动端') and ('应用闪退' not in msg):
                # 返回首页
                home = None
                home_element = '/底部导航栏/我的界面跳转按钮'
                if name.startswith('涨乐财富通/'):
                    home_element = '/底部导航栏/交易界面跳转按钮'
                for _ in range(9):
                    if home:
                        break
                    home = device.has_element(home_element, 2)
                    if home:
                        device.has_element(home_element)()
                    else:
                        device.appium.back()
                        device.log.info('返回上一级页面')
                if not home:
                    raise Exception()
                # 20220121 8.0的错误后的站点截屏用例
                if name.startswith('涨乐财富通/'):
                    device.has_element('/涨乐财富通/8.0/导航栏按钮')()
                    device.has_element('/涨乐财富通/8.0/导航栏/系统设置')()
                    device.has_element('/涨乐财富通/8.0/导航栏/系统设置/服务器设置')()
                    time.sleep(3)
                    device.screencap()
                else:
                    if device.has_element('/我的/返回'):
                        device.has_element('/我的/返回')()

                    if device.has_element('/我的/设置按钮', 2) == '':
                        device.appium.slide_down()

                    device.has_element('/我的/设置按钮')()
                    device.has_element('/我的/我的交易账户/设置/服务器设置')()
                    # device.log.info('name is %s' % name)
                    address = ''
                    # if name.startswith('行情'):
                    address = device.has_element('/我的/设置/服务器设置/行情服务器地址')
                    device.log.info('行情服务器地址：%s' % address)
                    # elif name.startswith('交易'):
                    address = device.has_element('/我的/设置/服务器设置/交易服务器地址')
                    device.log.info('交易服务器地址：%s' % address)
                    # else:
                    address = device.has_element('/我的/设置/服务器设置/资讯服务器地址')
                    device.log.info('资讯服务器地址：%s' % address)

        except:
            device.log.info('服务器查询失败')

    if perf_log == 'true' :
        import datetime
        stop_event.set()
        pid_thread.join()
        base_path = "/home/<USER>/src/ControlSystem/log/performance"
        current_time = datetime.datetime.now()
        save_path = os.path.join(
            base_path,
            current_time.strftime("%Y"),
            current_time.strftime("%m"),
            current_time.strftime("%d"),
            device.device_id
        )
        os.makedirs(save_path, exist_ok=True)

        filename = f"{case_id}.txt"
        full_path = os.path.join(save_path, filename)

        resultJson = {}
        performanceJson = []
        caseBase = {
            'device_id': device.device_id,
            'app_id': app['package_name'],
            'case_id': device.running_case_id,
            'execute_id': case_id
        }

        
        performanceJson = []  
        if device.device_id in buffer:
            performanceJson = buffer[device.device_id]

        resultJson['performance'] = performanceJson
        resultJson['base_info'] = caseBase

        # 将 resultJson 写入文件
        with open(full_path, 'w') as f:
            json.dump(resultJson, f, indent=4)
        
        # 将 resultJson 调接口上报
        url = "http://121.41.122.204:9080/api/data/collector/performance"
        params = {
            "message": str(resultJson)
        }
        # 发送POST请求
        response = requests.post(url, data=params)
        # 检查响应状态码
        if response.status_code == 200:
            # 请求成功
            # result = response.json()
            print('数据上报成功')
        else:
            # 请求失败
            print("Error:", response.status_code, response.text)
        # buffer.clear()
        del buffer[device.device_id]
        
        _log = 'moirai,99,'+ device.pid + ','+ str(int(time.time()*1000)) +',url,'+ str(int(time.time()*1000))+',,,' + device.running_case_id + ',' + case_id + ','+ name + ','+app['package_name']+','+str(success)
        _shell = 'log -t moirai -p w ' + _log
        _stop_shell = 'am force-stop ' + app['package_name']
        device.shell(_shell)
        device.shell(_stop_shell)
        time.sleep(1)
        device.output_logcat(case_id)
        
    if logcat == 'true' and success is False :
        device.output_logcat(case_id)


    # # 华龙证券
    # if app['package_name'] == 'com.thinkive.android.hualong' or app['package_name'] == 'hualong.szkingdom.android.newphone':
    #     if success is False:
    #         _curren_app_str = device.shell('dumpsys window | grep mCurrentFocus=W')
    #         if 'Not Responding' in _curren_app_str:
    #             msg = '【异常错误】%s' % str('设备程序异常')
    #             device.shell('reboot')
    #             success = None

    # 录屏卡死检测
    if success is False and device.has_element('/系统提示/录屏/关闭应用', 1) and device.has_element('/系统提示/录屏/等待', 1):
        try:
            device.has_element('/系统提示/录屏/关闭应用')()
            device.shell('reboot')
        except Exception as e:
            print(e)
            pass

    # 录屏弹窗
    # adb shell dumpsys window | findstr mCurrentFocus=W
    # 小米：mCurrentFocus=Window{be3ed4e u0 Sys2017:com.android.systemui/com.android.systemui.media.MediaProjectionPermissionActivity}
    # vivo：mCurrentFocus=Window{com.android.systemui:ab10938 u0 com.android.systemui type=2017}
    # 华为：mCurrentFocus=Window{216832b u0 com.android.systemui}

    if success is True and type(msg) == str and '预期结果不为空' in msg:
        msg = '通过'
    device.log.info('callback_start')
    define_get_callback('after_case')(device, case_id, success, msg)
    # time.sleep(1)
    device.screencap()


    cloud_end_case(device, case_id, success, msg,has_success)


    if device.is_screenrecord_on():
        device.screenrecord_finish(case_id,success,has_success)

    if device.screencap_history and case_id is not None and success != 'cancel':
        import datetime
        if success is True:
            status = 'success'
        elif success is False:
            status = 'fail'
        else:
            status = 'error'
        _d = datetime.datetime.now()
        _date = '%s/%s/%s' % (_d.year, _d.month, _d.day)
        images_urls = _upload_device_screencap(device.screencap_history, status, _date, case_id, {'Device': device.device_id, "CaseName": name, 'App': app})
        print(images_urls)
        if len(images_urls) > 0:
            cloud_api('case-images', {'result': case_id, 'images': '|'.join(images_urls)})
    device.screencap_history = None

    # 理财部分检查后门设置项，或者首页部分
    try:
        if success is False and (name.startswith('理财') or name.startswith('首页') or name.startswith('行情') or name.startswith('我的') or name.startswith('交易')):
            # 返回首页
            home = None
            for _ in range(9):
                if home:
                    break
                home = device.has_element('/底部导航栏/首页界面跳转按钮', 2)
                if home:
                    device.has_element('/底部导航栏/首页界面跳转按钮')()
                else:
                    device.appium.back()
                    device.log.info('返回上一级页面')
            if not home:
                raise Exception()

            # 7.0版本
            # device.has_element('/首页/搜索')()
            # # if device.has_element('/首页/搜索/输入框'):
            # device['/首页/搜索/输入框'] = '.cfttest'
            # device.has_element('/开发测试后门/单元测试项')()
            # device.has_element('/开发测试后门/单元测试项/webview')()

            # 7.7.7版本
            time.sleep(2)
            device.appium.driver.tap([(650, 100)])
            device['/首页/搜索/输入框'] = '.cfttest'
            device.appium.slide_up(3000, 0.3)
            device.appium.slide_up(3000, 0.3)
            device.has_element('/开发测试后门/web')()
            device.has_element('/开发测试后门/web/X5内核测试')()

            if device.has_element('/开发测试后门/单元测试项/webview/勾选'):
                print('后门配置项未被勾选！！')
                device.has_element('/开发测试后门/单元测试项/webview/勾选')()
            else:
                print('后门配置项已被勾选')
    except:
        print('检查后门设置项出现异常')

    # device.clean_appium() todo 回到初始页面
    define_get_callback('back_basic')(device)


def __import_local_testcase(name):
    if name not in CASES_CRIPT_MAP:
        import importlib
        module_name = 'casescripts.' + '.'.join(name.split('/')[:-1])
        try:
            importlib.import_module(module_name)
            print(module_name)
            if name not in CASES_CRIPT_MAP:
                logging.error('尝试加载 %s， 但未成功' % name)
                raise CaseScriptNotExist(name)
        except ImportError:
            raise CaseScriptNotExist(name)
    pass


@functools.lru_cache(None)
def __import_public_func():
    import re
    li = []
    with open('casescripts/public.py', 'r', encoding='utf-8') as f:
        for line in f:
            r = re.search(r'def ([\u4e00-\u9fa5|_]+)\(', line)
            if r:
                li.append(r.group(1))
    return '    from casescripts.public import %s' % (', '.join(li))


def __import_online_testcase(name, script: str, args):
    '''    from casescripts.public import *'''
    args_str = ', '.join(["%s='%s'" % (k, args[k]) for k in args])
    _testcase_script = ["@casescript('%s')" % name, "def _(device, %s):" % args_str, '    from time import sleep', __import_public_func()]
    _testcase_script += ['    ' + _ for _ in script.split('\n')]
    testcase_script = '\n'.join(_testcase_script)
    try:
        exec(testcase_script)
    except:
        print(testcase_script)
        raise CaseScriptNotExist(name)
    if name not in CASES_CRIPT_MAP:
        logging.error('尝试加载 %s， 但未成功' % name)
        raise CaseScriptNotExist(name)


def runcase(app,device, name, has_success,case_id=None, script=None, **kwargs):
    if script:
        __import_online_testcase(name, script, kwargs)
    else:
        __import_local_testcase(name)
    runcasescript(app,device, name, has_success, case_id=case_id, **kwargs)


def run_all_local_case(device):
    import uuid
    import random
    all_keys = list(CASES_CRIPT_MAP.keys())
    while True:
        name = random.choice(all_keys)
        case_id = str(uuid.uuid1())
        yield name, case_id


def run_task_case(device):
    from random import randint
    while True:
        data = cloud_api('get-job', number=device.device_id, lock=1,device_ip=device.device_ip,device_wifi=device.device_wifi)
        if 'id' not in data or 'testcase' not in data:
            print(data)
            if 'error' in data and data['error'] == 'CASE_COMPLETED':
                t = randint(10, 30)
                print('没有任务了。等待 %s s' % t)
                time.sleep(t)
                continue
            elif 'error' in data and data['error'] == 'UNAVAILABLE':
                print('设备不可用')
                time.sleep(25)
                continue
            else:
                time.sleep(25)
                continue

        _app = data['app']
        _case_id = data['id']
        _name = data['testcase']['title']
        _device_id = data['device_id']
        _args = data['args']
        _script = data['script']
        _has_success = data['has_success'] if 'has_success' in data else 1
        _testcase_id = data['testcase']['id']
        yield _app, _name, _case_id, _args, _script,_has_success,_testcase_id