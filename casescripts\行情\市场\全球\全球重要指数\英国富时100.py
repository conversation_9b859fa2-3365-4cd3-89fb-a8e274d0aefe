from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('行情/市场/全球/全球重要指数/英国富时100/进入指数单券查看点数值')
def _7edaa2e0606b2acb55b0e0a0f95ebb5d(device):
    登录前页面处理(device)
    点击(device, '/底部导航栏/行情界面跳转按钮')
    目录校验(device, '一', '行情', '/行情/选中状态')
    点击(device, '/行情/市场/市场标题栏')
    控件左滑(device, '/行情/市场/沪深按钮标签')
    点击(device, '/行情/市场/全球/全球标签')
    目录校验(device, '二', '全球', '/行情/更多/选中状态')
    滑动控件至屏幕内(device, '/行情/市场/全球/全球重要指数/英国富时100/证券名称')
    闭市 = 获取文本信息(device, '/行情/市场/全球/全球重要指数/英国富时100/是否闭市')
    点击(device, '/行情/市场/全球/全球重要指数/英国富时100/证券名称')
    实际结果 = 获取文本信息(device, '/行情/市场/全球/全球重要指数/英国富时100/检查点数值')
    print("/行情/市场/全球/全球重要指数/英国富时100/检查点数值:::", 实际结果)
    结果 = 实际结果 != '' and (实际结果 != '--' or 闭市 == '闭市')
    # 结果 = 检查点数值 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


