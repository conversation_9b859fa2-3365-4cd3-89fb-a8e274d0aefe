#!/bin/bash
# ubuntu20.04 auto install ControlSystem

echo -e "\033[47;30m ---------------------------开始自动安装--------------------------------- \033[0m"
echo -e "\033[32m---------------------------安装开启ssh---------------------------------\033[0m"
sudo apt update
sudo apt-get install ssh net-tools -y
sudo /etc/init.d/ssh start

echo -e "\033[32m---------------------------安装必要的支持包---------------------------------\033[0m"
sudo apt install software-properties-common -y

echo -e "\033[32m---------------------------添加第三方源 Deadsnakes PPA---------------------------------\033[0m"
sudo add-apt-repository ppa:deadsnakes/ppa
sudo apt update

python_model_check()
{
  if "pip" >/dev/null 2>&1
  then
      echo "1"
  else
      echo "0"
  fi
}
pip_result=`python_model_check $1`
if [ $pip_result == 1 ]
then
  echo -e "\033[32m---------------------------已安装pip---------------------------------\033[0m"
  echo "---------------已安装pip-----------------"
else
  echo -e "\033[32m---------------------------安装pip---------------------------------\033[0m"
  sudo apt install python3-pip -y
fi


java_check()
{
  if "java" >/dev/null 2>&1
  then
      echo "1"
  else
      echo "0"
  fi
}
java_result=`java_check $1`
if [ $java_result == 1 ]
then
  echo -e "\033[32m---------------------------已安装jdk---------------------------------\033[0m"
else
  echo -e "\033[32m---------------------------安装jdk---------------------------------\033[0m"
  sudo apt update
  sudo apt install openjdk-8-jdk -y
fi

downloadAndroidSdk(){
    if [ `uname` == 'Darwin' ]; then
        wget https://dl.google.com/android/repository/sdk-tools-darwin-4333796.zip
        mv sdk-tools-darwin-4333796.zip sdk-tools.zip
    else
        wget https://dl.google.com/android/repository/commandlinetools-linux-6858069_latest.zip
        mv commandlinetools-linux-6858069_latest.zip commandlinetools-linux.zip
    fi

    if [ ! -d "android-sdk" ]; then
        mkdir android-sdk
    fi

    unzip -d android-sdk commandlinetools-linux.zip

    android-sdk/cmdline-tools/bin/./sdkmanager --sdk_root=`pwd`/android-sdk/ "platforms;android-29"
    android-sdk/cmdline-tools/bin/./sdkmanager --sdk_root=`pwd`/android-sdk/ "platform-tools"
    android-sdk/cmdline-tools/bin/./sdkmanager --sdk_root=`pwd`/android-sdk/ "build-tools;29.0.3"
}

echo -e "\033[32m---------------------------安装android sdk---------------------------------\033[0m"
hash adb 2>/dev/null || downloadAndroidSdk

echo -e "\033[32m---------------------------安装node vim npm git---------------------------------\033[0m"
sudo apt update
sudo apt install git curl -y
# sudo apt install nodejs npm -y
curl -fsSL https://deb.nodesource.com/setup_14.x | sudo -E bash -
sudo apt-get install -y nodejs
sudo apt install vim -y

echo -e "\033[32m---------------------------下载项目代码并安装---------------------------------\033[0m"
mkdir src
cd src
git clone -b moreapp https://apiclient:<EMAIL>/whale/ControlSystem.git
cd ControlSystem/
pip install virtualenv -i https://pypi.tuna.tsinghua.edu.cn/simple
python3 -m virtualenv env

cd ../../
echo "export PATH=\$PATH:`pwd`/android-sdk/platform-tools" >> src/ControlSystem/env/bin/activate
echo "export ANDROID_SDK_ROOT=`pwd`/android-sdk" >> src/ControlSystem/env/bin/activate
echo "export ANDROID_HOME=`pwd`/android-sdk" >> src/ControlSystem/env/bin/activate
echo "export PATH=\$PATH:`pwd`/android-sdk/build-tools/29.0.3" >> src/ControlSystem/env/bin/activate
echo "export JAVA_HOME=/usr/lib/jvm/java-8-openjdk-amd64" >> src/ControlSystem/env/bin/activate

sudo sh -c 'echo "export PATH=\$PATH:`pwd`/android-sdk/platform-tools" >> /etc/profile'
sudo sh -c 'echo "export ANDROID_SDK_ROOT=`pwd`/android-sdk" >> /etc/profile'
sudo sh -c 'echo "export ANDROID_HOME=`pwd`/android-sdk" >> /etc/profile'
sudo sh -c 'echo "export PATH=\$PATH:`pwd`/android-sdk/build-tools/29.0.3" >> /etc/profile'
sudo sh -c 'echo "export JAVA_HOME=/usr/lib/jvm/java-8-openjdk-amd64" >> /etc/profile'

source /etc/profile

cd src/ControlSystem/
. env/bin/activate
cp config.default.ini config.ini
mkdir log

echo -e "\033[32m---------------------------安装python依赖---------------------------------\033[0m"
# pip config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple
pip install scikit-learn==0.19.2 -i https://pypi.tuna.tsinghua.edu.cn/simple

echo -e "\033[32m-----------------------安装appium，下载时间较长请耐心等待---------------------------\033[0m"
sudo npm --registry https://registry.npm.taobao.org install -g appium@1.21.0 --unsafe-perm=true --allow-root -y
sudo chmod 777 /usr/lib/node_modules/appium/node_modules/appium-uiautomator2-server/apks/appium-uiautomator2-server-debug-androidTest.apk
sudo chmod 777 /usr/lib/node_modules/appium/node_modules/appium-uiautomator2-server/apks/appium-uiautomator2-server-v4.21.1.apk
sudo chmod -R 777 /usr/lib/node_modules/appium/node_modules/appium-uiautomator2-server

echo -e "\033[32m----------------------安装完成，请配置平台地址以及EU_ID等配置项--------------------\033[0m"