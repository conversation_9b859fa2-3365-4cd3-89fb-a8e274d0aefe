from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('交易/打新神器/新股、新债发行计划/新股新债发行一览_新债_即将发行')
def _531aa9aac1d8e241c858eee078d56d29(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # hxf 生产账户
    # 初始化
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/打新神器')
    目录校验(device, '二', '打新神器', '/页面标题')
    点击(device, '/交易/普通交易/打新神器/打新神器页面/新股、新债发行计划')
    sleep(2)
    try:
        点击(device, '/交易/普通交易/打新神器/新股、新债发行计划/新债')
    except:
        返回(device)
        sleep(2)
        点击(device, '/交易/普通交易/打新神器/打新神器页面/新股、新债发行计划')
        点击(device, '/交易/普通交易/打新神器/新股、新债发行计划/新债')
    点击(device, '/交易/普通交易/打新神器/新股、新债发行计划/新债/即将发行')
    sleep(2)
    try:
        第一条记录 = 获取文本信息(device, '/交易/普通交易/打新神器/新股、新债发行计划/新债/已发行/第一条记录')
    except:
        第一条记录 = 获取文本信息(device, '/交易/普通交易/打新神器/新股、新债发行计划/新债/已发行/无记录')
    print(第一条记录)
    错误校验点 = 为空校验(device)
    结果 = len(第一条记录) > 1
    print(结果)
    return 结果, 错误校验点


@casescript('交易/打新神器/新股、新债发行计划/新股新债发行一览_新债_已发行')
def _b9f0dae05f59e2ad3f3b825636da1c2c(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # hxf 生产账户
    # 初始化
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/打新神器')
    目录校验(device, '二', '打新神器', '/页面标题')
    点击(device, '/交易/普通交易/打新神器/打新神器页面/新股、新债发行计划')
    点击(device, '/交易/普通交易/打新神器/新股、新债发行计划/新债')
    sleep(2)
    点击(device, '/交易/普通交易/打新神器/新股、新债发行计划/新债/已发行')
    第一条记录 = 获取文本信息(device, '/交易/普通交易/打新神器/新股、新债发行计划/新债/已发行/第一条记录')
    print('第一条记录：', 第一条记录)
    错误校验点 = 为空校验(device)
    结果 = len(第一条记录) > 1
    print('结果：', 结果)
    return 结果, 错误校验点


@casescript('交易/打新神器/新股、新债发行计划/新股新债发行一览_新股_上市表现')
def _1a4a469cd776244247da2e5e6f61757b(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # hxf 生产账户
    # 初始化
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/打新神器')
    目录校验(device, '二', '打新神器', '/页面标题')
    sleep(5)
    点击(device, '/交易/普通交易/打新神器/打新神器页面/新股、新债发行计划')
    sleep(2)
    # 点击('/交易/普通交易/打新神器/新股、新债发行计划/新股')
    点击(device, '/交易/普通交易/打新神器/新股、新债发行计划/新股/上市表现')
    第一条记录 = 获取文本信息(device, '/交易/普通交易/打新神器/新股、新债发行计划/新股/上市表现/第一条记录')
    错误校验点 = 为空校验(device)
    结果 = len(第一条记录) > 1
    print('第一条记录：%s , 结果：%s' % (第一条记录, 结果))
    return 结果, 错误校验点


@casescript('交易/打新神器/新股、新债发行计划/新股新债发行一览_新股_即将发行')
def _a83159b9b8825f6a8c20e5b6b03384e3(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # hxf 生产账户
    # 初始化
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/打新神器')
    目录校验(device, '二', '打新神器', '/页面标题')
    sleep(2)
    点击(device, '/交易/普通交易/打新神器/打新神器页面/新股、新债发行计划')
    # 点击('/交易/普通交易/打新神器/新股、新债发行计划/新股')
    sleep(2)
    点击(device, '/交易/普通交易/打新神器/新股、新债发行计划/新股/即将发行')
    第一条记录 = 获取文本信息(device, '/交易/普通交易/打新神器/新股、新债发行计划/新股/即将发行/第一条记录')
    错误校验点 = 为空校验(device)
    结果 = len(第一条记录) > 1
    print('第一条记录：%s , 结果：%s' % (第一条记录, 结果))
    return 结果, 错误校验点


@casescript('交易/打新神器/新股、新债发行计划/新股新债发行一览_新股_已发行待上市')
def _ac547b5828edf1a6fce2a27b24a2d7f9(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # hxf 生产账户
    # 初始化
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/打新神器')
    目录校验(device, '二', '打新神器', '/页面标题')
    sleep(2)
    点击(device, '/交易/普通交易/打新神器/打新神器页面/新股、新债发行计划')
    # 点击('/交易/普通交易/打新神器/新股、新债发行计划/新股')
    sleep(2)
    点击(device, '/交易/普通交易/打新神器/新股、新债发行计划/新股/已发行待上市')
    第一条记录 = 获取文本信息(device, '/交易/普通交易/打新神器/新股、新债发行计划/新股/已发行/第一条记录')
    错误校验点 = 为空校验(device)
    结果 = len(第一条记录) > 1
    print('第一条记录：%s , 结果：%s' % (第一条记录, 结果))
    return 结果, 错误校验点


