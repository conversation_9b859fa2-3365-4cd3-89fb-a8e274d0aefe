from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('行情/自选股/创业板指/创业板指')
def _a859e0d3258c6ccd154e1d0a9b179a68(device):
    登录前页面处理(device)
    点击(device, '/底部导航栏/行情界面跳转按钮')
    目录校验(device, '一', '行情', '/行情/选中状态')
    点击(device, '/行情/自选股/自选股标签')
    目录校验(device, '二', '自选股', '/行情/自选股')
    创业板指行情点数 = 获取文本信息(device, '/行情/自选股/创业板指/行情点数')
    结果 = 创业板指行情点数 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


