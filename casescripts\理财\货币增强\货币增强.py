from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('理财/货币增强/货币增强/货币增强')
def _41668003ab088c974f73968df1ae4840(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # 初始化
    登录前页面处理(device)
    理财登陆(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '理财', '/理财/选中状态')
    # time.sleep(2).0
    # 上滑()
    # time.sleep(2)
    # 上滑()
    # # 循环滑动('/理财/快捷功能/华泰专属理财', '上')
    # time.sleep(5)
    滑动控件至屏幕内_理财(device, '/理财/首发基金')
    # 滑动控件至屏幕内_理财('/理财/货币增强')
    控件左滑_理财(device, '/理财/首发基金')
    点击(device, '/理财/自定义添加')
    # 循环滑动('/理财/货币增强','上')
    上滑(device)
    上滑(device)
    device.appium.driver.background_app(3)
    点击(device, '/理财/货币增强')
    sleep(5)
    目录校验(device, '二', '华泰紫金货币增强', '/页面标题')
    实际结果 = float(获取文本信息(device, '/理财/货币增强/收益率')[0:-1])
    print(实际结果)
    if 实际结果 >= 0:
        结果 = True
    else:
        结果 = False
    错误校验点 = 数字校验(device, 实际结果)
    return 结果, 错误校验点


