from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('行情/市场/沪深/科创板/进入科创板查看指数值')
def _8187f23364508b98ee45ca20ebc82d08(device):
    登录前页面处理(device)
    点击(device, '/底部导航栏/行情界面跳转按钮')
    目录校验(device, '一', '行情', '/行情/选中状态')
    点击(device, '/行情/市场/市场标题栏')
    目录校验(device, '二', '沪深', '/行情/更多/选中状态')
    点击(device, '/行情/市场/沪深/科创板/科创板详情按钮')
    sleep(3)
    科创板指数点值 = 获取文本信息(device, '/行情/市场/沪深/科创板/科创板指数点值')
    print("科创板指数点值:::", 科创板指数点值)
    结果 = 科创板指数点值 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


