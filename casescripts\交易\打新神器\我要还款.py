from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('交易/打新神器/我要还款/我要还款')
def _07994194cdbd32580f1ee94147e266ca(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # 初始化
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/打新神器')
    目录校验(device, '二', '打新神器', '/页面标题')
    sleep(3)
    点击(device, '/交易/普通交易/打新神器/打新神器页面/我要还款')
    # 点击('/交易/普通交易/打新神器/打新神器页面/我要还款/确定')
    实际结果 = 获取文本信息(device, '/交易/普通交易/打新神器/打新神器页面/我要还款/查询借款')
    错误校验点 = 类型校验(device, float, 实际结果)
    结果 = 实际结果 != '' and 实际结果 != '--'
    return 结果, 错误校验点


