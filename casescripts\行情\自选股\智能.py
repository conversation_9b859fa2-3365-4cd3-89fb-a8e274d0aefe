from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('行情/自选股/智能/成本神器')
def _4deee0004c14b16659fe7dcfcd251b0c(device, 股票代码='600123'):
    错误校验点 = ''
    登录前页面处理(device)
    点击(device, '/底部导航栏/行情界面跳转按钮')
    目录校验(device, '一', '行情', '/行情/选中状态')
    点击(device, '/行情/自选股/自选股标签')
    # 目录校验('二', '自选股', '/行情/自选股')
    点击(device, '/行情/自选股/智能按钮')
    目录校验(device, '二', '自选股', '/行情/自选股/智能/页面标题')
    点击(device, '/行情/自选股/智能/成本神器/成本神器标签')
    try:
        第一条个股成本 = 获取文本信息(device, '/行情/自选股/智能/成本神器/无记录')
        点击(device, '/行情/自选股/添加自选股/返回')
        点击(device, '/行情/自选股/添加自选股')
        输入文本(device, '/行情/自选股/添加自选股/输入框', 股票代码)
        点击(device, '/行情/自选股/添加自选股/搜索')
        点击(device, '/行情/自选股/添加自选股/添加')
        点击(device, '/行情/自选股/添加自选股/返回')
        点击(device, '/行情/自选股/智能按钮')
        点击(device, '/行情/自选股/智能/成本神器/成本神器标签')
        第一条个股成本 = 获取文本信息(device, '/行情/自选股/智能/成本神器/第一条个股成本')
    except:
        第一条个股成本 = 获取文本信息(device, '/行情/自选股/智能/成本神器/第一条个股成本')
    print('第一条个股成本::', 第一条个股成本)
    结果 = 第一条个股成本 != ''
    if 第一条个股成本 == '':
        错误校验点 = '无自选股成本数据'
    return 结果, 错误校验点


@casescript('行情/自选股/智能/智能盯盘')
def _e66796eef14ad01f209fbbee5dd5944e(device, 股票代码='600123'):
    登录前页面处理(device)
    点击(device, '/底部导航栏/行情界面跳转按钮')
    目录校验(device, '一', '行情', '/行情/选中状态')
    点击(device, '/行情/自选股/自选股标签')
    # 目录校验('二', '自选股', '/行情/自选股')
    点击(device, '/行情/自选股/智能按钮')
    目录校验(device, '二', '自选股', '/行情/自选股/智能/页面标题')
    点击(device, '/行情/行情页面/自选股/智能/智能盯盘')
    预期结果 = ''
    实际结果 = ''
    结果 = ''
    if 获取控件对象(device, '/行情/请登录注册账号') != '':
        预期结果 = '登录注册'
        实际结果 = 获取文本信息(device, '/行情/自选股/智能/智能盯盘/登录提示')
    else:
        预期结果 = '自选股'
        实际结果 = 获取文本信息(device, '/行情/自选股/智能/智能盯盘/自选股')
    错误校验点 = 非空校验(device, 预期结果, 实际结果)
    结果 = 预期结果 in 实际结果
    return 结果, 错误校验点


@casescript('行情/自选股/智能/相似K线')
def _03ba4bc3d2b2ac01334ae0cabf6f232b(device, 股票代码='600123'):
    错误校验点 = ''
    登录前页面处理(device)
    点击(device, '/底部导航栏/行情界面跳转按钮')
    目录校验(device, '一', '行情', '/行情/选中状态')
    点击(device, '/行情/自选股/自选股标签')
    # 目录校验('二', '自选股', '/行情/自选股')
    点击(device, '/行情/自选股/智能按钮')
    目录校验(device, '二', '自选股', '/行情/自选股/智能/页面标题')
    控件左滑(device, '/行情/自选股/智能/自选资讯/自选资讯标签')
    点击(device, '/行情/自选股/智能/相似K线/相似K线标签')
    try:
        相似K线标题 = 获取文本信息(device, '/行情/自选股/智能/相似K线/无记录')
        点击(device, '/行情/自选股/添加自选股/返回')
        点击(device, '/行情/自选股/添加自选股')
        输入文本(device, '/行情/自选股/添加自选股/输入框', 股票代码)
        点击(device, '/行情/自选股/添加自选股/搜索')
        点击(device, '/行情/自选股/添加自选股/添加')
        点击(device, '/行情/自选股/添加自选股/返回')
        点击(device, '/行情/自选股/智能按钮')
        控件左滑(device, '/行情/自选股/智能/自选资讯/自选资讯标签')
        点击(device, '/行情/自选股/智能/相似K线/相似K线标签')
        相似K线标题 = 获取文本信息(device, '/行情/自选股/智能/相似K线/相似K线标题')
    except:
        try:
            相似K线标题 = 获取文本信息(device, '/行情/自选股/智能/相似K线/相似K线标题')
        except:
            返回(device)
            点击(device, '/行情/自选股/智能/相似K线/相似K线标签')
        try:
            相似K线标题 = 获取文本信息(device, '/行情/自选股/智能/相似K线/无记录')
            点击(device, '/行情/自选股/添加自选股/返回')
            点击(device, '/行情/自选股/添加自选股')
            输入文本(device, '/行情/自选股/添加自选股/输入框', 股票代码)
            点击(device, '/行情/自选股/添加自选股/搜索')
            点击(device, '/行情/自选股/添加自选股/添加')
            点击(device, '/行情/自选股/添加自选股/返回')
            点击(device, '/行情/自选股/智能按钮')
            控件左滑(device, '/行情/自选股/智能/自选资讯/自选资讯标签')
            点击(device, '/行情/自选股/智能/相似K线/相似K线标签')
            相似K线标题 = 获取文本信息(device, '/行情/自选股/智能/相似K线/相似K线标题')
        except:
            相似K线标题 = 获取文本信息(device, '/行情/自选股/智能/相似K线/相似K线标题')
    print('相似K线标题::', 相似K线标题)
    结果 = 相似K线标题 != ''
    if 相似K线标题 == '':
        错误校验点 = '未进入相似K线页面，未获取到相似K线标题'
    return 结果, 错误校验点


@casescript('行情/自选股/智能/短线精灵')
def _a839c8fa02e4f14d1ddacc74f80d1368(device, 股票代码='600123'):
    错误校验点 = ''
    登录前页面处理(device)
    点击(device, '/底部导航栏/行情界面跳转按钮')
    目录校验(device, '一', '行情', '/行情/选中状态')
    点击(device, '/行情/自选股/自选股标签')
    # 目录校验('二', '自选股', '/行情/自选股')
    点击(device, '/行情/自选股/智能按钮')
    目录校验(device, '二', '自选股', '/行情/自选股/智能/页面标题')
    点击(device, '/行情/自选股/智能/短线精灵')
    if 获取控件对象(device, '/行情/自选股/智能/短线精灵/未加自选') != '':
        是否异动 = 获取文本信息(device, '/行情/自选股/智能/短线精灵/未加自选')
        if '没有添加自选' in 是否异动:
            点击(device, '/行情/自选股/添加自选股/搜索')
            输入文本(device, '/行情/自选股/添加自选股/输入框', 股票代码)
            点击(device, '/行情/自选股/添加自选股/搜索')
            点击(device, '/行情/自选股/添加自选股/添加')
            点击(device, '/行情/自选股/添加自选股/返回')
    try:
        点击(device, '/行情/自选股/智能/短线精灵/第一条')
        实际结果 = 获取文本信息(device, '/行情/市场/股指/股指期货/成分品种校验/价格')
        错误校验点 = 为空校验(device)
        结果 = 实际结果 != '' and 实际结果 != '--'
    except:
        预期结果 = '暂无异动'
        实际结果 = 获取文本信息(device, '/行情/自选股/智能/短线精灵/未加自选')
        错误校验点 = 非空校验(device, 预期结果, 实际结果)
        结果 = 预期结果 in 实际结果
    return 结果, 错误校验点


@casescript('行情/自选股/智能/股力值')
def _af6fdb5be7ead7db722846351f52f057(device, 股票代码='600123'):
    错误校验点 = ''
    登录前页面处理(device)
    点击(device, '/底部导航栏/行情界面跳转按钮')
    目录校验(device, '一', '行情', '/行情/选中状态')
    点击(device, '/行情/自选股/自选股标签')
    # 目录校验('二', '自选股', '/行情/自选股')
    点击(device, '/行情/自选股/智能按钮')
    目录校验(device, '二', '自选股', '/行情/自选股/智能/页面标题')
    点击(device, '/行情/自选股/智能/股力值/股力值标签')
    try:
        第一条自选股股力值 = 获取文本信息(device, '/行情/自选股/智能/股力值/无记录')
        点击(device, '/行情/自选股/添加自选股/返回')
        点击(device, '/行情/自选股/添加自选股')
        输入文本(device, '/行情/自选股/添加自选股/输入框', 股票代码)
        点击(device, '/行情/自选股/添加自选股/搜索')
        点击(device, '/行情/自选股/添加自选股/添加')
        点击(device, '/行情/自选股/添加自选股/返回')
        点击(device, '/行情/自选股/智能按钮')
        点击(device, '/行情/自选股/智能/股力值/股力值标签')
        第一条自选股股力值 = 获取文本信息(device, '/行情/自选股/智能/股力值/第一条自选股股力值')
    except:
        try:
            第一条自选股股力值 = 获取文本信息(device, '/行情/自选股/智能/股力值/第一条自选股股力值')
        except:
            返回(device)
            sleep(2)
            点击(device, '/行情/自选股/智能/股力值/股力值标签')
            try:
                第一条自选股股力值 = 获取文本信息(device, '/行情/自选股/智能/股力值/无记录')
                点击(device, '/行情/自选股/添加自选股/返回')
                点击(device, '/行情/自选股/添加自选股')
                输入文本(device, '/行情/自选股/添加自选股/输入框', 股票代码)
                点击(device, '/行情/自选股/添加自选股/搜索')
                点击(device, '/行情/自选股/添加自选股/添加')
                点击(device, '/行情/自选股/添加自选股/返回')
                点击(device, '/行情/自选股/智能按钮')
                点击(device, '/行情/自选股/智能/股力值/股力值标签')
                第一条自选股股力值 = 获取文本信息(device, '/行情/自选股/智能/股力值/第一条自选股股力值')
            except:
                第一条自选股股力值 = 获取文本信息(device, '/行情/自选股/智能/股力值/第一条自选股股力值')
    print('第一条自选股股力值::', 第一条自选股股力值)
    结果 = 第一条自选股股力值 != ''
    if 第一条自选股股力值 == '':
        错误校验点 = '无自选股股力值数据'
    return 结果, 错误校验点


@casescript('行情/自选股/智能/自选资讯')
def _a2035a79dc756b7c8b6afb2b22665e9c(device, 股票代码='600123'):
    错误校验点 = ''
    登录前页面处理(device)
    点击(device, '/底部导航栏/行情界面跳转按钮')
    目录校验(device, '一', '行情', '/行情/选中状态')
    点击(device, '/行情/自选股/自选股标签')
    # 目录校验('二', '自选股', '/行情/自选股')
    点击(device, '/行情/自选股/智能按钮')
    目录校验(device, '二', '自选股', '/行情/自选股/智能/页面标题')
    点击(device, '/行情/自选股/智能/自选资讯/自选资讯标签')
    try:
        第一条资讯标题 = 获取文本信息(device, '/行情/自选股/智能/自选资讯/无记录')
        点击(device, '/行情/自选股/添加自选股/返回')
        点击(device, '/行情/自选股/添加自选股')
        输入文本(device, '/行情/自选股/添加自选股/输入框', 股票代码)
        点击(device, '/行情/自选股/添加自选股/搜索')
        点击(device, '/行情/自选股/添加自选股/添加')
        点击(device, '/行情/自选股/添加自选股/返回')
        点击(device, '/行情/自选股/智能按钮')
        点击(device, '/行情/自选股/智能/自选资讯/自选资讯标签')
        第一条资讯标题 = 获取文本信息(device, '/行情/自选股/智能/自选资讯/第一条资讯标题')
    except:
        try:
            第一条资讯标题 = 获取文本信息(device, '/行情/自选股/智能/自选资讯/第一条资讯标题')
        except:
            返回(device)
            sleep(2)
            点击(device, '/行情/自选股/智能/自选资讯/自选资讯标签')
            try:
                第一条资讯标题 = 获取文本信息(device, '/行情/自选股/智能/自选资讯/无记录')
                点击(device, '/行情/自选股/添加自选股/返回')
                点击(device, '/行情/自选股/添加自选股')
                输入文本(device, '/行情/自选股/添加自选股/输入框', 股票代码)
                点击(device, '/行情/自选股/添加自选股/搜索')
                点击(device, '/行情/自选股/添加自选股/添加')
                点击(device, '/行情/自选股/添加自选股/返回')
                点击(device, '/行情/自选股/智能按钮')
                点击(device, '/行情/自选股/智能/自选资讯/自选资讯标签')
                第一条资讯标题 = 获取文本信息(device, '/行情/自选股/智能/自选资讯/第一条资讯标题')
            except:
                第一条资讯标题 = 获取文本信息(device, '/行情/自选股/智能/自选资讯/第一条资讯标题')
    print('第一条资讯标题::', 第一条资讯标题)
    结果 = 第一条资讯标题 != ''
    if 第一条资讯标题 == '':
        错误校验点 = '无自选资讯内容'
    return 结果, 错误校验点


