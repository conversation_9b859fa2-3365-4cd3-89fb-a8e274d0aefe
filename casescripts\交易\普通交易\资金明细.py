from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('交易/普通交易/资金明细/资金明细')
def _7abac804945363576553aa63ac277864(device, 账号='666630296771', 交易密码='124369', 通信密码='159753'):
    # 初始化
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/普通交易')
    点击(device, '/交易/普通交易/普通交易首页/更多')
    目录校验(device, '二', '股票交易', '/页面标题')
    点击(device, '/交易/普通交易/更多/更多交易页面/资金明细')
    点击(device, '/交易/普通交易/更多/资金明细/查询按钮')
    if 获取控件对象(device, '/交易/普通交易/更多/资金明细/资金明细列表第一条记录'):
        实际结果 = 获取文本信息(device, '/交易/普通交易/更多/资金明细/资金明细列表第一条记录')
    else:
        实际结果 = 获取控件对象(device, '/交易/普通交易/其他交易/大宗交易/委托查询页面/无记录')
    错误校验点 = 为空校验(device)
    结果 = 实际结果 != ''
    return 结果, 错误校验点


