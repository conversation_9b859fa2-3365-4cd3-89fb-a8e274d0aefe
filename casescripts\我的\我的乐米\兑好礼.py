from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('我的/我的乐米/兑好礼/兑好礼')
def _df732aec990000fd370648f87368b52d(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    登录前页面处理(device)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    上滑(device)
    点击(device, '/底部导航栏/理财界面跳转按钮')
    点击(device, '/底部导航栏/我的界面跳转按钮')
    目录校验(device, '一', '我的', '/我的/选中状态')
    点击(device, '/我的/我的乐米')
    目录校验(device, '二', '我的乐米', '/我的/我的乐米/页面标题')
    点击(device, '/我的/我的乐米/兑好礼')
    实际结果 = 获取文本信息(device, '/我的/我的乐米/兑好礼/第一条')
    错误校验点 = 为空校验(device)
    结果 = 实际结果 != ''
    return 结果, 错误校验点


