from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('首页/专家/明星专家/明星专家_直播')
def _4c4c4024e1080f7203f69cf2c07827f1(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # 初始化
    登录前页面处理(device)
    点击(device, '/底部导航栏/首页界面跳转按钮')
    目录校验(device, '一','首页','/首页/选中状态')
    点击(device, '/首页/专家')
    目录校验(device, '二', '专家', '/首页/专家/选中状态')
    点击(device, '/首页/专家/明星专家')
    点击(device, '/首页/专家/明星专家/直播')
    实际结果 = 获取文本信息(device, '/首页/专家/明星专家/直播/第一条记录时间')
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


@casescript('首页/专家/明星专家/明星专家_简介')
def _dd9c5c403093e72f468f06d8f09d9470(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # 初始化
    登录前页面处理(device)
    点击(device, '/底部导航栏/首页界面跳转按钮')
    目录校验(device, '一', '首页', '/首页/选中状态')
    点击(device, '/首页/专家')
    目录校验(device, '二', '专家', '/首页/专家/选中状态')
    点击(device, '/首页/专家/明星专家')
    点击(device, '/首页/专家/明星专家/简介')
    实际结果 = 获取文本信息(device, '/首页/专家/明星专家/简介/个人信息')
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


@casescript('首页/专家/明星专家/明星专家_观点')
def _92df1fe5583e7b7c9e42a2836a9bb84e(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # 初始化
    登录前页面处理(device)
    点击(device, '/底部导航栏/首页界面跳转按钮')
    目录校验(device, '一', '首页', '/首页/选中状态')
    点击(device, '/首页/专家')
    目录校验(device, '二', '专家', '/首页/专家/选中状态')
    点击(device, '/首页/专家/明星专家')
    点击(device, '/首页/专家/明星专家/观点')
    实际结果 = 获取文本信息(device, '/首页/专家/明星专家/观点/第一条记录标题')
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


@casescript('首页/专家/明星专家/更多')
def _59c96aae7c6a92f36ca03bd4119c4c94(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # 初始化
    登录前页面处理(device)
    点击(device, '/底部导航栏/首页界面跳转按钮')
    目录校验(device, '一', '首页', '/首页/选中状态')
    点击(device, '/首页/专家')
    目录校验(device, '二', '专家', '/首页/专家/选中状态')
    点击(device, '/首页/专家/更多')
    实际结果 = 获取文本信息(device, '/首页/专家/更多/第一个专家名字')
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


