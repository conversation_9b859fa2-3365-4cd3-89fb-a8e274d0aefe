from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('行情/市场/更多/沪深/深证A股/获取首只深证A股股名称')
def _b4b4f2cbd89f314b221a9c463ec83bb1(device):
    登录前页面处理(device)
    print(device.appium.driver)
    点击(device, '/底部导航栏/行情界面跳转按钮')
    点击(device, '/行情/市场/市场标题栏')
    控件左滑(device, '/行情/市场/沪深按钮标签')
    点击(device, '/行情/市场/更多/更多标签')
    点击(device, '/行情/市场/更多/沪深/深证A股/深证A股标签')
    首只深证A股名称 = 获取文本信息(device, '/行情/市场/更多/沪深/深证A股/首只深证A股名称')
    结果 = 首只深证A股名称 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


