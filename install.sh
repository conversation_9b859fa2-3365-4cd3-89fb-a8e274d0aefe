#!/bin/bash
# oracle username: yo<PERSON><PERSON><PERSON>@maxmail.in  password: cYwMS5bm73LKrVZSh9CJZQPV
# https://files.plmt-soft.com/index.php?share/folder&user=1&sid=JiajhQAX  The software you may be need


hash virtualenv 2>/dev/null || { echo >&2 "I require virtualenv but it's not installed.  Aborting."; exit 1; }
hash node 2>/dev/null || { echo >&2 "I require nodejs but it's not installed.  Aborting."; exit 1; }
hash java 2>/dev/null || { echo >&2 "I require sdk but it's not installed.  Aborting."; exit 1; }

downloadAndroidSdk(){
    if [ `uname` == 'Darwin' ]; then
        wget https://dl.google.com/android/repository/sdk-tools-darwin-4333796.zip
        mv sdk-tools-darwin-4333796.zip sdk-tools.zip
    else
        wget https://dl.google.com/android/repository/sdk-tools-linux-4333796.zip
        mv sdk-tools-linux-4333796.zip sdk-tools.zip
    fi

    if [ ! -d "AndroidSdkTool" ]; then
        mkdir AndroidSdkTool
    fi

    unzip sdk-tools.zip

    mv tools AndroidSdkTool/
    rm sdk-tools.zip
}

if [ ! -d "env" ]; then
  virtualenv env
fi

. env/bin/activate

# pip freeze > ./requirements.txt

pip config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple

pip install -r requirements.txt

# npm --registry https://registry.npm.taobao.org install appium

if [ ! -d "node_modules" ]; then
  npm --registry https://registry.npm.taobao.org install
fi

ls ./AndroidSdkTool/tools/bin/sdkmanager 2>/dev/null || downloadAndroidSdk

PATH=$PATH:`pwd`/node_modules/.bin

echo "export PATH=\$PATH:`pwd`/node_modules/.bin" >> env/bin/activate


hash adb 2>/dev/null || downloadAndroidSdk

if [ ! -d "casescript" ]; then
    #git clone https://git.plmt-soft.com/whale/casescript.git
fi

./AndroidSdkTool/tools/bin/sdkmanager "platform-tools"
./AndroidSdkTool/tools/bin/sdkmanager "build-tools;29.0.1"

echo "" >> env/bin/activate
echo "export PATH=\$PATH:`pwd`/AndroidSdkTool/platform-tools" >> env/bin/activate
echo "export ANDROID_SDK_ROOT=`pwd`/AndroidSdkTool" >> env/bin/activate

if [ `uname` == 'Darwin' ]; then
    echo "export MAC_OS=1" >> env/bin/activate
fi