from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('交易/融资融券/委托成交/当日委托')
def _83ea853881b3ba6832b870ef06f8fbed(device, 账号='58008098', 交易密码='123123', 通信密码='123123'):
    # 账号 = '104000030816'
    # 交易密码 = '566173'
    # 通信密码 = 'bao654607'
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/融资融券')
    点击(device, '/交易/融资融券/融资融券首页/委托成交')
    目录校验(device, '二', '委托', '/交易/融资融券/委托成交/页面标题')
    sleep(2)
    点击(device, '/交易/融资融券/委托成交/当日委托/当日委托页面/第一条')
    try:
        实际结果 = 获取文本信息(device, '/交易/融资融券/委托成交/当日委托/当日委托页面/第一条的股票名称')
    except:
        print('-----当日无委托记录-----')
        # 点击('/交易/融资融券/融资融券首页/委托成交')
        点击(device, '/交易/融资融券/委托成交/当日成交')
        点击(device, '/交易/融资融券/委托成交/当日委托')
        实际结果 = 获取控件对象(device, '/交易/融资融券/委托成交/当日委托/当日委托页面/无记录')
    错误校验点 = 为空校验(device)
    结果 = 实际结果 != ''
    return 结果, 错误校验点


@casescript('交易/融资融券/委托成交/当日成交')
def _e487ae1437868c2483f00eb3625b5838(device, 账号='58008098', 交易密码='123123', 通信密码='123123'):
    # 账号 = '104000030816'
    # 交易密码 = '566173'
    # 通信密码 = 'bao654607'
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/融资融券')
    点击(device, '/交易/融资融券/融资融券首页/委托成交')
    目录校验(device, '二', '委托', '/交易/融资融券/委托成交/页面标题')
    点击(device, '/交易/融资融券/委托成交/当日成交')
    sleep(2)
    try:
        点击(device, '/交易/融资融券/委托成交/当日成交/当日成交页面/点击第一条委托')
        实际结果 = 获取文本信息(device, '/交易/融资融券/委托成交/当日成交/第一条记录/股票名称')
    except:
        print('当日无成交记录')
        实际结果 = 获取控件对象(device, '/交易/融资融券/委托成交/当日成交/无记录')
    错误校验点 = 为空校验(device)
    结果 = 实际结果 != ''
    return 结果, 错误校验点


