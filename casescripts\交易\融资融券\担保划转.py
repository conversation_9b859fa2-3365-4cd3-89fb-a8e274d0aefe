from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('交易/融资融券/担保划转/担保划转')
def _935143f3a630bc55ff3fd952581c77e0(device, 账号='30002187', 交易密码='123123', 通信密码='123123'):
    错误校验点 = ''
    交易数量 = 100
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/融资融券')
    点击(device, '/交易/融资融券/融资融券首页/担保划转')
    目录校验(device, '二', '担保品划转', '/页面标题')
    担保品 = 获取控件对象(device, '/交易/融资融券/担保划转/担保品列表')
    结果 = ''
    if 担保品 != '':
        点击(device, '/交易/融资融券/担保划转/第一条/划转数量')
        输入文本(device, '/交易/融资融券/担保划转/第一条/划转数量/修改', 交易数量)
        点击(device, '/底部确定/确定按钮')
        点击(device, '/交易/融资融券/担保划转/划转')
        sleep(2)
        点击(device, '/交易/融资融券/担保划转/确定')
        sleep(3)
        预期结果 = '已提交'
        实际结果 = 获取文本信息(device, '/交易/融资融券/担保划转/结果页面/信息')
        错误校验点 = 非空校验(device, 预期结果, 实际结果)
        结果 = 预期结果 in 实际结果
        点击(device, '/交易/融资融券/合约延期/合约延期页面/完成按钮')
    else:
        print('无可划转担保品')
    return 结果, 错误校验点


