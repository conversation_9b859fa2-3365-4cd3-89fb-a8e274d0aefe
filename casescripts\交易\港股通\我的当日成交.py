from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('交易/港股通/我的当日成交/我的当日成交')
def _483829571daeec448fea3a16c4da31ab(device, 账号='60000361', 交易密码='123123', 通信密码='123123'):
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/港股通')
    目录校验(device, '二', '港股通', '/页面标题')
    点击(device, '/交易/普通交易/港股通/港股通首页/我的当日成交')
    try:
        实际结果 = 获取文本信息(device, '/交易/普通交易/港股通/港股通首页/我的当日成交/第一条名称')
    except:
        实际结果 = 获取控件对象(device, '/交易/普通交易/港股通/港股通首页/我的当日成交/无记录')
    if 实际结果 == '':
        返回(device)
        sleep(3)
        点击(device, '/交易/普通交易/港股通/港股通首页/我的当日成交')
        try:
            实际结果 = 获取文本信息(device, '/交易/普通交易/港股通/港股通首页/我的当日成交/第一条名称')
        except:
            实际结果 = 获取控件对象(device, '/交易/普通交易/港股通/港股通首页/我的当日成交/无记录')
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


