from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('交易/普通交易/场内基金/场内申购')
def _c38ad246228e78fd3a4ad6dc1032776b(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119', 证券代码='160225', 申购金额='1000', 预期结果='成功'):
    错误校验点 = ''
    # 业务交易数据 = 获取业务交易信息('交易/普通交易/场内基金/场内申购')
    # 证券代码 = 业务交易数据['证券代码']
    # 初始化
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/普通交易')
    点击(device, '/交易/普通交易/普通交易首页/更多')
    目录校验(device, '二', '股票交易', '/页面标题')
    点击(device, '/交易/普通交易/更多/更多交易页面/场内基金')
    点击(device, '/交易/普通交易/更多/更多交易页面/场内申购')
    输入文本(device, '/交易/普通交易/更多/场内基金/场内申购页面/请输入基金代码', 证券代码)
    输入文本(device, '/交易/普通交易/更多/场内基金/场内申购页面/申购金额', 申购金额)
    可用资金1 = 获取文本信息(device, '/交易/普通交易/更多/场内基金/可用资金')
    点击(device, '/交易/普通交易/更多/场内基金/场内申购页面/确定按钮')
    点击(device, '/交易/普通交易/更多/场内基金/场内申购页面/场内基金申购确定')
    实际结果 = 获取文本信息(device, '/交易/普通交易/更多/场内基金/场内申购页面/提示信息')
    错误校验点 = 非空校验(device, 预期结果, 实际结果)
    结果 = 预期结果 in 实际结果
    if 结果:
        委托合同号 = 截取合同号(device, 实际结果)
        点击(device, '/交易/普通交易/更多/场内基金/场内申购页面/提示确定')
        输入文本(device, '/交易/普通交易/更多/场内基金/场内申购页面/请输入基金代码', 证券代码)
        输入文本(device, '/交易/普通交易/更多/场内基金/场内申购页面/申购金额', 申购金额)
        # 输入文本('/交易/普通交易/更多/场内基金/场内申购页面/申购金额', 申购金额)
        可用资金2 = 获取文本信息(device, '/交易/普通交易/更多/场内基金/可用资金')
        结果 = 可用资金2 < 可用资金1
        错误校验点 = '之前可用资金为%s,买卖后可用资金为%s' % (可用资金1, 可用资金2)
        if 结果:
            返回(device)
            返回(device)
            点击(device, '/交易/普通交易/普通交易首页/撤单按钮')
            点击(device, '/交易/普通交易/交易页面/委托')
            sleep(3)
            点击(device, '/交易/基金交易/查询委托/当日委托/委托列表第一条记录的业务名称')
            while 结果:
                合同编号 = 获取文本信息(device, '/交易/普通交易/交易/委托/第一条记录/委托明细/合同编号')
                if 合同编号 != 委托合同号:
                    点击(device, '/交易/普通交易/交易/委托/第一条记录/委托明细/下一条')
                    try:
                        最后提示 = 获取文本信息(device, '/交易/普通交易/买入页面/系统提示')
                        print(最后提示)
                        结果 = False
                        错误校验点 = 非空校验(device, '合同编号：%s' % 委托合同号, '获取不到')
                        点击(device, '/提示确认/确认按钮')
                    except:
                        pass
                else:
                    交易代码 = 获取文本信息(device, '/交易/普通交易/交易/委托/第一条记录/委托明细/证券代码')
                    委托数量 = 获取文本信息(device, '/交易/普通交易/交易/委托/第一条记录/委托明细/委托数量')
                    结果 = 交易代码 == 证券代码 and 委托数量 == 申购金额
                    错误校验点 = 非空校验(device, '代码：%s，数量：%s' % (证券代码, 申购金额),
                                      '代码：%s，数量：%s' % (交易代码, 委托数量))
                    点击(device, '/交易/普通交易/撤单/撤单页面/撤单按钮')
                    点击(device, '/交易/普通交易/撤单/撤单页面/确定按钮')
                    break
    return 结果, 错误校验点


@casescript('交易/普通交易/场内基金/场内认购')
def _339052b1d19acf6c3ed485c5ef79090a(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119', 证券代码='515053', 认购金额='100', 预期结果='认购成功'):
    # 初始化
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    # ---------------------------------------------------------#
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/普通交易')
    点击(device, '/交易/普通交易/普通交易首页/更多')
    目录校验(device, '二', '股票交易', '/页面标题')
    点击(device, '/交易/普通交易/更多/更多交易页面/场内基金')
    点击(device, '/交易/普通交易/更多/更多交易页面/场内认购')
    输入文本(device, '/交易/普通交易/更多/场内基金/场内认购页面/请输入基金代码', 证券代码)
    输入文本(device, '/交易/普通交易/更多/场内基金/场内认购页面/认购金额', 认购金额)
    点击(device, '/交易/普通交易/更多/场内基金/场内认购页面/确定按钮')
    sleep(3)
    点击(device, '/交易/普通交易/基金交易/紫金基金/基金购买/合同签署页面/同意勾选框')
    sleep(5)
    点击(device, '/交易/普通交易/基金交易/紫金基金/基金购买/确定')
    sleep(5)
    #点不上去
    点击(device, '/交易/普通交易/基金交易/紫金基金/基金购买/确定认购')
    实际结果 = 获取文本信息(device, '/交易/普通交易/更多/场内基金/场内认购页面/提示信息')
    错误校验点 = 非空校验(device, 预期结果, 实际结果)
    结果 = 预期结果 in 实际结果
    return 结果, 错误校验点


@casescript('交易/普通交易/场内基金/场内赎回')
def _2c2919c7959607fc9026be6633ca17dc(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119', 证券代码='160225', 赎回份额='100', 预期结果='成功'):
    错误校验点 = ''
    # 业务交易数据 = 获取业务交易信息('交易/普通交易/场内基金/场内赎回')
    # 证券代码 = 业务交易数据['证券代码']
    # 初始化
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/普通交易')
    点击(device, '/交易/普通交易/普通交易首页/更多')
    目录校验(device, '二', '股票交易', '/页面标题')
    点击(device, '/交易/普通交易/更多/更多交易页面/场内基金')
    点击(device, '/交易/普通交易/更多/更多交易页面/场内赎回')
    输入文本(device, '/交易/普通交易/更多/场内基金/场内赎回页面/请输入基金代码', 证券代码)
    输入文本(device, '/交易/普通交易/更多/场内基金/场内赎回页面/赎回份额', 赎回份额)
    可用份额1 = 获取文本信息(device, '/交易/普通交易/更多/场内基金/可用资金')
    点击(device, '/交易/普通交易/更多/场内基金/场内赎回页面/确定按钮')
    点击(device, '/交易/普通交易/更多/场内基金/场内赎回页面/场内基金赎回确定')
    实际结果 = 获取文本信息(device, '/交易/普通交易/更多/场内基金/场内申购页面/提示信息')
    错误校验点 = 非空校验(device, 预期结果, 实际结果)
    结果 = 预期结果 in 实际结果
    if 结果:
        委托合同号 = 截取合同号(device, 实际结果)
        点击(device, '/交易/普通交易/更多/场内基金/场内赎回页面/提示确定')
        输入文本(device, '/交易/普通交易/更多/场内基金/场内申购页面/请输入基金代码', 证券代码)
        输入文本(device, '/交易/普通交易/更多/场内基金/场内申购页面/申购金额', 赎回份额)
        # 输入文本('/交易/普通交易/更多/场内基金/场内申购页面/申购金额', 申购金额)
        可用份额2 = 获取文本信息(device, '/交易/普通交易/更多/场内基金/可用资金')
        结果 = 可用份额2 < 可用份额1
        错误校验点 = '之前可用份额为%s,买卖后可用份额为%s' % (可用份额1, 可用份额2)
        if 结果:
            返回(device)
            返回(device)
            点击(device, '/交易/普通交易/普通交易首页/撤单按钮')
            点击(device, '/交易/普通交易/交易页面/委托')
            sleep(3)
            点击(device, '/交易/基金交易/查询委托/当日委托/委托列表第一条记录的业务名称')
            while 结果:
                合同编号 = 获取文本信息(device, '/交易/普通交易/交易/委托/第一条记录/委托明细/合同编号')
                if 合同编号 != 委托合同号:
                    点击(device, '/交易/普通交易/交易/委托/第一条记录/委托明细/下一条')
                    try:
                        最后提示 = 获取文本信息(device, '/交易/普通交易/买入页面/系统提示')
                        print(最后提示)
                        结果 = False
                        错误校验点 = 非空校验(device, '合同编号：%s' % 委托合同号, '获取不到')
                        点击(device, '/提示确认/确认按钮')
                    except:
                        pass
                else:
                    交易代码 = 获取文本信息(device, '/交易/普通交易/交易/委托/第一条记录/委托明细/证券代码')
                    委托数量 = 获取文本信息(device, '/交易/普通交易/交易/委托/第一条记录/委托明细/委托数量')
                    结果 = 交易代码 == 证券代码 and 委托数量 == 赎回份额
                    错误校验点 = 非空校验(device, '代码：%s，数量：%s' % (证券代码, 赎回份额),
                                      '代码：%s，数量：%s' % (交易代码, 委托数量))
                    点击(device, '/交易/普通交易/撤单/撤单页面/撤单按钮')
                    点击(device, '/交易/普通交易/撤单/撤单页面/确定按钮')
                    break
    return 结果, 错误校验点


