from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('我的/我的业务/账户管理/辅助资金账号新增/进入辅助资金账号新增')
def _9c116f1343babd4a306ec1e87e475523(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # 初始化
    登录前页面处理(device)
    print("dskfhdsfjdskljfkdsljfkldsjkl")
    点击(device, '/底部导航栏/我的界面跳转按钮')
    上滑(device)
    点击(device, '/底部导航栏/理财界面跳转按钮')
    点击(device, '/底部导航栏/我的界面跳转按钮')
    目录校验(device, '一', '我的', '/我的/选中状态')
    点击(device, '/我的/我的业务')
    目录校验(device, '二', '办业务', '/我的/我的业务/页面标题')
    上滑(device, 3000)
    点击(device, "/我的/我的业务/问一问")
    返回(device)
    sleep(2)
    点击(device, "/我的/我的业务/账户管理/辅助资金账号新增")
    登录(device, 账号, 交易密码, 通信密码)
    source = device.appium.driver.page_source
    print("source::", source)
    sleep(3)
    if "当前非新增辅助资金账户业务受理时间，请于以上时间段进行办理" in source:
        结果 = True
    else:
        # 交易时间内的检查未做
        raise Exception("用例未完成")
    # time.sleep(50)
    # 点击('/理财/90天以下')
    # 实际结果 = 获取文本信息('/理财/90天以下/第一款产品名称')
    # print(实际结果)
    # if 实际结果:
    #     结果 = True
    # else:
    #     结果 = False
    # 全局变量.错误校验点 = 为空校验()
    # 结果校验(结果)


