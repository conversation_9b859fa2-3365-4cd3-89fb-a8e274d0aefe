from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('行情/市场/沪深/开采辅助/开采辅助')
def _780bb0bfba7a70d481d83205b42b1957(device):
    登录前页面处理(device)
    点击(device, '/底部导航栏/行情界面跳转按钮')
    目录校验(device, '一', '行情', '/行情/选中状态')
    点击(device, '/行情/市场/市场标题栏')
    目录校验(device, '二', '沪深', '/行情/更多/选中状态')
    滑动控件至屏幕内(device, '/行情/市场/沪深/热门板块更多')
    sleep(3)
    点击(device, '/行情/市场/沪深/热门板块更多')
    滑动控件至屏幕内(device, '/行情/市场/沪深/开采辅助')
    点击(device, '/行情/市场/沪深/开采辅助')
    sleep(3)
    点击(device, '/行情/市场/沪深/第一个名称')
    买入金额 = 获取文本信息(device, '/行情/市场/沪深/第一个名称/买入金额')
    结果 = 买入金额 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


