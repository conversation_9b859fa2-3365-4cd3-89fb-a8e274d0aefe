from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('理财/灵活收益/灵活收益_主体/灵活收益_主体')
def _720707db74c8af3502994f5483451bdb(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    登录前页面处理(device)
    理财登陆(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '理财', '/理财/选中状态')
    sleep(2)
    指定位置左滑(device, '/理财/理财首页/本金保障')
    点击(device, '/理财/理财首页/灵活收益')
    目录校验(device, '二', '灵活收益', '/页面标题')
    实际结果 = 获取控件对象(device, '/理财/理财首页/灵活收益/无记录')
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


