import re
import sys
import uuid

from libs.adb import *

from casescripts.default import *

device_list = get_adb_devices()

if not len(device_list):
    print('无连接的设备')
    exit()

device = LocalDevice(device_list[0])
# device.appium_port = 4723

print(device)


def run(name):
    print("START ================ %s ===========" % name)
    runcase(device, name)
    print("FINISH ================ %s ===========" % name)
    pass


def run_all(path):
    if os.path.isdir(path):
        for file in os.listdir(path):
            fullpath = '%s/%s' % (path, file)
            run_all(fullpath)
    else:
        with open(path, encoding='utf-8') as f:
            data = f.read()
            lists = re.findall(r"@casescript\('(.+)'\)", data)
        length = max(len(name.split('/')[-1]) for name in lists)
        for _id, name in enumerate(lists):
            s = '%s %s' % (_id + 1, name.split('/')[-1])
            print(s, end=' ' * (length - len(s) + 5))
            if _id % 3 == 2:
                print()
        print()
        while True:
            print('u:上一级，q:退出, a: 执行所有脚本')
            v = input('请选择：')
            if v == 'u':
                path = os.path.basename(path)
                return list_path(path)
            elif v == 'q':
                sys.exit()
            elif v == 'a':
                run_all(path)
            elif 0 < int(v) <= len(lists):
                fullpath = lists[int(v) - 1]
                run(fullpath)
                return run_all(path)
            else:
                print('输入不合法')


def list_path(path):
    file_list = []
    for file in os.listdir(path):
        if file[0] == '_':
            continue
        fullpath = '%s/%s' % (path, file)
        if os.path.isdir(fullpath):
            file_list.append(fullpath)
    for file in os.listdir(path):
        if file[0] == '_':
            continue
        fullpath = '%s/%s' % (path, file)
        if os.path.isfile(fullpath):
            file_list.append(fullpath)

    length = max(len(os.path.basename(name)) for name in file_list)
    for _id, name in enumerate(file_list):
        s = '%s %s' % (_id + 1, os.path.basename(name))
        print(s, end=' ' * (length - len(s) + 5))
        if _id % 3 == 2:
            print()
    print()
    print('u:上一级，q:退出')
    while True:
        v = input('请选择：')
        if v == 'u':
            path = os.path.abspath('%s/../' % path).replace('\\', '/')
            if 'casescripts' not in path:
                path = './casescripts'
            return list_path(path)
        elif v == 'q':
            sys.exit()
        elif 0 < int(v) <= len(file_list):
            fullpath = file_list[int(v) - 1]
            if os.path.isdir(fullpath):
                return list_path(fullpath)
            run_all(fullpath)
            return
        else:
            print('输入不合法')


if __name__ == '__main__':
    if len(sys.argv) > 1:
        name = sys.argv[1]
        device_list = get_adb_devices()
        L = [threading.Thread(target=runcase, args=(LocalDevice(d), name)) for d in device_list]
        for l in L:
            l.start()
            sleep(5)
    else:
        list_path('./casescripts')
