from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('行情/市场/沪深/形态雷达/近30日K线/第一相似度个股')
def _0cee1f67970c3bbfdfb7bb8e44dc8a42(device):
    登录前页面处理(device)
    点击(device, '/底部导航栏/行情界面跳转按钮')
    目录校验(device, '一', '行情', '/行情/选中状态')
    点击(device, '/行情/市场/市场标题栏')
    目录校验(device, '二', '沪深', '/行情/更多/选中状态')
    控件左滑(device, '/行情/市场/沪深/智能投顾栏目')
    点击(device, '/行情/市场/沪深/形态雷达/形态雷达按钮')
    结果 = ''
    if 获取控件对象(device, '/行情/市场/沪深/形态雷达/形态雷达按钮/确定按钮'):
        点击(device, '/行情/市场/沪深/形态雷达/形态雷达按钮/确定按钮')
    循环滑动(device, '/行情/市场/沪深/形态雷达/30日K线/首条相似K线股票','上')
    sleep(3)
    首条相似K线股票 = 获取文本信息(device, '/行情/市场/沪深/形态雷达/30日K线/首条相似K线股票')
    print("首条相似K线股票:::", 首条相似K线股票)
    结果 = 首条相似K线股票 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


