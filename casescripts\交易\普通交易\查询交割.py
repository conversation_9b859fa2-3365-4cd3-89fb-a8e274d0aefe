from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('交易/普通交易/查询交割/查询交割')
def _2b52d75b3ee4a50362ee3506a091764b(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # 初始化
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/普通交易')
    点击(device, '/交易/普通交易/普通交易首页/更多')
    目录校验(device, '二', '股票交易', '/页面标题')
    点击(device, '/交易/普通交易/更多/更多交易页面/查询交割')
    点击(device, '/交易/普通交易/更多/查询交割/查询按钮')
    if 获取控件对象(device, '/交易/普通交易/更多/查询交割/资金明细列表第一条记录'):
        实际结果 = 获取文本信息(device, '/交易/普通交易/更多/查询交割/资金明细列表第一条记录')
    else:
        实际结果 = 获取控件对象(device, '/交易/普通交易/更多/查询交割/无记录')
    错误校验点 = 为空校验(device)
    结果 = 实际结果 != ''
    return 结果, 错误校验点


