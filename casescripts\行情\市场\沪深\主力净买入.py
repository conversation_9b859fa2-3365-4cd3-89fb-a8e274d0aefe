from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('行情/市场/沪深/主力净买入/首只个股净买入金额')
def _a41b8240c1e86fbec4e745931d9e843c(device):
    登录前页面处理(device)
    点击(device, '/底部导航栏/行情界面跳转按钮')
    目录校验(device, '一', '行情', '/行情/选中状态')
    点击(device, '/行情/市场/市场标题栏')
    目录校验(device, '二', '沪深', '/行情/更多/选中状态')
    滑动控件至屏幕内(device, '/行情/市场/沪深/主力净买入/首只个股净买入金额')
    首只个股净买入金额 = 获取文本信息(device, '/行情/市场/沪深/主力净买入/首只个股净买入金额')
    结果 = 首只个股净买入金额 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


