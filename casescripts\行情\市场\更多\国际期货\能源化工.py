from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('行情/市场/更多/国际期货/能源化工/获取NYMEX轻质原油价格')
def _094f343f8a0614fc58b46be372c83667(device):
    登录前页面处理(device)
    点击(device, '/底部导航栏/行情界面跳转按钮')
    目录校验(device, '一', '行情', '/行情/选中状态')
    点击(device, '/行情/市场/市场标题栏')
    控件左滑(device, '/行情/市场/沪深按钮标签')
    点击(device, '/行情/市场/更多/更多标签')
    目录校验(device, '二', '更多', '/行情/更多/选中状态')
    滑动控件至屏幕内(device, '/行情/市场/更多/国际期货/能源化工/能源化工标签')
    点击(device, '/行情/市场/更多/国际期货/能源化工/能源化工标签')
    获取COMEX黄金价格 = 获取文本信息(device, '/行情/市场/更多/国际期货/能源化工/获取NYMEX轻质原油价格')
    结果 = 获取COMEX黄金价格 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


