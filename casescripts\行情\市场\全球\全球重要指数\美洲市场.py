from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('行情/市场/全球/全球重要指数/美洲市场/巴西BOVESPA')
def _bbf1bfee9bcd70068c9364b9123a71c8(device):
    登录前页面处理(device)
    点击(device, '/底部导航栏/行情界面跳转按钮')
    目录校验(device, '一', '行情', '/行情/选中状态')
    点击(device, '/行情/市场/市场标题栏')
    控件左滑(device, '/行情/市场/沪深按钮标签')
    点击(device, '/行情/市场/全球/全球标签')
    目录校验(device, '二', '全球', '/行情/更多/选中状态')
    点击(device, '/行情/市场/全球/全球重要指数/更多')
    sleep(2)
    闭市 = 获取文本信息(device, '/行情/市场/全球/全球重要指数/更多/美洲市场/巴西BOVESPA/是否闭市')
    点击(device, '/行情/市场/全球/全球重要指数/更多/美洲市场/巴西BOVESPA')
    sleep(5)
    实际结果 = 获取文本信息(device, '/行情/市场/股指/股指期货/成分品种校验/价格')
    错误校验点 = 为空校验(device)
    # 结果 = 实际结果 != '' and 实际结果 != '--'
    结果 = 实际结果 != '' and (实际结果 != '--' or 闭市 == '闭市')
    return 结果, 错误校验点


@casescript('行情/市场/全球/全球重要指数/美洲市场/巴西BRX50')
def _99db9167a76c6b7b73b14cb5dfdb91b4(device):
    登录前页面处理(device)
    点击(device, '/底部导航栏/行情界面跳转按钮')
    目录校验(device, '一', '行情', '/行情/选中状态')
    点击(device, '/行情/市场/市场标题栏')
    控件左滑(device, '/行情/市场/沪深按钮标签')
    点击(device, '/行情/市场/全球/全球标签')
    目录校验(device, '二', '全球', '/行情/更多/选中状态')
    点击(device, '/行情/市场/全球/全球重要指数/更多')
    闭市 = 获取文本信息(device, '/行情/市场/全球/全球重要指数/更多/美洲市场/巴西BRX50/是否闭市')
    sleep(2)
    点击(device, '/行情/市场/全球/全球重要指数/更多/美洲市场/巴西BRX50')
    sleep(5)
    实际结果 = 获取文本信息(device, '/行情/市场/股指/股指期货/成分品种校验/价格')
    错误校验点 = 为空校验(device)
    # 结果 = 实际结果 != '' and 实际结果 != '--'
    结果 = 实际结果 != '' and (实际结果 != '--' or 闭市 == '闭市')
    return 结果, 错误校验点


@casescript('行情/市场/全球/全球重要指数/美洲市场/标普100')
def _7f99f83f855e7ee8469588586a3f9ed7(device):
    登录前页面处理(device)
    点击(device, '/底部导航栏/行情界面跳转按钮')
    目录校验(device, '一', '行情', '/行情/选中状态')
    点击(device, '/行情/市场/市场标题栏')
    控件左滑(device, '/行情/市场/沪深按钮标签')
    点击(device, '/行情/市场/全球/全球标签')
    目录校验(device, '二', '全球', '/行情/更多/选中状态')
    点击(device, '/行情/市场/全球/全球重要指数/更多')
    sleep(2)
    闭市 = 获取文本信息(device, '/行情/市场/全球/全球重要指数/更多/美洲市场/标普100/是否闭市')
    点击(device, '/行情/市场/全球/全球重要指数/更多/美洲市场/标普100')
    sleep(5)
    实际结果 = 获取文本信息(device, '/行情/市场/股指/股指期货/成分品种校验/价格')
    错误校验点 = 为空校验(device)
    # 结果 = 实际结果 != '' and 实际结果 != '--'
    结果 = 实际结果 != '' and (实际结果 != '--' or 闭市 == '闭市')
    return 结果, 错误校验点


@casescript('行情/市场/全球/全球重要指数/美洲市场/纳斯达克100')
def _d19a8075fa1a9c5e33e1a74471d4a505(device):
    登录前页面处理(device)
    点击(device, '/底部导航栏/行情界面跳转按钮')
    目录校验(device, '一', '行情', '/行情/选中状态')
    点击(device, '/行情/市场/市场标题栏')
    控件左滑(device, '/行情/市场/沪深按钮标签')
    点击(device, '/行情/市场/全球/全球标签')
    目录校验(device, '二', '全球', '/行情/更多/选中状态')
    点击(device, '/行情/市场/全球/全球重要指数/更多')
    sleep(2)
    闭市 = 获取文本信息(device, '/行情/市场/全球/全球重要指数/更多/美洲市场/纳斯达克100/是否闭市')
    点击(device, '/行情/市场/全球/全球重要指数/更多/美洲市场/纳斯达克100')
    sleep(5)
    实际结果 = 获取文本信息(device, '/行情/市场/股指/股指期货/成分品种校验/价格')
    错误校验点 = 为空校验(device)
    # 结果 = 实际结果 != '' and 实际结果 != '--'
    结果 = 实际结果 != '' and (实际结果 != '--' or 闭市 == '闭市')
    return 结果, 错误校验点


@casescript('行情/市场/全球/全球重要指数/美洲市场/道琼斯运输')
def _da13cdbf5d477ee636140f1a933fa276(device):
    登录前页面处理(device)
    点击(device, '/底部导航栏/行情界面跳转按钮')
    目录校验(device, '一', '行情', '/行情/选中状态')
    点击(device, '/行情/市场/市场标题栏')
    控件左滑(device, '/行情/市场/沪深按钮标签')
    点击(device, '/行情/市场/全球/全球标签')
    目录校验(device, '二', '全球', '/行情/更多/选中状态')
    点击(device, '/行情/市场/全球/全球重要指数/更多')
    sleep(2)
    闭市 = 获取文本信息(device, '/行情/市场/全球/全球重要指数/更多/美洲市场/道琼斯运输/是否闭市')
    点击(device, '/行情/市场/全球/全球重要指数/更多/美洲市场/道琼斯运输')
    sleep(5)
    实际结果 = 获取文本信息(device, '/行情/市场/股指/股指期货/成分品种校验/价格')
    错误校验点 = 为空校验(device)
    # 结果 = 实际结果 != '' and 实际结果 != '--'
    结果 = 实际结果 != '' and (实际结果 != '--' or 闭市 == '闭市')
    return 结果, 错误校验点


