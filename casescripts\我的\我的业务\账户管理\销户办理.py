from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('我的/我的业务/账户管理/销户办理/进入销户办理')
def _e8921907f7ca922fa4f4f09985f66521(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # 初始化
    登录前页面处理(device)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    上滑(device)
    点击(device, '/底部导航栏/理财界面跳转按钮')
    点击(device, '/底部导航栏/我的界面跳转按钮')
    目录校验(device, '一', '我的', '/我的/选中状态')
    点击(device, '/我的/我的业务')
    目录校验(device, '二', '办业务', '/我的/我的业务/页面标题')
    上滑(device, 3000)
    点击(device, "/我的/我的业务/问一问")
    返回(device)
    sleep(2)
    点击(device, "/我的/我的业务/账户管理/销户办理")
    登录(device, 账号, 交易密码, 通信密码)
    标题 = 获取文本信息(device, '/我的/我的业务/账户管理/销户办理/标题')
    if 标题 != '':
        结果 = True
    else:
        结果 = False
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


