from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('理财/公募基金/基金严选/基金严选')
def _8fa5f9884f26e36d525fe7cd2ac4b809(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # 初始化
    登录前页面处理(device)
    理财登陆(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '理财', '/理财/选中状态')
    点击(device, '/理财/理财首页/公募基金')
    目录校验(device, '二', '基金专区', '/页面标题')
    sleep(3)
    点击(device, '/理财/理财首页/公募基金/基金严选')
    实际结果 = 获取文本信息(device, '/理财/理财首页/公募基金/基金严选/第一条记录收益率')
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


