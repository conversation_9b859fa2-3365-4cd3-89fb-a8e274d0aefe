#!/bin/bash

# ANSI 颜色代码，用于彩色输出
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
DARK_RED='\033[0;31m'
NC='\033[0m' # 无颜色

# 打印章节标题
print_section() {
    echo -e "\n=========================== $1 ===========================\n"
}

# 切换到目标目录
print_section "切换到目录"
cd ~/src/ControlSystem || { echo -e "${DARK_RED}无法切换到目录 ~/src/ControlSystem${NC}"; exit 1; }
echo "当前目录：$(pwd)"

# 激活虚拟环境
print_section "激活虚拟环境"
if [ -f "env/bin/activate" ]; then
    . env/bin/activate
    echo "虚拟环境已激活"
else
    echo -e "${DARK_RED}虚拟环境激活脚本不存在${NC}"
    exit 1
fi



# 清理主控的log/video
print_section "清理主控的log/video"
root_dir="log/video"

# 获取一个月前的时间戳
one_month_ago=$(date -d "-30 days" +%s)

# 确认根目录是否存在
if [ ! -d "$root_dir" ]; then
  echo "目录 $root_dir 不存在。"
  exit 1
fi

echo "正在清理 $root_dir 中超过一个月的 .log 文件..."

# 查找并删除超过一个月的 .log 文件，并统计删除的文件数
deleted_count=$(find "$root_dir" -type f -name "*.log" -printf '%T@ %p\n' \
  | awk -v timestamp="$one_month_ago" '$1 < timestamp {print $2}' \
  | xargs rm -v 2>/dev/null | wc -l)

echo "共删除了 $deleted_count 个文件。"




# 清理主控的log/video_cache
print_section "清理主控的log/video_cache"
# 设置目标目录为用户主目录下的某个路径
target_dir="~/src/ControlSystem/log/video_cache"

# 获取一个月前的时间戳
one_month_ago=$(date -d "-30 days" +%s)

# 将波浪号展开为完整的主目录路径
expanded_dir=$(eval echo "$target_dir")

# 确认目录是否存在
if [ ! -d "$expanded_dir" ]; then
  echo "目录 $expanded_dir 不存在。"
  exit 1
fi

echo "正在清理 $expanded_dir 中超过一个月的 .mp4 文件..."

# 查找并删除超过一个月的 .mp4 文件
find "$expanded_dir" -type f -name "*.mp4" | while read -r file; do
  file_time=$(stat --format=%Y "$file")

  if [ "$file_time" -lt "$one_month_ago" ]; then
    echo "正在删除旧文件: $file"
    rm "$file"
  fi
done

echo "清理完成。"


# 获取当前连接的设备
get_connected_devices() {
    adb devices | awk 'NR>1 {print $1}' | grep -v '^$'
}

# 检查未授权设备
check_unauthorized_devices() {
    unauthorized_devices=$(adb devices | grep "unauthorized" | awk '{print $1}')
    if [ -n "$unauthorized_devices" ]; then
        echo -e "${YELLOW}发现未授权设备：${NC}"
        echo "$unauthorized_devices"
        exit 1
    fi
}

# 清理设备上的视频缓存
print_section "清理视频缓存"
for var in $(get_connected_devices); do
    echo "处理设备：$var"
    output=$(adb -s "$var" shell rm -rf /sdcard/zfkj_screen_record/video 2>&1)

    if echo "$output" | grep -q "No such file or directory"; then
        echo "设备 $var 无视频缓存"
    else
        echo "设备 $var 的视频缓存已清理"
    fi
done

# 顺序重启设备
print_section "重启手机"
original_devices=$(get_connected_devices)
original_count=$(echo "$original_devices" | wc -w)
echo -e "初始设备列表${GREEN}：$original_count 台设备：${NC}"
echo "$original_devices"

for var in $original_devices; do
    adb -s "$var" shell reboot
    echo "设备 $var 已重启"
done

# 等待设备重新连接
print_section "等待设备重新连接"
timeout=180 # 180s
elapsed_time=0
interval=5

while [ "$elapsed_time" -lt "$timeout" ]; do
    current_devices=$(get_connected_devices)
    current_count=$(echo "$current_devices" | wc -w)

    check_unauthorized_devices

    # 对比序列号列表而不是仅比较数量
    new_devices=$(comm -13 <(echo "$original_devices" | tr ' ' '\n' | sort) <(echo "$current_devices" | tr ' ' '\n' | sort))
    lost_devices=$(comm -23 <(echo "$original_devices" | tr ' ' '\n' | sort) <(echo "$current_devices" | tr ' ' '\n' | sort))

    if [ -z "$lost_devices" ]; then
        echo -e "所有设备已重新连接${GREEN}：$current_count 台设备：${NC}"
        echo "$current_devices"
        if [ -n "$new_devices" ]; then
            echo -e "${YELLOW}有增加的设备：${NC}"
            echo "$new_devices"
        fi
        break
    fi

    echo "设备仍在重启中，当前已连接设备：$current_count 台，正在等待..."
    sleep "$interval"
    elapsed_time=$((elapsed_time + interval))
done

if [ "$elapsed_time" -ge "$timeout" ]; then
    # 列出未重新连接的设备
    unconnected_devices=$(comm -23 <(echo "$original_devices" | tr ' ' '\n' | sort) <(echo "$current_devices" | tr ' ' '\n' | sort))
    unconnected_count=$(echo "$unconnected_devices" | wc -l)
    echo -e "${DARK_RED}重启超时，以下设备未能连接：${NC}${unconnected_count} 台"
    echo -e "${DARK_RED}$unconnected_devices${NC}"
    exit 1
fi

# 重新拉服务
print_section "重新加载服务"
if [ -f "reload.sh" ]; then
    ./reload.sh
    echo "服务已成功重新加载"
else
    echo -e "${DARK_RED}reload.sh 脚本不存在${NC}"
    exit 1
fi

print_section "所有操作完成"
