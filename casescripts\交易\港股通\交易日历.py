from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('交易/港股通/交易日历/交易日历')
def _580d8680cde08415af188dfd49ac45d1(device, 账号='60000361', 交易密码='123123', 通信密码='123123', 预期结果='交易日'):
    登录前页面处理(device)
    # time.sleep(20)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/港股通')
    目录校验(device, '二', '港股通', '/页面标题')
    点击(device, '/交易/普通交易/港股通/港股通首页/交易日历')
    if 获取控件对象(device, '/交易/普通交易/港股通/港股通首页/交易日历/交易日检验') == '':
        返回(device)
        点击(device, '/交易/普通交易/港股通/港股通首页/交易日历')
    实际结果 = 获取文本信息(device, '/交易/普通交易/港股通/港股通首页/交易日历/交易日检验')
    错误校验点 = 非空校验(device, 预期结果, 实际结果)
    结果 = 预期结果 in 实际结果
    return 结果, 错误校验点


