from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('首页/热点/本金保障/本金保障')
def _ed73e69387fabcbcb04ec4f27f1c1a2b(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # 初始化
    登录前页面处理(device)
    点击(device, '/底部导航栏/首页界面跳转按钮')
    目录校验(device, '一', '首页', '/首页/选中状态')
    点击(device, '/首页/热点')
    目录校验(device, '二', '热点', '/首页/热点/选中状态')
    点击(device, '/首页/热点/向右点击按钮')
    点击(device, '/首页/热点/本金保障')
    登录(device, 账号, 交易密码, 通信密码)
    sleep(3)
    文本 = 获取文本信息(device, '/理财/理财首页/本金保障/当前在售数量')
    实际结果 = 截取合同号(device, 文本)
    print('------------', 实际结果)
    结果 = int(实际结果) >= 0
    错误校验点 = 类型校验(device, int, 实际结果)
    return 结果, 错误校验点


