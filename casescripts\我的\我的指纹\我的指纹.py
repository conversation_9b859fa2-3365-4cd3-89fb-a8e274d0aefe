from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('我的/我的指纹/我的指纹/我的指纹')
def _1d2fbda54a3679b06be4fa127a68417c(device, 账号='666630296771', 交易密码='124369', 通信密码='159753', 预期结果='指纹登录'):
    登录前页面处理(device)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    目录校验(device, '一', '我的', '/我的/选中状态')
    点击(device, '/我的/我的指纹')
    目录校验(device, '二', '指纹登录', '/页面标题')
    实际结果 = 获取文本信息(device, '/页面标题')
    # 全局变量.错误校验点 = 为空校验()
    错误校验点 = 非空校验(device, 预期结果, 实际结果)
    结果 = 预期结果 in 实际结果
    return 结果, 错误校验点


