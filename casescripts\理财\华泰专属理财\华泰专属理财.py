from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('理财/华泰专属理财/华泰专属理财/华泰专属理财')
def _a4fca87721807b8ccbd64316d972cf77(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # 初始化
    # 登录前页面处理()
    理财登陆(device, 账号, 交易密码, 通信密码)
    # time.sleep(5)
    滑动控件至屏幕内_理财(device, '/理财/快捷功能/华泰专属理财')
    目录校验(device, '一', '理财', '/理财/选中状态')
    点击(device, '/理财/快捷功能/华泰专属理财')
    目录校验(device, '二', '华泰专属理财', '/页面标题')
    sleep(2)
    实际结果 = 获取文本信息(device, '/理财/快捷功能/华泰专属理财/在售产品数')
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


