from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('交易/融资融券/还款还券/还券_买券还券')
def _a9d3626d6fdeec36090eb4e95eadebb3(device, 账号='01066033', 交易密码='123123', 通信密码='123123', 证券代码='010303', 交易数量='100', 预期结果='成功'):
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/融资融券')
    点击(device, '/交易/融资融券/融资融券首页/还款还券按钮')
    点击(device, '/交易/融资融券/还款还券/还款还券首页/买券还券')
    目录校验(device, '二', '买券还券', '/页面标题')
    点击(device, '/交易/融资融券/还款还券/买券还券页面/请输入股票代码框')
    输入文本(device, '/交易/融资融券/还款还券/买券还券页面/请输入股票代码框', 证券代码)
    点击(device, '/交易/融资融券/还款还券/买券还券页面/跌停价')
    点击(device, '/交易/融资融券/增加数量')
    # 点击('/交易/融资融券/还款还券/买券还券页面/买入数量输入框')
    # 输入文本('/交易/融资融券/还款还券/买券还券页面/买入数量输入框', 交易数量)
    点击(device, '/交易/融资融券/还款还券/买券还券页面/买入按钮')
    点击(device, '/交易/融资融券/还款还券/买券还券页面/买入确定按钮')
    实际结果 = 获取文本信息(device, '/交易/融资融券/还款还券/买券还券页面/系统提示')
    错误校验点 = 非空校验(device, 预期结果, 实际结果)
    结果 = 预期结果 in 实际结果
    点击(device, '/交易/融资融券/还款还券/买券还券页面/委托确定按钮')
    print(结果)
    return 结果, 错误校验点


@casescript('交易/融资融券/还款还券/还券_直接还券')
def _dae4ffa0a72802a11d7210ab571daed4(device, 账号='01000440', 交易密码='123123', 通信密码='123123', 证券代码='600123', 交易数量='100', 预期结果='成功'):
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/融资融券')
    点击(device, '/交易/融资融券/融资融券首页/还款还券按钮')
    点击(device, '/交易/融资融券/还款还券/还款还券首页/直接还券')
    目录校验(device, '二', '直接还券', '/页面标题')
    点击(device, '/交易/融资融券/还款还券/直接还券页面/股票代码输入框')
    输入文本(device, '/交易/融资融券/还款还券/直接还券页面/股票代码输入框', 证券代码)
    # 点击( '/交易/融资融券/融资买入页面/跌停价')
    点击(device, '/交易/融资融券/还款还券/直接还券页面/还券数量输入框')
    输入文本(device, '/交易/融资融券/还款还券/直接还券页面/还券数量输入框', 交易数量)
    点击(device, '/交易/融资融券/还款还券/直接还券页面/还券按钮')
    点击(device, '/交易/融资融券/还款还券/直接还券页面/确定按钮')
    实际结果 = 获取文本信息(device, '/交易/融资融券/还款还券/直接还款页面/系统提示')
    错误校验点 = 非空校验(device, 预期结果, 实际结果)
    结果 = 预期结果 in 实际结果
    点击(device, '/交易/融资融券/还款还券/直接还款页面/系统提示确定按钮')
    print(结果)
    return 结果, 错误校验点


@casescript('交易/融资融券/还款还券/还款_卖券还款')
def _af9e852a999e0e7884fc7cdb82d9c057(device, 账号='58008098', 交易密码='123123', 通信密码='123123', 证券代码='600536', 交易数量='100', 预期结果='委托成功'):
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/融资融券')
    点击(device, '/交易/融资融券/融资融券首页/还款还券按钮')
    点击(device, '/交易/融资融券/还款还券/还款还券首页/卖券还款')
    目录校验(device, '二', '卖券还款', '/页面标题')
    输入文本(device, '/交易/融资融券/还款还券/卖券还款页面/请输入股票代码框', 证券代码)
    点击(device, '/交易/融资融券/增加数量')
    # 输入文本('/交易/融资融券/还款还券/卖券还款页面/卖出数量输入框', 交易数量)
    点击(device, '/交易/融资融券/还款还券/卖券还款页面/卖出按钮')
    点击(device, '/交易/融资融券/还款还券/卖券还款页面/卖出确定按钮')
    实际结果 = 获取文本信息(device, '/交易/融资融券/还款还券/卖券还款页面/系统提示')
    错误校验点 = 非空校验(device, 预期结果, 实际结果)
    结果 = 预期结果 in 实际结果
    点击(device, '/交易/融资融券/还款还券/卖券还款页面/委托确定按钮')
    return 结果, 错误校验点


@casescript('交易/融资融券/还款还券/还款_直接还款')
def _8309d4d02b3fd5c86fe71639fa4361a1(device, 账号='30002187', 交易密码='123123', 通信密码='123123', 还款金额='100', 预期结果='直接还券'):
    # time.sleep(15)
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/融资融券')
    点击(device, '/交易/融资融券/融资融券首页/还款还券按钮')
    点击(device, '/交易/融资融券/还款还券/还款还券首页/直接还款')
    目录校验(device, '二', '直接还款', '/页面标题')
    实际结果 = 获取文本信息(device, '/页面标题')
    结果 = 预期结果 in 实际结果
    # 待还款金额1 = 获取文本信息('/交易/融资融券/还款还券/直接还款页面/待还款金额文本')
    # print(待还款金额1)
    # 输入文本('/交易/融资融券/还款还券/直接还款页面/金额输入框', 还款金额)
    # 点击('/交易/融资融券/还款还券/直接还款页面/还款按钮')
    # 点击('/交易/融资融券/还款还券/直接还款页面/确认还款按钮')
    # time.sleep(5)
    # 待还款金额2 = 获取文本信息('/交易/融资融券/还款还券/直接还款页面/待还款金额文本')
    # print(待还款金额2)
    # 实际结果 = float(待还款金额1.replace(',', '')) - float(待还款金额2.replace(',', ''))
    # print(实际结果)
    # 预期结果 = float(还款金额)
    # 全局变量.错误校验点 = 非空校验(预期结果, 实际结果)
    错误校验点 = 为空校验(device)
    # 结果 = 预期结果 == 实际结果
    return 结果, 错误校验点


