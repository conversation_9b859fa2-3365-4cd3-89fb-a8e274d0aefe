# 手机初次接入系统，绑定eu、录入平台的时候执行

import subprocess
from libs.adb import get_adb_devices,LocalDevice,register_device


# 获取usb连线的手机设备
device_list = get_adb_devices()


# 设备录入平台，开启adb tcpip
for device_id in device_list:
    if ':' not in device_id:
        # 平台注册设备，保存信息
        local_device = LocalDevice(device_id,eu_adb_port='5555')
        register_device(local_device)

        # 创建设备与eu本地端口 监听关系
        p_c = subprocess.Popen('adb -s %s tcpip %s' % (device_id, 5555), shell=True, stdout=subprocess.PIPE)
        d_c = p_c.stdout.readline()
