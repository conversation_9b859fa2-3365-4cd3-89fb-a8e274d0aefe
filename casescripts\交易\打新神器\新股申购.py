from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('交易/打新神器/新股申购/新股申购')
def _489029d379898ea2ac336ff99105a96e(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    错误校验点 = ''
    # 真实账号
    # 初始化
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/打新神器')
    目录校验(device, '二', '打新神器', '/页面标题')
    点击(device, '/交易/普通交易/打新神器/打新神器页面/新股申购')
    sleep(2)
    try:
        获取文本信息(device, '/交易/普通交易/打新神器/新股申购页面/新股申购提示')
    except:
        print('==============无额度==================')
        try:
            实际结果 = 获取文本信息(device, '/交易/普通交易/打新神器/新股申购页面/无申购额度提示')
        except:
            print('==============无新股申购==================')
            实际结果 = 获取文本信息(device, '/交易/普通交易/打新神器/新股申购页面/无新股可申购')
            预期结果 = '今日无新股可申购'
            结果 = 预期结果 in 实际结果
            错误校验点 = 非空校验(device, 预期结果, 实际结果)
            return 结果, 错误校验点
        else:
            预期结果 = '您暂无申购额度'
            结果 = 预期结果 in 实际结果
            错误校验点 = 非空校验(device, 预期结果, 实际结果)
            return 结果, 错误校验点
    else:
        print('===============有额度===================')
        上海市场 = 获取文本信息(device, '/交易/普通交易/打新神器/新股申购页面/申购额度/上海市场')
        深圳市场 = 获取文本信息(device, '/交易/普通交易/打新神器/新股申购页面/申购额度/深圳市场')
        科创板 = 获取文本信息(device, '/交易/普通交易/打新神器/新股申购页面/申购额度/科创板')
        print('上海市场:%s,深圳市场:%s,科创板:%s' % (上海市场, 深圳市场, 科创板))
        try:
            获取文本信息(device, '/交易/普通交易/打新神器/新股申购页面/新股/第一支')
        except:
            错误校验点 = '无可申购新股'
            return False, 错误校验点
        else:
            print('有可申购新股！')
            点击(device, '/交易/普通交易/打新神器/新股申购页面/新股/第一支')
            # try:
            #     获取控件对象('/交易/普通交易/打新神器/新股申购页面/新股/第一支/立即申购')
            # except:
            #     返回()
            # else:
            #     print('有立即申购按钮！')
            #     点击('/交易/普通交易/打新神器/新股申购页面/新股/第一支/立即申购')
            返回(device)
            sleep(1)
            点击(device, '/交易/普通交易/打新神器/新股申购页面/立即申购按钮')
            点击(device, '/交易/普通交易/打新神器/新股申购确认页面/确定申购')
            预期结果 = '已成功提交'
            实际结果 = ''
            try:
                获取文本信息(device, '/交易/普通交易/打新神器/新股申购结果页面/成功提交')
            except:
                实际结果 = 获取文本信息(device, '/交易/普通交易/打新神器/新股申购结果页面/失败原因')
                index = 实际结果.find('：')
                print('下标值：', index)
                失败原因 = 实际结果[index:]
                错误校验点 = 失败原因
                点击(device, '/交易/普通交易/打新神器/新股申购结果页面/确定按钮')
            else:
                实际结果 = 获取文本信息(device, '/交易/普通交易/打新神器/新股申购结果页面/成功提交')
                错误校验点 = 非空校验(device, 预期结果, 实际结果)
                点击(device, '/交易/普通交易/打新神器/新股申购结果页面/完成申购按钮')
        结果 = 预期结果 in 实际结果
        return 结果, 错误校验点


@casescript('交易/打新神器/新股申购/新股申购确认_我的历史申购')
def _b575e88f143b81416decb28daa48acba(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    错误校验点 = ''
    # 真实账号
    # 初始化
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/打新神器')
    目录校验(device, '二', '打新神器', '/页面标题')
    点击(device, '/交易/普通交易/打新神器/打新神器页面/新股申购')
    sleep(2)
    if 获取控件对象(device, '/交易/普通交易/打新神器/新股申购页面/新股申购提示') == '':
        try:
            实际结果 = 获取文本信息(device, '/交易/普通交易/打新神器/新股申购页面/无申购额度提示')
            print('==============无额度==================')
            预期结果 = '您暂无申购额度'
            结果 = 预期结果 in 实际结果
            错误校验点 = 非空校验(device, 预期结果, 实际结果)
            return 结果, 错误校验点
        except:
            实际结果 = 获取文本信息(device, '/交易/普通交易/打新神器/新股申购页面/无新股可申购')
            print('==============无新股申购==================')
            预期结果 = '今日无新股可申购'
            结果 = 预期结果 in 实际结果
            错误校验点 = 非空校验(device, 预期结果, 实际结果)
            return 结果, 错误校验点
    else:
        print('===============有额度===================')
        if 获取控件对象(device, '/交易/普通交易/打新神器/新股申购页面/新股/第一支') == '':
            错误校验点 = '无可申购新股'
            return False, 错误校验点
        else:
            print('有可申购新股！')
            点击(device, '/交易/普通交易/打新神器/新股申购页面/立即申购按钮')
            点击(device, '/交易/普通交易/打新神器/新股申购确认页面/确定申购')
            预期结果 = ''
            实际结果 = ''
            if 获取控件对象(device, '/交易/普通交易/打新神器/新股申购结果页面/成功提交') == '':
                print('申购失败！')
                点击(device, '/交易/普通交易/打新神器/新股申购结果页面/确定按钮')
            else:
                print('申购成功！')
                点击(device, '/交易/普通交易/打新神器/新股申购结果页面/完成申购按钮')
            print('进入我的申购页面！')
            try:
                点击(device, '/交易/普通交易/打新神器/新股申购/我的申购页面/第一条记录')
            except:
                错误校验点 = '无申购记录'
                return False, 错误校验点
            else:
                print('进入申购详情')
                申购代码 = 获取文本信息(device, '/交易/普通交易/打新神器/新股申购/我的申购/申购详情页面/申购代码')
                print('申购股为：%s' % 申购代码)
                结果 = 申购代码 != ''
                错误校验点 = 为空校验(device)
                return 结果, 错误校验点


@casescript('交易/打新神器/新股申购/新股申购确认_我的申购')
def _c909f9c55703a937f5327fe142bbe0ce(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    错误校验点 = ''
    # 真实账号
    # 初始化
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/打新神器')
    目录校验(device, '二', '打新神器', '/页面标题')
    点击(device, '/交易/普通交易/打新神器/打新神器页面/新股申购')
    sleep(2)
    if 获取控件对象(device, '/交易/普通交易/打新神器/新股申购页面/新股申购提示') == '':
        try:
            实际结果 = 获取文本信息(device, '/交易/普通交易/打新神器/新股申购页面/无申购额度提示')
            print('==============无额度==================')
            预期结果 = '您暂无申购额度'
            结果 = 预期结果 in 实际结果
            错误校验点 = 非空校验(device, 预期结果, 实际结果)
            return 结果, 错误校验点
        except:
            实际结果 = 获取文本信息(device, '/交易/普通交易/打新神器/新股申购页面/无新股可申购')
            print('==============无新股申购==================')
            预期结果 = '今日无新股可申购'
            结果 = 预期结果 in 实际结果
            错误校验点 = 非空校验(device, 预期结果, 实际结果)
            return 结果, 错误校验点
    else:
        print('===============有额度===================')
        if 获取控件对象(device, '/交易/普通交易/打新神器/新股申购页面/新股/第一支') == '':
            错误校验点 = '无可申购新股'
            return False, 错误校验点
        else:
            print('有可申购新股！')
            点击(device, '/交易/普通交易/打新神器/新股申购页面/立即申购按钮')
            点击(device, '/交易/普通交易/打新神器/新股申购确认页面/确定申购')
            预期结果 = ''
            实际结果 = ''
            if 获取控件对象(device, '/交易/普通交易/打新神器/新股申购结果页面/成功提交') == '':
                print('申购失败！')
                点击(device, '/交易/普通交易/打新神器/新股申购结果页面/确定按钮')
            else:
                print('申购成功！')
                点击(device, '/交易/普通交易/打新神器/新股申购结果页面/完成申购按钮')
            print('进入我的申购页面！')
            try:
                点击(device, '/交易/普通交易/打新神器/新股申购/我的申购页面/第一条记录')
            except:
                错误校验点 = '无申购记录'
                return False, 错误校验点
            else:
                print('进入申购详情')
                申购代码 = 获取文本信息(device, '/交易/普通交易/打新神器/新股申购/我的申购/申购详情页面/申购代码')
                print('申购股为：%s' % 申购代码)
                结果 = 申购代码 != ''
                错误校验点 = 为空校验(device)
                return 结果, 错误校验点


