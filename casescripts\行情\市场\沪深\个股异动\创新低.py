from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('行情/市场/沪深/个股异动/创新低/首只股票代码')
def _a9422c8b2f98f018cae75dfc143c3f11(device):
    登录前页面处理(device)
    点击(device, '/底部导航栏/行情界面跳转按钮')
    目录校验(device, '一', '行情', '/行情/选中状态')
    点击(device, '/行情/市场/市场标题栏')
    目录校验(device, '二', '沪深', '/行情/更多/选中状态')
    滑动控件至屏幕内(device, '/行情/市场/沪深/个股异动_创新低/创新低标签')
    点击(device, '/行情/市场/沪深/个股异动_创新低/创新低标签')
    首只股票代码 = 获取文本信息(device, '/行情/市场/沪深/个股异动_创新低/首只股票代码')
    结果 = 首只股票代码 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


