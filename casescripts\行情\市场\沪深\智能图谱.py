from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('行情/市场/沪深/智能图谱/个股图谱股票名称和涨跌')
def _5c3e7ab6fbba97acdc21de84a9ac4102(device):
    登录前页面处理(device)
    点击(device, '/底部导航栏/行情界面跳转按钮')
    目录校验(device, '一', '行情', '/行情/选中状态')
    点击(device, '/行情/市场/市场标题栏')
    目录校验(device, '二', '沪深', '/行情/更多/选中状态')
    控件左滑(device, '/行情/市场/沪深/智能投顾栏目')
    点击(device, '/行情/市场/沪深/智能图谱/智能图谱按钮')
    sleep(3)
    超高股力值收益 = 获取文本信息(device, '/行情/市场/沪深/智能图谱/个股图谱股票名称和涨跌')
    print("超高股力值收益:::", 超高股力值收益)
    结果 = 超高股力值收益 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


