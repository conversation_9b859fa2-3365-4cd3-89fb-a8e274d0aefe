from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('行情/市场/沪深/泰牛智投/我的订阅')
def _4e99c19128c530899674296ce0fb5fe0(device):
    登录前页面处理(device)
    点击(device, '/底部导航栏/行情界面跳转按钮')
    目录校验(device, '一', '行情', '/行情/选中状态')
    点击(device, '/行情/市场/市场标题栏')
    目录校验(device, '二', '沪深', '/行情/更多/选中状态')
    点击(device, '/行情/市场/沪深/泰牛智投')
    登录前页面处理(device)
    sleep(3)
    点击(device, '/首页/热点/泰牛智投/我的订阅')
    sleep(3)
    实际结果 = 获取文本信息(device, '/首页/热点/泰牛智投/我的订阅/订阅信息')
    print(实际结果)
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


@casescript('行情/市场/沪深/泰牛智投/精选策略')
def _da7f92f3ca171d1dd41ca37efa5bd33b(device):
    登录前页面处理(device)
    点击(device, '/底部导航栏/行情界面跳转按钮')
    目录校验(device, '一', '行情', '/行情/选中状态')
    点击(device, '/行情/市场/市场标题栏')
    目录校验(device, '二', '沪深', '/行情/更多/选中状态')
    点击(device, '/行情/市场/沪深/泰牛智投')
    登录前页面处理(device)
    实际结果 = 获取文本信息(device, '/行情/市场/沪深/泰牛智投/标题')
    print(实际结果)
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


