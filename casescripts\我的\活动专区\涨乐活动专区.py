from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('我的/活动专区/涨乐活动专区/涨乐活动专区')
def _eb5b86272038c0e493f0d05fb5ea868d(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # 初始化
    登录前页面处理(device)
    # 理财登陆(账号, 交易密码, 通信密码)
    # 交易登录(账号, 交易密码, 通信密码)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    目录校验(device, '一', '我的', '/我的/选中状态')
    # time.sleep(3)
    # 上滑()
    循环滑动(device, '/我的/活动专区', "上")
    点击(device, '/我的/活动专区')
    目录校验(device, '二', '活动专区', '/我的/活动专区/页面标题')
    # 点击('/我的/组合/查看牛人组合')
    # time.sleep(5)
    # 循环滑动('/我的/组合/查看牛人组合/牛人讨论区','上')
    实际结果 = 获取控件对象(device, '/我的/活动专区/第一个活动')
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


