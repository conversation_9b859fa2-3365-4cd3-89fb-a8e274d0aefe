from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('交易/紫金理财/紫金赎回/紫金赎回')
def _e9c54fa8146b42517f6c7eadf4af59b9(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119', 证券代码='940018', 赎回份额='10'):
    错误校验点 = ''
    # print(1)
    # 初始化
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/紫金理财')
    目录校验(device, '二', '紫金理财', '/页面标题')
    点击(device, '/交易/普通交易/紫金理财/紫金理财首页/紫金赎回')
    sleep(3)
    点击(device, '/交易/普通交易/基金交易/紫金基金/基金赎回页面/基金代码')
    try:
        点击(device, '/交易/普通交易/紫金理财/紫金赎回/选择产品/第一条')
    except:
        输入文本(device, '/交易/普通交易/基金交易/紫金基金/基金赎回页面/基金代码', 证券代码)
    输入文本(device, '/交易/普通交易/基金交易/紫金基金/基金赎回页面/赎回份额', 赎回份额)
    点击(device, '/交易/普通交易/紫金理财/紫金赎回/紫金产品赎回页面/立即赎回按钮')
    try:
        点击(device, '/确认/确认按钮')
        实际结果 = 获取文本信息(device, '/交易/普通交易/紫金理财/紫金赎回/结果页面/状态')
        预期结果 = '已提交'
        结果 = 预期结果 in 实际结果
        if 结果:
            点击(device, '/交易/普通交易/撤销委托/赎回撤单')
            点击(device, '/底部确定/确定按钮')
            实际结果 = 获取文本信息(device, '/交易/普通交易/更多/网络投票/投票查询/投票记录详细页面/提示框内容')
            预期结果 = '成功'
            结果 = 预期结果 in 实际结果
            错误校验点 = 非空校验(device, 预期结果, 实际结果)
        else:
            错误校验点 = 获取文本信息(device, '/交易/普通交易/紫金理财/紫金赎回/结果页面/失败原因')
            点击(device, '/交易/普通交易/紫金理财/紫金赎回/结果页面/完成')
    except:
        实际结果 = 获取文本信息(device, '/交易/普通交易/紫金理财/紫金赎回/提示信息')
        点击(device, '/交易/普通交易/紫金理财/紫金赎回/紫金产品赎回页面/立即赎回按钮/确认按钮')
        结果 = 实际结果 != ''
        错误校验点 = 为空校验(device)
    return 结果, 错误校验点


