from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('行情/市场/全球/全球重要指数/欧洲市场/富时希腊')
def _84bf5b85d19c852ee417cc0a27996367(device):
    登录前页面处理(device)
    点击(device, '/底部导航栏/行情界面跳转按钮')
    目录校验(device, '一', '行情', '/行情/选中状态')
    点击(device, '/行情/市场/市场标题栏')
    控件左滑(device, '/行情/市场/沪深按钮标签')
    点击(device, '/行情/市场/全球/全球标签')
    目录校验(device, '二', '全球', '/行情/更多/选中状态')
    点击(device, '/行情/市场/全球/全球重要指数/更多')
    sleep(2)
    滑动控件至屏幕内(device, '/行情/市场/全球/全球重要指数/更多/欧洲市场/富时希腊')
    闭市 = 获取文本信息(device, '/行情/市场/全球/全球重要指数/更多/欧洲市场/富时希腊/是否闭市')
    点击(device, '/行情/市场/全球/全球重要指数/更多/欧洲市场/富时希腊')
    sleep(5)
    实际结果 = 获取文本信息(device, '/行情/市场/股指/股指期货/成分品种校验/价格')
    错误校验点 = 为空校验(device)
    结果 = 实际结果 != '' and (实际结果 != '--' or 闭市 == '闭市')
    return 结果, 错误校验点


@casescript('行情/市场/全球/全球重要指数/欧洲市场/富时意大利MIB')
def _6af0acda4a584fdf31e729e7ad604b69(device):
    登录前页面处理(device)
    点击(device, '/底部导航栏/行情界面跳转按钮')
    目录校验(device, '一', '行情', '/行情/选中状态')
    点击(device, '/行情/市场/市场标题栏')
    控件左滑(device, '/行情/市场/沪深按钮标签')
    点击(device, '/行情/市场/全球/全球标签')
    目录校验(device, '二', '全球', '/行情/更多/选中状态')
    点击(device, '/行情/市场/全球/全球重要指数/更多')
    sleep(2)
    滑动控件至屏幕内(device, '/行情/市场/全球/全球重要指数/更多/欧洲市场/富时意大利MIB')
    闭市 = 获取文本信息(device, '/行情/市场/全球/全球重要指数/更多/欧洲市场/富时意大利MIB/是否闭市')
    点击(device, '/行情/市场/全球/全球重要指数/更多/欧洲市场/富时意大利MIB')
    sleep(5)
    实际结果 = 获取文本信息(device, '/行情/市场/股指/股指期货/成分品种校验/价格')
    错误校验点 = 为空校验(device)
    # 结果 = 实际结果 != '' and 实际结果 != '--'
    结果 = 实际结果 != '' and (实际结果 != '--' or 闭市 == '闭市')
    return 结果, 错误校验点


@casescript('行情/市场/全球/全球重要指数/欧洲市场/富时挪威30')
def _d5c9fb6bc975cb386d821fd679de8ba4(device):
    登录前页面处理(device)
    点击(device, '/底部导航栏/行情界面跳转按钮')
    目录校验(device, '一', '行情', '/行情/选中状态')
    点击(device, '/行情/市场/市场标题栏')
    控件左滑(device, '/行情/市场/沪深按钮标签')
    点击(device, '/行情/市场/全球/全球标签')
    目录校验(device, '二', '全球', '/行情/更多/选中状态')
    点击(device, '/行情/市场/全球/全球重要指数/更多')
    sleep(2)
    滑动控件至屏幕内(device, '/行情/市场/全球/全球重要指数/更多/欧洲市场/富时挪威30')
    闭市 = 获取文本信息(device, '/行情/市场/全球/全球重要指数/更多/欧洲市场/富时挪威30/是否闭市')
    # 闭市颜色 = 全局变量.Drive.find_element(:class, ‘gettextcolor’).css_value(“color”)
    点击(device, '/行情/市场/全球/全球重要指数/更多/欧洲市场/富时挪威30')
    sleep(5)
    实际结果 = 获取文本信息(device, '/行情/市场/股指/股指期货/成分品种校验/价格')
    错误校验点 = 为空校验(device)
    结果 = 实际结果 != '' and (实际结果 != '--' or 闭市 == '闭市')
    return 结果, 错误校验点


@casescript('行情/市场/全球/全球重要指数/欧洲市场/富时欧洲发达市场')
def _6f56eef469717a44e6a58efd3921bf47(device):
    登录前页面处理(device)
    点击(device, '/底部导航栏/行情界面跳转按钮')
    目录校验(device, '一', '行情', '/行情/选中状态')
    点击(device, '/行情/市场/市场标题栏')
    控件左滑(device, '/行情/市场/沪深按钮标签')
    点击(device, '/行情/市场/全球/全球标签')
    目录校验(device, '二', '全球', '/行情/更多/选中状态')
    点击(device, '/行情/市场/全球/全球重要指数/更多')
    sleep(2)
    滑动控件至屏幕内(device, '/行情/市场/全球/全球重要指数/更多/欧洲市场/富时欧洲发达市场')
    闭市 = 获取文本信息(device, '/行情/市场/全球/全球重要指数/更多/欧洲市场/富时欧洲发达市场/是否闭市')
    点击(device, '/行情/市场/全球/全球重要指数/更多/欧洲市场/富时欧洲发达市场')
    sleep(5)
    实际结果 = 获取文本信息(device, '/行情/市场/股指/股指期货/成分品种校验/价格')
    错误校验点 = 为空校验(device)
    结果 = 实际结果 != '' and (实际结果 != '--' or 闭市 == '闭市')
    return 结果, 错误校验点


@casescript('行情/市场/全球/全球重要指数/欧洲市场/富时瑞典30')
def _9e36d7caac1f8128938c8981c51a72b2(device):
    登录前页面处理(device)
    点击(device, '/底部导航栏/行情界面跳转按钮')
    目录校验(device, '一', '行情', '/行情/选中状态')
    点击(device, '/行情/市场/市场标题栏')
    控件左滑(device, '/行情/市场/沪深按钮标签')
    点击(device, '/行情/市场/全球/全球标签')
    目录校验(device, '二', '全球', '/行情/更多/选中状态')
    点击(device, '/行情/市场/全球/全球重要指数/更多')
    sleep(2)
    滑动控件至屏幕内(device, '/行情/市场/全球/全球重要指数/更多/欧洲市场/富时瑞典30')
    闭市 = 获取文本信息(device, '/行情/市场/全球/全球重要指数/更多/欧洲市场/富时瑞典30/是否闭市')
    点击(device, '/行情/市场/全球/全球重要指数/更多/欧洲市场/富时瑞典30')
    sleep(5)
    实际结果 = 获取文本信息(device, '/行情/市场/股指/股指期货/成分品种校验/价格')
    错误校验点 = 为空校验(device)
    结果 = 实际结果 != '' and (实际结果 != '--' or 闭市 == '闭市')
    return 结果, 错误校验点


@casescript('行情/市场/全球/全球重要指数/欧洲市场/比利时BEL20')
def _ad0d19ff190fcca088c7de6ded99f2aa(device):
    登录前页面处理(device)
    点击(device, '/底部导航栏/行情界面跳转按钮')
    目录校验(device, '一', '行情', '/行情/选中状态')
    点击(device, '/行情/市场/市场标题栏')
    控件左滑(device, '/行情/市场/沪深按钮标签')
    点击(device, '/行情/市场/全球/全球标签')
    目录校验(device, '二', '全球', '/行情/更多/选中状态')
    点击(device, '/行情/市场/全球/全球重要指数/更多')
    sleep(2)
    滑动控件至屏幕内(device, '/行情/市场/全球/全球重要指数/更多/欧洲市场/比利时BEL20')
    闭市 = 获取文本信息(device, '/行情/市场/全球/全球重要指数/更多/欧洲市场/比利时BEL20/是否闭市')
    点击(device, '/行情/市场/全球/全球重要指数/更多/欧洲市场/比利时BEL20')
    sleep(5)
    实际结果 = 获取文本信息(device, '/行情/市场/股指/股指期货/成分品种校验/价格')
    错误校验点 = 为空校验(device)
    结果 = 实际结果 != '' and (实际结果 != '--' or 闭市 == '闭市')
    return 结果, 错误校验点


@casescript('行情/市场/全球/全球重要指数/欧洲市场/法国CAC40')
def _9879885a2b5d68fac9ea0a8abee234f1(device):
    登录前页面处理(device)
    点击(device, '/底部导航栏/行情界面跳转按钮')
    目录校验(device, '一', '行情', '/行情/选中状态')
    点击(device, '/行情/市场/市场标题栏')
    控件左滑(device, '/行情/市场/沪深按钮标签')
    点击(device, '/行情/市场/全球/全球标签')
    目录校验(device, '二', '全球', '/行情/更多/选中状态')
    点击(device, '/行情/市场/全球/全球重要指数/更多')
    sleep(2)
    滑动控件至屏幕内(device, '/行情/市场/全球/全球重要指数/更多/欧洲市场/法国CAC40')
    闭市 = 获取文本信息(device, '/行情/市场/全球/全球重要指数/更多/欧洲市场/法国CAC40/是否闭市')
    点击(device, '/行情/市场/全球/全球重要指数/更多/欧洲市场/法国CAC40')
    sleep(5)
    实际结果 = 获取文本信息(device, '/行情/市场/股指/股指期货/成分品种校验/价格')
    错误校验点 = 为空校验(device)
    结果 = 实际结果 != '' and (实际结果 != '--' or 闭市 == '闭市')
    return 结果, 错误校验点


@casescript('行情/市场/全球/全球重要指数/欧洲市场/荷兰AEX')
def _2e08ac6638a33e346c79489eb5a45607(device):
    登录前页面处理(device)
    点击(device, '/底部导航栏/行情界面跳转按钮')
    目录校验(device, '一', '行情', '/行情/选中状态')
    点击(device, '/行情/市场/市场标题栏')
    控件左滑(device, '/行情/市场/沪深按钮标签')
    点击(device, '/行情/市场/全球/全球标签')
    目录校验(device, '二', '全球', '/行情/更多/选中状态')
    点击(device, '/行情/市场/全球/全球重要指数/更多')
    sleep(2)
    滑动控件至屏幕内(device, '/行情/市场/全球/全球重要指数/更多/欧洲市场/荷兰AEX')
    闭市 = 获取文本信息(device, '/行情/市场/全球/全球重要指数/更多/欧洲市场/荷兰AEX/是否闭市')
    点击(device, '/行情/市场/全球/全球重要指数/更多/欧洲市场/荷兰AEX')
    sleep(5)
    实际结果 = 获取文本信息(device, '/行情/市场/股指/股指期货/成分品种校验/价格')
    错误校验点 = 为空校验(device)
    结果 = 实际结果 != '' and (实际结果 != '--' or 闭市 == '闭市')
    return 结果, 错误校验点


@casescript('行情/市场/全球/全球重要指数/欧洲市场/葡萄牙PSI20')
def _9496097852a15002d7d45ad56bb7cc58(device):
    登录前页面处理(device)
    点击(device, '/底部导航栏/行情界面跳转按钮')
    目录校验(device, '一', '行情', '/行情/选中状态')
    点击(device, '/行情/市场/市场标题栏')
    控件左滑(device, '/行情/市场/沪深按钮标签')
    点击(device, '/行情/市场/全球/全球标签')
    目录校验(device, '二', '全球', '/行情/更多/选中状态')
    点击(device, '/行情/市场/全球/全球重要指数/更多')
    sleep(2)
    滑动控件至屏幕内(device, '/行情/市场/全球/全球重要指数/更多/欧洲市场/葡萄牙PSI20')
    闭市 = 获取文本信息(device, '/行情/市场/全球/全球重要指数/更多/欧洲市场/葡萄牙PSI20/是否闭市')
    点击(device, '/行情/市场/全球/全球重要指数/更多/欧洲市场/葡萄牙PSI20')
    sleep(5)
    实际结果 = 获取文本信息(device, '/行情/市场/股指/股指期货/成分品种校验/价格')
    错误校验点 = 为空校验(device)
    结果 = 实际结果 != '' and (实际结果 != '--' or 闭市 == '闭市')
    return 结果, 错误校验点


