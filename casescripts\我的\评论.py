from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('我的/评论/我的评论')
def _181fc0a91f5bb257c19c099ed6315992(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # 初始化
    登录前页面处理(device)
    # 理财登陆(账号, 交易密码, 通信密码)
    # 交易登录(账号, 交易密码, 通信密码)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    循环滑动(device, '/我的/评论', "上")
    点击(device, '/我的/评论')
    点击(device, '/我的/评论/我的评论')
    try:
        实际结果 = 获取控件对象(device, '/我的/评论/我的评论/无记录')
    except:
        pass
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


@casescript('我的/评论/评论我的')
def _437fc560fc3c8ec25fb8372624a57039(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # 初始化
    登录前页面处理(device)
    # 理财登陆(账号, 交易密码, 通信密码)
    # 交易登录(账号, 交易密码, 通信密码)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    循环滑动(device, '/我的/评论', "上")
    点击(device, '/我的/评论')
    点击(device, '/我的/评论/评论我的')
    try:
        实际结果 = 获取控件对象(device, '/我的/评论/评论我的/无记录')
    except:
        pass
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


