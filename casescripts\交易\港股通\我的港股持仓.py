from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('交易/港股通/我的港股持仓/我的港股持仓')
def _e1bbf9b9e86db8438fa243e302b12a26(device, 账号='60000361', 交易密码='123123', 通信密码='123123'):
    登录前页面处理(device)
    # time.sleep(20)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/港股通')
    目录校验(device, '二', '港股通', '/页面标题')
    点击(device, '/交易/普通交易/港股通/港股通首页/我的港股持仓按钮')
    try:
        实际结果 = 获取文本信息(device, '/交易/普通交易/港股通/港股通首页/我的港股持仓按钮/第一条股票代码')
    except:
        实际结果 = 获取控件对象(device, '/交易/普通交易/港股通/港股通首页/我的港股持仓按钮/无记录')
    错误校验点 = 为空校验(device)
    结果 = 实际结果 != ''
    return 结果, 错误校验点


