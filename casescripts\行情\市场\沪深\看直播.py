from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('行情/市场/沪深/看直播/跳转专家栏目')
def _1bcb2869372ae7e6787cc0355da528a6(device):
    登录前页面处理(device)
    点击(device, '/底部导航栏/行情界面跳转按钮')
    目录校验(device, '一', '行情', '/行情/选中状态')
    点击(device, '/行情/市场/市场标题栏')
    目录校验(device, '二', '沪深', '/行情/更多/选中状态')
    点击(device, '/行情/市场/沪深/看直播/看直播按钮')
    sleep(3)
    专家栏目名称 = 获取文本信息(device, '/行情/市场/沪深/看直播/专家栏目名称')
    print("专家栏目名称:::", 专家栏目名称)
    结果 = 专家栏目名称 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


