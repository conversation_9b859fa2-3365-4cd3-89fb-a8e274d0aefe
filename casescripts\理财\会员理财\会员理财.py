from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('理财/会员理财/会员理财/暂无产品')
def _273743fb073f97ab39a1f81500fe861f(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # 初始化
    登录前页面处理(device)
    理财登陆(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一','理财','/理财/选中状态')
    滑动控件至屏幕内_理财(device, '/理财/首发基金')
    控件左滑_理财(device, '/理财/首发基金')
    点击(device, '/理财/会员理财')
    目录校验(device, '二', '会员专享理财', '/页面标题')
    sleep(3)
    # 上滑()
    try:
        实际结果 = 获取文本信息(device, '/理财/会员理财/U+专享/名称')
        print(实际结果)
    except:
        实际结果 = 获取控件对象(device, '/理财/会员理财/U+专享/无产品')
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


