from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('我的/我的交易账户/服务记录/在线客服')
def _1b268504feadfee9b862b538db5c737e(device, 账号='666630296771', 交易密码='124369', 通信密码='159753'):
    # 初始化
    登录前页面处理(device)
    # 理财登陆(账号, 交易密码, 通信密码)
    交易登录(device, 账号, 交易密码, 通信密码)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    目录校验(device, '一', '我的', '/我的/选中状态')
    循环滑动(device, '/我的/我的交易账户/服务记录', "上")
    点击(device, '/我的/我的交易账户/服务记录')
    目录校验(device, '二', '服务记录', '/页面标题')
    点击(device, '/我的/我的服务记录/在线客服')
    if 获取控件对象(device, '/我的/我的服务记录/在线客服/第一条记录'):
        实际结果 = 获取控件对象(device, '/我的/我的服务记录/在线客服/第一条记录')
    else:
        实际结果 = 获取控件对象(device, '/我的/我的服务记录/在线客服/无记录')
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


@casescript('我的/我的交易账户/服务记录/电话客服')
def _eb54e9f0890adad3e04bfb687766a863(device, 账号='666630296771', 交易密码='124369', 通信密码='159753'):
    # 初始化
    登录前页面处理(device)
    # 理财登陆(账号, 交易密码, 通信密码)
    交易登录(device, 账号, 交易密码, 通信密码)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    目录校验(device, '一', '我的', '/我的/选中状态')
    循环滑动(device, '/我的/我的交易账户/服务记录', "上")
    点击(device, '/我的/我的交易账户/服务记录')
    目录校验(device, '二', '服务记录', '/页面标题')
    点击(device, '/我的/我的服务记录/电话客服')
    if 获取控件对象(device, '/我的/我的服务记录/电话客服/第一条记录'):
        实际结果 = 获取控件对象(device, '/我的/我的服务记录/电话客服/第一条记录')
    else:
        实际结果 = 获取控件对象(device, '/我的/我的服务记录/电话客服/无记录')
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


@casescript('我的/我的交易账户/服务记录/营业网点')
def _b505574f00a8a79a29c2edf75be2d17d(device, 账号='666630296771', 交易密码='124369', 通信密码='159753'):
    # 初始化
    登录前页面处理(device)
    # 理财登陆(账号, 交易密码, 通信密码)
    交易登录(device, 账号, 交易密码, 通信密码)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    目录校验(device, '一', '我的', '/我的/选中状态')
    循环滑动(device, '/我的/我的交易账户/服务记录', "上")
    点击(device, '/我的/我的交易账户/服务记录')
    目录校验(device, '二', '服务记录', '/页面标题')
    点击(device, '/我的/我的服务记录/营业网点')
    if 获取控件对象(device, '/我的/我的服务记录/营业网点/第一条记录'):
        实际结果 = 获取控件对象(device, '/我的/我的服务记录/营业网点/第一条记录')
    else:
        实际结果 = 获取控件对象(device, '/我的/我的服务记录/营业网点/无记录')
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


