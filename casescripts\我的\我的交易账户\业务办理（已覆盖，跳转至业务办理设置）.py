from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('我的/我的交易账户/业务办理（已覆盖，跳转至业务办理设置）/业务办理（已覆盖，跳转至业务办理设置）')
def _7c259dfc9bf490f3ccfbe5ead9e7333f(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119', 预期结果='我的业务办理'):
    # 初始化
    登录前页面处理(device)
    # 理财登陆(账号, 交易密码, 通信密码)
    交易登录(device, 账号, 交易密码, 通信密码)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    目录校验(device, '一', '我的', '/我的/选中状态')
    # time.sleep(3)
    # 上滑()
    循环滑动(device, '/我的/我的交易账户/业务办理', "上")
    点击(device, '/我的/我的交易账户/业务办理')
    目录校验(device, '二', '我的业务办理', '/页面标题')
    实际结果 = 获取文本信息(device, '/我的/我的交易账户/业务办理/标题')
    print(实际结果)
    错误校验点 = 非空校验(device, 预期结果, 实际结果)
    结果 = 实际结果 == 预期结果
    return 结果, 错误校验点


