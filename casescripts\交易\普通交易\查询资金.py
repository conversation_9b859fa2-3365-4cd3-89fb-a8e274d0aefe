from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('交易/普通交易/查询资金/查询资金')
def _216dced77970cf7d1bedf666baf4835f(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # hxf 生产账户
    # 初始化
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/普通交易')
    点击(device, '/交易/普通交易/普通交易首页/查询资金按钮')
    目录校验(device, '二', '查询资金', '/页面标题')
    实际结果 = ''
    if 获取控件对象(device, '/交易/普通交易/查询资金页面/可用资金'):
        实际结果 = 获取文本信息(device, '/交易/普通交易/查询资金页面/可用资金')
    else:
        返回(device)
        点击(device, '/交易/普通交易/普通交易首页/查询资金按钮')
        实际结果 = 获取文本信息(device, '/交易/普通交易/查询资金页面/可用资金')
    错误校验点 = 为空校验(device)
    结果 = 实际结果 != ''
    return 结果, 错误校验点


