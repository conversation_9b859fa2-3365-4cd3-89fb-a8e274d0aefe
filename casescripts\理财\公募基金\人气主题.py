from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('理财/公募基金/人气主题/人气主题')
def _028f639482408c69567394b1b1dd4b6c(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # 初始化
    登录前页面处理(device)
    理财登陆(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一','理财','/理财/选中状态')
    点击(device, '/理财/理财首页/公募基金')
    目录校验(device, '二', '基金专区', '/页面标题')
    # 上滑()
    # 循环滑动('/理财/理财首页/公募基金/人气主题', '上')
    sleep(5)
    try:
        实际结果 = 获取文本信息(device, '/理财/理财首页/公募基金/人气主题/第一条记录')
    except:
        返回(device)
        点击(device, '/理财/理财首页/公募基金')
        sleep(5)
        实际结果 = 获取文本信息(device, '/理财/理财首页/公募基金/人气主题/第一条记录')
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


