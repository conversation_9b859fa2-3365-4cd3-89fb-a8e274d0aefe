from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('理财/稳健收益/灵活期限_天天利B/灵活期限_天天利B')
def _da270ffd91da7da4e2c20cdd98646511(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # 初始化
    登录前页面处理(device)
    理财登陆(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '理财', '/理财/选中状态')
    点击(device, '/理财/理财首页/稳健收益')
    目录校验(device, '二', '稳健收益', '/页面标题')
    sleep(3)
    点击(device, '/理财/理财/稳健收益/灵活期限')
    上滑(device)
    点击(device, '/理财/理财首页/加载检查')
    返回(device)
    点击(device, '/理财/理财/稳健收益/灵活期限/天天利B')
    sleep(3)
    收益率 = 获取文本信息(device, '/理财/货币增强/收益率')
    实际结果 = float(str(收益率).strip('%'))
    print(实际结果)
    结果 = 实际结果 > 0
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


