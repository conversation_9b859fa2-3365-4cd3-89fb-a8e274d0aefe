from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('交易/大宗交易/委托查询/委托查询')
def _34d9d7a26c1ba4189a9184a208b35f2c(device, 账号='01028889', 交易密码='123123', 通信密码='123123', 证券代码='600123'):
    错误校验点 = ''
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    上滑(device)
    sleep(1)
    点击(device, '/交易/普通交易/普通交易首页/其他交易')
    目录校验(device, '二', '其他交易', '/页面标题')
    点击(device, '/交易/普通交易/其他交易/其他交易首页/大宗交易')
    点击(device, '/交易/普通交易/其他交易/其他交易首页/大宗交易_委托查询')
    if 获取控件对象(device, '/交易/普通交易/其他交易/大宗交易/委托查询页面/第一条证券代码'):
        实际结果 = 获取文本信息(device, '/交易/普通交易/其他交易/大宗交易/委托查询页面/第一条证券代码')
        错误校验点 = 非空校验(device, 证券代码, 实际结果)
        结果 = 证券代码 in 实际结果
    else:
        实际结果 = 获取文本信息(device, '/交易/普通交易/其他交易/大宗交易/委托查询页面/无记录')
        错误校验点 = 为空校验(device)
        结果 = 实际结果 != ''
    print(实际结果)
    return 结果, 错误校验点


