from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('首页/热点/理财/跳转到《理财》页面')
def _74fa6367d7cd333128cf0331f31cfa64(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # 初始化
    登录前页面处理(device)
    # time.sleep(15)
    # 交易登录( 账号, 交易密码, 通信密码)
    # ---------------------------------------------------------#
    点击(device, '/底部导航栏/首页界面跳转按钮')
    目录校验(device, '一', '首页', '/首页/选中状态')
    点击(device, '/首页/热点')
    目录校验(device, '二', '热点', '/首页/热点/选中状态')
    点击(device, '/首页/热点/理财')
    sleep(2)
    # 交易登录( 账号, 交易密码, 通信密码)
    实际结果 = 获取文本信息(device, '/首页/热点/理财/判断')
    结果 = 实际结果 == '严选理财'
    错误校验点 = 非空校验(device, "严选理财", 实际结果)
    return 结果, 错误校验点


