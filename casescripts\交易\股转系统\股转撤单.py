from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('交易/股转系统/股转撤单/股转撤单')
def _993db94d2d07a59d23c84f9e808467cc(device, 账号='07072328', 交易密码='123123', 通信密码='123123'):
    错误校验点 = ''
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    上滑(device)
    sleep(1)
    点击(device, '/交易/普通交易/普通交易首页/其他交易')
    目录校验(device, '二', '其他交易', '/页面标题')
    点击(device, '/交易/普通交易/其他交易/其他交易首页/股转系统')
    点击(device, '/交易/普通交易/其他交易/其他交易首页/股转系统_股转撤单')
    try:
        点击(device, '/交易/普通交易/其他交易/其他交易首页/股转系统_股转撤单/第一条记录')
        点击(device, '/交易/普通交易/其他交易/ETF网下/委托撤单页面/撤单')
        点击(device, '/底部确定/确定按钮')
        实际结果 = 获取文本信息(device, '/提示')
        预期结果 = '已提交'
        结果 = 预期结果 in 实际结果
        错误校验点 = 非空校验(device, 预期结果, 实际结果)
    except:
        实际结果 = 获取控件对象(device, '/交易/普通交易/其他交易/其他交易首页/股转系统_委托查询/无记录')
        结果 = 实际结果 != ''
        错误校验点 = 为空校验(device)
    return 结果, 错误校验点


