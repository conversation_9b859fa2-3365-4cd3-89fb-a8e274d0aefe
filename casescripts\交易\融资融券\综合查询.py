from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('交易/融资融券/综合查询/委托与成交_历史委托')
def _23c5fa7a3aa3c0f063bbc726738683b4(device, 账号='30002187', 交易密码='123123', 通信密码='123123'):
    错误校验点 = ''
    # time.sleep(15)
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/融资融券')
    sleep(2)
    上滑(device)
    点击(device, '/交易/普通交易/普通交易首页/普通交易')
    点击(device, '/交易/普通交易/普通交易首页/融资融券')
    sleep(2)
    点击(device, '/交易/融资融券/融资融券首页/综合查询')
    目录校验(device, '二', '查询', '/页面标题')
    点击(device, '/交易/融资融券/综合查询/综合查询首页/历史委托')
    点击(device, '/交易/融资融券/综合查询/查询按钮')
    结果 = False
    sleep(3)
    if 获取控件对象(device, '/交易/融资融券/综合查询/综合查询首页/历史委托/无记录') != '':
        预期结果 = '该期间没有历史委托记录'
        实际结果 = 获取文本信息(device, '/交易/融资融券/综合查询/综合查询首页/历史委托/无记录')
        错误校验点 = 非空校验(device, 预期结果, 实际结果)
        结果 = 实际结果 != 预期结果
    else:
        点击(device, '/交易/融资融券/综合查询/综合查询首页/历史委托/第一条')
        实际结果 = 获取文本信息(device, '/交易/融资融券/综合查询/综合查询首页/历史委托/第一条/委托时间')
        错误校验点 = 为空校验(device)
        结果 = 实际结果 != ''
    return 结果, 错误校验点


@casescript('交易/融资融券/综合查询/委托与成交_历史成交')
def _399e1e7e8438f59e820b7608e0e5d6f2(device, 账号='30002187', 交易密码='123123', 通信密码='123123'):
    错误校验点 = ''
    # time.sleep(15)
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/融资融券')
    sleep(2)
    上滑(device)
    点击(device, '/交易/普通交易/普通交易首页/普通交易')
    点击(device, '/交易/普通交易/普通交易首页/融资融券')
    sleep(2)
    点击(device, '/交易/融资融券/融资融券首页/综合查询')
    目录校验(device, '二', '查询', '/页面标题')
    点击(device, '/交易/融资融券/综合查询/综合查询首页/历史成交')
    点击(device, '/交易/融资融券/综合查询/查询按钮')
    if 获取控件对象(device, '/提示'):
        错误校验点 = 获取文本信息(device, '/提示')
        结果 = False
    else:
        if 获取控件对象(device, '/交易/融资融券/综合查询/综合查询首页/历史委托/无记录') != '':
            实际结果 = 获取文本信息(device, '/交易/融资融券/综合查询/综合查询首页/历史委托/无记录')
        else:
            点击(device, '/交易/融资融券/综合查询/历史委托/第一条')
            实际结果 = 获取文本信息(device, '/交易/融资融券/综合查询/历史委托/第一条/股票名称')
        错误校验点 = 为空校验(device)
        结果 = 实际结果 != ''
    return 结果, 错误校验点


@casescript('交易/融资融券/综合查询/委托与成交_当日委托')
def _e4be9851299c9f7929fff9ea3f96b256(device, 账号='30002187', 交易密码='123123', 通信密码='123123'):
    登录前页面处理(device)
    # time.sleep(15)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/融资融券')
    sleep(2)
    上滑(device)
    点击(device, '/交易/普通交易/普通交易首页/普通交易')
    点击(device, '/交易/普通交易/普通交易首页/融资融券')
    sleep(2)
    点击(device, '/交易/融资融券/融资融券首页/综合查询')
    目录校验(device, '二', '查询', '/页面标题')
    点击(device, '/交易/融资融券/综合查询/综合查询首页/当日委托')
    try:
        实际结果 = 获取文本信息(device, '/交易/融资融券/综合查询/综合查询首页/当日委托/第一条股票名称')
    except:
        实际结果 = 获取控件对象(device, '/交易/融资融券/综合查询/综合查询首页/当日委托/无记录')
    错误校验点 = 为空校验(device)
    结果 = 实际结果 != ''
    print(实际结果)
    return 结果, 错误校验点


@casescript('交易/融资融券/综合查询/委托与成交_当日成交')
def _cac6548c9141f89a11f3bbf3e19e7e26(device, 账号='30002187', 交易密码='123123', 通信密码='123123'):
    # time.sleep(15)
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/融资融券')
    sleep(2)
    上滑(device)
    点击(device, '/交易/普通交易/普通交易首页/普通交易')
    点击(device, '/交易/普通交易/普通交易首页/融资融券')
    sleep(2)
    点击(device, '/交易/融资融券/融资融券首页/综合查询')
    目录校验(device, '二', '查询', '/页面标题')
    点击(device, '/交易/融资融券/综合查询/综合查询首页/当日成交')
    实际结果 = ''
    结果 = False
    try:
        实际结果 = 获取文本信息(device, '/交易/融资融券/综合查询/综合查询首页/当日成交/第一条记录')
        结果 = 实际结果 != ''
    except:
        实际结果 = 获取文本信息(device, '/交易/融资融券/综合查询/综合查询首页/当日成交/无记录')
        预期结果 = '当日成交'
        结果 = 预期结果 in 实际结果
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


@casescript('交易/融资融券/综合查询/流水查询_历史收市负债汇总')
def _2726b04bb3f57957c1f07e5c461f07e2(device, 账号='30002187', 交易密码='123123', 通信密码='123123'):
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/融资融券')
    sleep(2)
    上滑(device)
    点击(device, '/交易/普通交易/普通交易首页/普通交易')
    点击(device, '/交易/普通交易/普通交易首页/融资融券')
    sleep(2)
    点击(device, '/交易/融资融券/融资融券首页/综合查询')
    目录校验(device, '二', '查询', '/页面标题')
    点击(device, '/交易/融资融券/综合查询/综合查询首页/历史收市负债汇总')
    点击(device, '/交易/融资融券/综合查询/查询按钮')
    try:
        实际结果 = 获取文本信息(device, '/交易/融资融券/综合查询/历史收市负债汇总/历史收市负债汇总列表第一条记录')
    except:
        实际结果 = 获取文本信息(device, '/交易/融资融券/综合查询/历史收市负债汇总/无记录')
    错误校验点 = 为空校验(device)
    结果 = 实际结果 != ''
    return 结果, 错误校验点


@casescript('交易/融资融券/综合查询/流水查询_合约变动流水')
def _552fa5d2d2016b7c6337f5cdebacc9b4(device, 账号='30002187', 交易密码='123123', 通信密码='123123'):
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/融资融券')
    sleep(2)
    上滑(device)
    点击(device, '/交易/普通交易/普通交易首页/普通交易')
    点击(device, '/交易/普通交易/普通交易首页/融资融券')
    sleep(2)
    点击(device, '/交易/融资融券/融资融券首页/综合查询')
    目录校验(device, '二', '查询', '/页面标题')
    点击(device, '/交易/融资融券/综合查询/综合查询首页/合约变动流水')
    点击(device, '/交易/融资融券/综合查询/查询按钮')
    try:
        实际结果 = 获取文本信息(device, '/交易/融资融券/综合查询/合约变动流水/流水列表第一条记录')
    except:
        实际结果 = 获取控件对象(device, '/交易/融资融券/综合查询/综合查询首页/当日成交/无记录')
    错误校验点 = 为空校验(device)
    结果 = 实际结果 != ''
    return 结果, 错误校验点


@casescript('交易/融资融券/综合查询/流水查询_当日资金流水')
def _49fe7d67846785e38e126d009fadb92b(device, 账号='30002187', 交易密码='123123', 通信密码='123123'):
    # 初始化
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/融资融券')
    sleep(2)
    上滑(device)
    点击(device, '/交易/普通交易/普通交易首页/普通交易')
    点击(device, '/交易/普通交易/普通交易首页/融资融券')
    sleep(2)
    点击(device, '/交易/融资融券/融资融券首页/综合查询')
    目录校验(device, '二', '查询', '/页面标题')
    点击(device, '/交易/融资融券/综合查询/综合查询首页/当日资金流水')
    # 点击('/交易/融资融券/综合查询/查询按钮')
    try:
        实际结果 = 获取文本信息(device, '/交易/融资融券/综合查询/当日资金流水/当日资金流水列表第一条记录')
    except:
        实际结果 = 获取控件对象(device, '/交易/融资融券/综合查询/综合查询首页/当日成交/无记录')
    错误校验点 = 为空校验(device)
    结果 = 实际结果 != ''
    return 结果, 错误校验点


@casescript('交易/融资融券/综合查询/流水查询_查询交割')
def _6d2d8ab4ce802130547cb824315dffa1(device, 账号='30002187', 交易密码='123123', 通信密码='123123'):
    错误校验点 = ''
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/融资融券')
    sleep(2)
    上滑(device)
    点击(device, '/交易/普通交易/普通交易首页/普通交易')
    点击(device, '/交易/普通交易/普通交易首页/融资融券')
    sleep(2)
    点击(device, '/交易/融资融券/融资融券首页/综合查询')
    目录校验(device, '二', '查询', '/页面标题')
    点击(device, '/交易/融资融券/综合查询/综合查询首页/查询交割')
    点击(device, '/交易/融资融券/综合查询/查询按钮')
    if 获取控件对象(device, '/提示'):
        错误校验点 = 获取文本信息(device, '/提示')
        结果 = False
    else:
        实际结果 = 获取文本信息(device, '/交易/融资融券/综合查询/查询交割/查询交割流水列表第一条记录')
        错误校验点 = 为空校验(device)
        结果 = 实际结果 != ''
    return 结果, 错误校验点


@casescript('交易/融资融券/综合查询/流水查询_资金流水历史')
def _f68198df0590d365def31b239358b914(device, 账号='30002187', 交易密码='123123', 通信密码='123123'):
    错误校验点 = ''
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/融资融券')
    sleep(2)
    上滑(device)
    点击(device, '/交易/普通交易/普通交易首页/普通交易')
    点击(device, '/交易/普通交易/普通交易首页/融资融券')
    sleep(2)
    点击(device, '/交易/融资融券/融资融券首页/综合查询')
    目录校验(device, '二', '查询', '/页面标题')
    点击(device, '/交易/融资融券/综合查询/综合查询首页/资金流水历史')
    点击(device, '/交易/融资融券/综合查询/查询按钮')
    结果 = False
    if 获取控件对象(device, '/提示'):
        实际结果 = 获取文本信息(device, '/提示')
        预期结果 = '该功能禁止在目前系统状态下运行'
        结果 = 预期结果 in 实际结果
        错误校验点 = 非空校验(device, 预期结果, 实际结果)
    else:
        实际结果 = 获取文本信息(device, '/交易/融资融券/综合查询/资金历史/资金历史列表第一条记录')
        错误校验点 = 为空校验(device)
        结果 = 实际结果 != ''
    return 结果, 错误校验点


@casescript('交易/融资融券/综合查询/资产查询_两融总负债')
def _185552303416eccd57ba25f2e4922998(device, 账号='30002187', 交易密码='123123', 通信密码='123123'):
    # 初始化
    登录前页面处理(device)
    sleep(3)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/融资融券')
    sleep(2)
    上滑(device)
    点击(device, '/交易/普通交易/普通交易首页/普通交易')
    点击(device, '/交易/普通交易/普通交易首页/融资融券')
    sleep(2)
    点击(device, '/交易/融资融券/融资融券首页/综合查询')
    目录校验(device, '二', '查询', '/页面标题')
    点击(device, '/交易/融资融券/综合查询/综合查询首页/两融总负债')
    实际结果 = 获取文本信息(device, '/交易/融资融券/综合查询/两融总负债/总负债')
    print('总负债：%s' % (实际结果))
    错误校验点 = 为空校验(device)
    结果 = 实际结果 != ''
    return 结果, 错误校验点


@casescript('交易/融资融券/综合查询/资产查询_查询持仓')
def _63bd56071ef47d36fa71100914226242(device, 账号='30002187', 交易密码='123123', 通信密码='123123'):
    sleep(3)
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/融资融券')
    sleep(2)
    上滑(device)
    点击(device, '/交易/普通交易/普通交易首页/普通交易')
    点击(device, '/交易/普通交易/普通交易首页/融资融券')
    sleep(2)
    点击(device, '/交易/融资融券/融资融券首页/综合查询')
    目录校验(device, '二', '查询', '/页面标题')
    点击(device, '/交易/融资融券/综合查询/综合查询首页/查询持仓')
    try:
        实际结果 = 获取文本信息(device, '/交易/融资融券/综合查询/查询持仓/第一条记录')
    except:
        实际结果 = 获取控件对象(device, '/交易/融资融券/综合查询/综合查询首页/当日成交/无记录')
    print('持仓名称：%s' % (实际结果))
    错误校验点 = 为空校验(device)
    结果 = 实际结果 != ''
    return 结果, 错误校验点


@casescript('交易/融资融券/综合查询/资产查询_查询资产')
def _1361bb5ce83812e559098515d622bed0(device, 账号='30002187', 交易密码='123123', 通信密码='123123'):
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/融资融券')
    sleep(2)
    上滑(device)
    点击(device, '/交易/普通交易/普通交易首页/普通交易')
    点击(device, '/交易/普通交易/普通交易首页/融资融券')
    sleep(2)
    点击(device, '/交易/融资融券/融资融券首页/综合查询')
    目录校验(device, '二', '查询', '/页面标题')
    点击(device, '/交易/融资融券/综合查询/综合查询首页/查询资产')
    担保资产 = 获取文本信息(device, '/交易/融资融券/综合查询/查询资产页面/资产明细/担保资产')
    print('担保资产：%s' % (担保资产))
    错误校验点 = 为空校验(device)
    结果 = 担保资产 != ''
    return 结果, 错误校验点


@casescript('交易/融资融券/综合查询/资产查询_查询资金')
def _0217dbe5da64990945ad21a3b797c84a(device, 账号='30002187', 交易密码='123123', 通信密码='123123'):
    # 初始化
    登录前页面处理(device)
    sleep(3)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/融资融券')
    sleep(2)
    上滑(device)
    点击(device, '/交易/普通交易/普通交易首页/普通交易')
    点击(device, '/交易/普通交易/普通交易首页/融资融券')
    sleep(2)
    点击(device, '/交易/融资融券/融资融券首页/综合查询')
    目录校验(device, '二', '查询', '/页面标题')
    点击(device, '/交易/融资融券/综合查询/综合查询首页/查询资金')
    if 获取文本信息(device, '/交易/融资融券/综合查询/查询资金/现金资产'):
        现金资产 = 获取文本信息(device, '/交易/融资融券/综合查询/查询资金/现金资产')
    else:
        返回(device)
        点击(device, '/交易/融资融券/综合查询/综合查询首页/查询资金')
        现金资产 = 获取文本信息(device, '/交易/融资融券/综合查询/查询资金/现金资产')
    print('现金资产：%s' % (现金资产))
    错误校验点 = 为空校验(device)
    结果 = 现金资产 != ''
    return 结果, 错误校验点


@casescript('交易/融资融券/综合查询/资产查询_融券合约明细')
def _e5ea228a72862f1a0ba0399b0914d19b(device, 账号='30002187', 交易密码='123123', 通信密码='123123'):
    # 初始化
    登录前页面处理(device)
    sleep(3)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/融资融券')
    sleep(2)
    上滑(device)
    点击(device, '/交易/普通交易/普通交易首页/普通交易')
    点击(device, '/交易/普通交易/普通交易首页/融资融券')
    sleep(2)
    点击(device, '/交易/融资融券/融资融券首页/综合查询')
    目录校验(device, '二', '查询', '/页面标题')
    点击(device, '/交易/融资融券/综合查询/综合查询首页/融券合约明细')
    try:
        证券代码 = 获取文本信息(device, '/交易/融资融券/综合查询/融资合约明细/第一个证券代码')
        结果 = 证券代码 != ''
    except:
        实际结果 = 获取控件对象(device, '/交易/融资融券/综合查询/综合查询首页/当日成交/无记录')
        结果 = 实际结果 != ''
    # print('证券代码：%s' % (证券代码))
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


@casescript('交易/融资融券/综合查询/资产查询_融资合约明细')
def _c93d1f19be31be9085ebd27ea2e40b17(device, 账号='30002187', 交易密码='123123', 通信密码='123123'):
    # 初始化
    登录前页面处理(device)
    sleep(3)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/融资融券')
    sleep(2)
    上滑(device)
    点击(device, '/交易/普通交易/普通交易首页/普通交易')
    点击(device, '/交易/普通交易/普通交易首页/融资融券')
    sleep(2)
    点击(device, '/交易/融资融券/融资融券首页/综合查询')
    目录校验(device, '二', '查询', '/页面标题')
    点击(device, '/交易/融资融券/综合查询/综合查询首页/融资合约明细')
    try:
        证券代码 = 获取文本信息(device, '/交易/融资融券/综合查询/融资合约明细/第一个证券代码')
        结果 = 证券代码 != ''
    except:
        实际结果 = 获取控件对象(device, '/交易/融资融券/综合查询/综合查询首页/当日成交/无记录')
        结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


