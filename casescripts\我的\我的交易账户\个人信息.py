from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('我的/我的交易账户/个人信息/个人信息更新')
def _a42afac18caf6c99ad699574818fde7b(device, 账号='666630296771', 交易密码='124369', 通信密码='159753', 居住地址='kkkkkkkk', 手机号码='17749532309'):
    错误校验点 = ''
    # hxf 生产账户
    # 初始化
    登录前页面处理(device)
    # 理财登陆(账号, 交易密码, 通信密码)
    交易登录(device, 账号, 交易密码, 通信密码)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    目录校验(device, '一', '我的', '/我的/选中状态')
    # time.sleep(3)
    # 上滑()
    循环滑动(device, '/我的/我的交易账户/个人信息', "上")
    点击(device, '/我的/我的交易账户/个人信息')
    目录校验(device, '二', '个人信息', '/页面标题')
    上滑(device)
    上滑(device)
    上滑(device)
    点击(device, '/我的/极速开户/在线客服')
    返回(device)
    sleep(3)
    点击(device, '/我的/我的交易账户/个人信息/个人信息更新')
    输入文本(device, '/首页/热点/我的资产/个人信息/个人信息更新/居住地址', 居住地址)
    点击(device, '/首页/热点/我的资产/个人信息/个人信息更新/下一步')
    try:
        获取文本信息(device, '/首页/热点/我的资产/个人信息/个人信息更新/系统提示/加载检查')
    except:
        print('无系统提示')
        pass
    else:
        错误校验点 = 获取文本信息(device, '/首页/热点/我的资产/个人信息/个人信息更新/系统提示/提示信息')
        return False, 错误校验点
        raise NotFindError
    try:
        获取文本信息(device, '/公用/温馨提示/加载检查')
    except:
        print('无温馨提示！')
        pass
    else:
        点击(device, '/公用/温馨提示/我已确认')
        点击(device, '/首页/热点/我的资产/个人信息/个人信息更新/下一步')
    结果 = 获取控件对象(device, '/首页/热点/我的资产/个人信息/个人信息更新/验证/获取验证码') != ''
    错误校验点 = 为空校验(device)
    # 输入文本('/首页/热点/我的资产/个人信息/个人信息更新/验证/手机号码', 手机号码)
    # 点击('/首页/热点/我的资产/个人信息/个人信息更新/验证/获取验证码')
    #
    # time.sleep(5)
    # 验证码 = 获取短信验证码()
    # print('验证码：；', 验证码)
    # 输入文本('/首页/热点/我的资产/个人信息/个人信息更新/验证/输入验证码', 验证码)
    # 点击('/首页/热点/我的资产/个人信息/个人信息更新/验证/确认修改')
    # 预期结果 = '修改成功'
    # 实际结果 = 获取文本信息('/首页/热点/我的资产/个人信息/个人信息更新/结果/修改结果')
    # 点击('/首页/热点/我的资产/个人信息/个人信息更新/结果/确定')
    # 结果 = 预期结果 in 实际结果
    # 全局变量.错误校验点 = 非空校验(预期结果, 实际结果)
    return 结果, 错误校验点


@casescript('我的/我的交易账户/个人信息/手机号码修改')
def _c598a840e5dacc0d696e8af6a12d28e0(device, 账号='666630296771', 交易密码='124369', 通信密码='159753', 居住地址='华泰证券三号楼', 手机号码='13024339187', 预期结果='手机号码修改'):
    # hxf 生产账户
    # 初始化
    登录前页面处理(device)
    # 理财登陆(账号, 交易密码, 通信密码)
    交易登录(device, 账号, 交易密码, 通信密码)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    目录校验(device, '一', '我的', '/我的/选中状态')
    # time.sleep(3)
    # 上滑()
    循环滑动(device, '/我的/我的交易账户/个人信息', "上")
    点击(device, '/我的/我的交易账户/个人信息')
    目录校验(device, '二', '个人信息', '/页面标题')
    上滑(device)
    上滑(device)
    上滑(device)
    点击(device, '/我的/极速开户/在线客服')
    返回(device)
    sleep(3)
    点击(device, '/首页/热点/我的资产/个人信息/手机号码修改')
    实际结果 = 获取文本信息(device, '/首页/热点/我的资产/个人信息/提示信息')
    结果 = 预期结果 in 实际结果
    错误校验点 = 非空校验(device, 预期结果, 实际结果)
    return 结果, 错误校验点


@casescript('我的/我的交易账户/个人信息/手机号码重置')
def _753564d0eab0652cac001056b7e4b872(device, 账号='666630296771', 交易密码='124369', 通信密码='159753', 手机号码='13024339187', 预期结果='手机号码重置'):
    # hxf 生产账户
    # 初始化
    登录前页面处理(device)
    # 理财登陆(账号, 交易密码, 通信密码)
    交易登录(device, 账号, 交易密码, 通信密码)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    目录校验(device, '一', '我的', '/我的/选中状态')
    # time.sleep(3)
    # 上滑()
    循环滑动(device, '/我的/我的交易账户/个人信息', "上")
    点击(device, '/我的/我的交易账户/个人信息')
    目录校验(device, '二', '个人信息', '/页面标题')
    上滑(device)
    上滑(device)
    上滑(device)
    点击(device, '/我的/极速开户/在线客服')
    返回(device)
    sleep(3)
    点击(device, '/首页/热点/我的资产/个人信息/手机号码重置')
    实际结果 = 获取文本信息(device, '/首页/热点/我的资产/个人信息/提示信息')
    结果 = 预期结果 in 实际结果
    错误校验点 = 非空校验(device, 预期结果, 实际结果)
    return 结果, 错误校验点


@casescript('我的/我的交易账户/个人信息/账户信息（已覆盖，跳转至账户信息）')
def _0a6995c7cb15453fc2cad8442e106d83(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # 初始化
    登录前页面处理(device)
    # 理财登陆(账号, 交易密码, 通信密码)
    交易登录(device, 账号, 交易密码, 通信密码)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    目录校验(device, '一', '我的', '/我的/选中状态')
    # time.sleep(3)
    # 上滑()
    循环滑动(device, '/我的/我的交易账户/个人信息', "上")
    点击(device, '/我的/我的交易账户/个人信息')
    目录校验(device, '二', '个人信息', '/页面标题')
    上滑(device)
    上滑(device)
    上滑(device)
    点击(device, '/我的/极速开户/在线客服')
    返回(device)
    sleep(3)
    点击(device, '/首页/热点/我的资产/个人信息/账户信息')
    实际结果 = 获取文本信息(device, '/首页/热点/我的资产/个人信息/账户信息/资金账户')
    结果 = 实际结果 == 账号
    错误校验点 = 非空校验(device, 账号, 实际结果)
    return 结果, 错误校验点


