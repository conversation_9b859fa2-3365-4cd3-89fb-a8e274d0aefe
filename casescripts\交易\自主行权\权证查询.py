from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('交易/自主行权/权证查询/权证查询')
def _92e00cecbfb4020ebaafa8233473a680(device, 账号='10022785', 交易密码='123123', 通信密码='123123'):
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    上滑(device)
    sleep(1)
    点击(device, '/交易/普通交易/普通交易首页/其他交易')
    目录校验(device, '二', '其他交易', '/页面标题')
    点击(device, '/交易/普通交易/其他交易/其他交易首页/自主行权')
    点击(device, '/交易/普通交易/其他交易/其他交易首页/自主行权_权证查询')
    try:
        实际结果 = 获取文本信息(device, '/交易/普通交易/其他交易/其他交易首页/自主行权_权证查询/第一个交易市场')
    except:
        实际结果 = 获取文本信息(device, '/交易/普通交易/其他交易/其他交易首页/自主行权_权证查询/无记录')
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


