from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('交易/港股通/港股通汇率、余额查询/港股通汇率余额查询')
def _95b0f0ebda4ac919c5f183f626cb762c(device, 账号='60000361', 交易密码='123123', 通信密码='123123'):
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/港股通')
    目录校验(device, '二', '港股通', '/页面标题')
    点击(device, '/交易/普通交易/港股通/港股通首页/汇率余额')
    登录前页面处理(device)
    沪港通买入 = 获取文本信息(device, '/交易/普通交易/港股通/汇率余额/沪港通买入')
    沪港通卖出 = 获取文本信息(device, '/交易/普通交易/港股通/汇率余额/沪港通卖出')
    深港通买入 = 获取文本信息(device, '/交易/普通交易/港股通/汇率余额/深港通买入')
    深港通卖出 = 获取文本信息(device, '/交易/普通交易/港股通/汇率余额/深港通卖出')
    沪港通买入结果 = 沪港通买入 != '' and 沪港通买入 != '--'
    沪港通卖出结果 = 沪港通卖出 != '' and 沪港通卖出 != '--'
    深港通买入结果 = 深港通买入 != '' and 深港通买入 != '--'
    深港通卖出结果 = 深港通卖出 != '' and 深港通卖出 != '--'
    if  沪港通买入 == '--' or 沪港通卖出 == '--' or 深港通买入 == '--' or 深港通卖出 == '--':
        返回(device)
        点击(device, '/交易/普通交易/港股通/港股通首页/汇率余额')
        沪港通买入 = 获取文本信息(device, '/交易/普通交易/港股通/汇率余额/沪港通买入')
        沪港通卖出 = 获取文本信息(device, '/交易/普通交易/港股通/汇率余额/沪港通卖出')
        深港通买入 = 获取文本信息(device, '/交易/普通交易/港股通/汇率余额/深港通买入')
        深港通卖出 = 获取文本信息(device, '/交易/普通交易/港股通/汇率余额/深港通卖出')
        沪港通买入结果 = 沪港通买入 != '' and 沪港通买入 != '--'
        沪港通卖出结果 = 沪港通卖出 != '' and 沪港通卖出 != '--'
        深港通买入结果 = 深港通买入 != '' and 深港通买入 != '--'
        深港通卖出结果 = 深港通卖出 != '' and 深港通卖出 != '--'
    为空校验(device)
    print('沪港通买入：%s,沪港通卖出：%s,深港通买入：%s,深港通卖出：%s' % (沪港通买入, 沪港通卖出, 深港通买入, 深港通卖出))
    错误校验点 = '沪港通买入：%s,沪港通卖出：%s,深港通买入：%s,深港通卖出：%s' % (沪港通买入, 沪港通卖出, 深港通买入, 深港通卖出)
    结果 = 沪港通买入结果 and 沪港通卖出结果 and 深港通买入结果 and 深港通卖出结果
    # 余额 = 获取文本信息('/交易/普通交易/港股通/汇率余额页面/余额')
    # if 余额 != '--':
    #     全局变量.错误校验点 = 数字校验(余额)
    #     结果 = float(余额) >= 0
    # else:
    #     全局变量.错误校验点 = 为空校验()
    #     结果 = False
    return 结果, 错误校验点


