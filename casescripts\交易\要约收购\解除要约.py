from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('交易/要约收购/解除要约/解除要约')
def _b7b7b0b9eacd13cc4ac715bb7f156e3f(device, 账号='10022785', 交易密码='123123', 通信密码='123123', 证券代码='706012', 委托价格='6.12', 预期结果='成功'):
    # 账号 = '666621794831'
    # 交易密码 = '110119'
    # 通信密码 = 'rf110119'
    # time.sleep(15)
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    上滑(device)
    sleep(1)
    点击(device, '/交易/普通交易/普通交易首页/其他交易')
    目录校验(device, '二', '其他交易', '/页面标题')
    点击(device, '/交易/普通交易/其他交易/其他交易首页/要约收购')
    点击(device, '/交易/普通交易/其他交易/其他交易首页/解除要约')
    输入文本(device, '/交易/普通交易/其他交易/要约收购/解除要约页面/要约代码输入框', 证券代码)
    输入文本(device, '/交易/普通交易/其他交易/要约收购/解除要约页面/委托价格', 委托价格)
    # 输入文本('/交易/普通交易/其他交易/要约收购/解除要约页面/委托数量', 委托数量)
    点击(device, '/交易/普通交易/其他交易/要约收购/预受要约页面/委托数量增加按钮')
    点击(device, '/底部确定/确定按钮')
    sleep(3)
    # 点击('/底部确定/确定按钮')
    # 点击('/交易/普通交易/其他交易/要约收购/解除要约页面/确定按钮')
    点击(device, '/交易/普通交易/其他交易/要约收购/解除要约页面/解除要约确定按钮')
    sleep(2)
    实际结果 = 获取文本信息(device, '/交易/普通交易/其他交易/解除要约首页/提示信息')
    sleep(2)
    错误校验点 = 非空校验(device, 预期结果, 实际结果)
    点击(device, '/交易/普通交易/其他交易/解除要约首页/提示确定')
    结果 = 预期结果 in 实际结果
    return 结果, 错误校验点


