from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('交易/我的国债理财/我的国债理财/我的国债理财')
def _385af6ce6272cccaf035671115b98700(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119', 证券代码='600123', 年收益率='10', 参与金额='1000'):
    错误校验点 = ''
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/国债理财')
    目录校验(device, '二', '国债理财', '/理财/国债理财/页面标题')
    点击(device, '/交易/普通交易/普通交易首页/国债理财/查询我的可用资金')
    sleep(3)
    购买前可用资金 = 获取文本信息(device, '/交易/普通交易/普通交易首页/国债理财/可交易资金')
    返回(device)
    sleep(3)
    点击(device, '/交易/普通交易/普通交易首页/国债理财/我的国债理财')
    sleep(3)
    点击(device, '/交易/普通交易/普通交易首页/国债理财/我的国债理财/立即去购买')
    sleep(3)
    点击(device, '/交易/普通交易/普通交易首页/国债理财/我的国债理财/立即去购买/一天期')
    sleep(3)
    点击(device, '/交易/普通交易/普通交易首页/国债理财/我的国债理财/立即去购买/一天期/立即参与')
    sleep(3)
    if 获取控件对象(device, '/交易/普通交易/普通交易首页/国债理财/我的国债理财/立即去购买/一天期/年化收益率') != '':
        输入文本(device, '/交易/普通交易/普通交易首页/国债理财/我的国债理财/立即去购买/一天期/年化收益率', 年收益率)
        输入文本(device, '/交易/普通交易/普通交易首页/国债理财/我的国债理财/立即去购买/一天期/输入金额', 参与金额)
        sleep(3)
        点击(device, '/交易/普通交易/普通交易首页/国债理财/我的国债理财/立即去购买/一天期/下单')
        点击(device, '/交易/普通交易/买入页面/买入确定按钮')
        try:
            实际结果 = 获取文本信息(device, '/交易/普通交易/买入页面/系统提示')
        except:
            实际结果 = 获取文本信息(device, '/交易/普通交易/普通交易首页/国债理财/我的国债理财/立即去购买/一天期/结果')
        预期结果 = '已下单'
        结果 = 预期结果 in 实际结果
        sleep(3)
        if not 结果:
            错误校验点 = 非空校验(device, 预期结果, 实际结果)
            点击(device, '/交易/普通交易/我的国债理财/买入一天期/结果页面/确定')
        else:
            点击(device, '/交易/普通交易/我的国债理财/买入一天期/结果页面/确定')
            # 验资
            返回(device)
            返回(device)
            返回(device)
            返回(device)
            点击(device, '/交易/普通交易/普通交易首页/国债理财/查询我的可用资金')
            sleep(3)
            购买后可用资金 = 获取文本信息(device, '/交易/普通交易/普通交易首页/国债理财/可交易资金')
            结果 = 购买前可用资金 > 购买后可用资金
            print('购买前可用资金为：%s，购买后可用资金为：%s' % (购买前可用资金, 购买后可用资金))
            if 结果:
                返回(device)
                点击(device, '/交易/普通交易/普通交易首页/国债理财/我的国债理财')
                sleep(2)
                点击(device, '/交易/普通交易/我的国债理财/今日下单/撤单')
                sleep(2)
                点击(device, '/交易/普通交易/我的国债理财/撤单确认/确定撤单')
                # 预期结果 = '成功'
                # 实际结果 = 获取文本信息('/交易/普通交易/我的国债理财/撤单/提示信息')
                结果 = 获取控件对象(device, '/交易/普通交易/我的国债理财/撤单/提示信息') !=''
                # 结果 = 预期结果 in 实际结果
                # 全局变量.错误校验点 = 非空校验(预期结果, 实际结果)
                错误校验点 = 为空校验(device)
                sleep(3)
                点击(device, '/交易/普通交易/其他交易/债转股页面/温馨提示确定')
            else:
                截屏(device)
                错误校验点 = '购买前可用资金为：%s，购买后可用资金为：%s' % (购买前可用资金, 购买后可用资金)
    else:
        错误校验点 = 获取文本信息(device, '/交易/普通交易/普通交易首页/国债理财/我的国债理财/立即去购买/一天期/超出时段')
        结果 = False
    return 结果, 错误校验点


