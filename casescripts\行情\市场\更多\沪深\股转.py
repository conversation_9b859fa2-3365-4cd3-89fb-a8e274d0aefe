from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('行情/市场/更多/沪深/股转/获取首只股转代码')
def _9ca9f76408edc5af8083fcec4117f253(device):
    登录前页面处理(device)
    print(device.appium.driver)
    点击(device, '/底部导航栏/行情界面跳转按钮')
    点击(device, '/行情/市场/市场标题栏')
    控件左滑(device, '/行情/市场/沪深按钮标签')
    点击(device, '/行情/市场/更多/更多标签')
    点击(device, '/行情/市场/更多/沪深/股转/股转标签')
    首只股转代码 = 获取文本信息(device, '/行情/市场/更多/沪深/股转/首只股转代码')
    结果 = 首只股转代码 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


