from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('行情/市场/沪深/热门板块/行业/首只股票最新幅度')
def _4d95274c7359524ceeb5e4c4053cc817(device):
    登录前页面处理(device)
    点击(device, '/底部导航栏/行情界面跳转按钮')
    目录校验(device, '一', '行情', '/行情/选中状态')
    点击(device, '/行情/市场/市场标题栏')
    目录校验(device, '二', '沪深', '/行情/更多/选中状态')
    上滑(device, 5000)
    点击(device, '/行情/市场/沪深/热门板块/更多版块按钮')
    行业首行股票最新价 = 获取文本信息(device, '/行情/市场/沪深/热门板块/更多/行业/行业首行股票最新幅度')
    结果 = 行业首行股票最新价 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


