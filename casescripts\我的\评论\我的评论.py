from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('我的/评论/我的评论/我的评论')
def _cdd3ed9ef1c63e79182634d4f49063b3(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # 初始化
    登录前页面处理(device)
    # 理财登陆(账号, 交易密码, 通信密码)
    # 交易登录(账号, 交易密码, 通信密码)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    目录校验(device, '一', '我的', '/我的/选中状态')
    循环滑动(device, '/我的/评论', "上")
    点击(device, '/我的/评论')
    目录校验(device, '二', '评论', '/页面标题')
    点击(device, '/我的/评论/我的评论')
    try:
        实际结果 = 获取控件对象(device, '/我的/评论/我的评论/无记录')
    except:
        pass
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


