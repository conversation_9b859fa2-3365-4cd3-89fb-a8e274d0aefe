from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('理财/公募基金/基金排行_股票基金排行/基金排行_股票基金排行')
def _c497afc58943358a628683ec93e8bc4d(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # 初始化
    登录前页面处理(device)
    理财登陆(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '理财', '/理财/选中状态')
    点击(device, '/理财/理财首页/公募基金')
    目录校验(device, '二', '基金专区', '/页面标题')
    sleep(3)
    点击(device, '/理财/理财首页/公募基金/基金排行')
    sleep(3)
    点击(device, '/理财/理财首页/公募基金/基金排行/股票基金')
    sleep(1)
    实际结果1 = float(获取文本信息(device, '/理财/理财首页/公募基金/基金排行/股票基金/第一条记录日涨幅')[0:-1])
    实际结果2 = float(获取文本信息(device, '/理财/理财首页/公募基金/基金排行/股票基金/第二条记录日涨幅')[0:-1])
    print(实际结果1, 实际结果2)
    if 实际结果1 >= 实际结果2:
        结果 = True
    else:
        结果 = False
    错误校验点 = 比较校验(device, 实际结果1, 实际结果2)
    return 结果, 错误校验点


