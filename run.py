import datetime

from casescripts.default import *


@hide_error
def install_apk(device: LocalDevice):
    with open('log/%s.id' % device.device_id, 'w') as f:
        f.write(device.device_id)
        f.close()
    device.push(os.path.abspath('log/%s.id' % device.device_id), '/sdcard/device.id')
    fp = subprocess.Popen(
        'adb -s %s shell am start -n com.zfkj.ybc.screenrecord/com.zfkj.ybc.screenrecord.activity.MainActivity' % device.device_id,
        shell=True, bufsize=-1, stderr=subprocess.PIPE)
    msg = fp.stderr.read()
    if 'Error' in msg.decode():
        device.adb_command('install %s' % os.path.abspath('./casescripts/screenrecord.apk'))




# 局域网有线网络模式，连接adb
if WIRED=='true':
    connect_adb()


@listen_device_connect
@async_func
@hide_error
def conn(device: LocalDevice):
    if device.device_id in devices_s:
        return
    devices_s.append(device.device_id)
    device.shell('echo ' + device.device_id + ' >/sdcard/device_id')
    device.shell('am force-stop com.zfkj.ybc.screenrecord')
    sleep(1)
    device.shell('am start -n com.zfkj.ybc.screenrecord/com.zfkj.ybc.screenrecord.activity.MainActivity')
    while True:
        try:
            device.clean_appium()
            for app, name, case_id, _args, script, has_success,_testcase_id in run_task_case(device):
                # clear_log(device,case_id)
                print('================ %s ===========' % name)
                # device.log.info('========锁定用例======== %s ===========' % name)
                device.running_case_id = _testcase_id
                device.result_id = case_id
                runcase(app, device, name,has_success, case_id, script, **_args)
                if device.device_id not in DEVICES_LIST:
                    break
        except Exception as e:
            import traceback
            print('出现错误：', e, traceback.format_exc())
            sleep(5)
            pass


@listen_device_disconnect
@hide_error
def disconnection(local_device: LocalDevice):
    print("断开", local_device.device_id)
    pass


# _screenupload_fp = open('log/screenupload.log', 'w', encoding='UTF-8')
# subprocess.Popen('python ./script/screenupload.py', stdout=_screenupload_fp, stderr=_screenupload_fp, shell=True)
if is_track == 'true':
    subprocess.Popen('python tracking.py', stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL, shell=True)

if perf_log == 'true':
    _performance_fp = open('log/performance.log', 'w', encoding='UTF-8')
    subprocess.Popen('python performance.py', stdout=_performance_fp, stderr=_performance_fp, shell=True)

subprocess.Popen('python ./script/shellsocket.py', stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL, shell=True)

if mitm == 'true':
    _mitmproxy_fp = open('log/mitmproxy.log', 'w', encoding='UTF-8')
    subprocess.Popen('mitmdump -p 8090 -s /home/<USER>/src/ControlSystem/mitm/tls_passthrough.py --q', stdout=_mitmproxy_fp, stderr=subprocess.DEVNULL, shell=True)


class UploadScreen(threading.Thread):
    def __init__(self):
        threading.Thread.__init__(self)
        self.upload_type=caches.get('config.IMAGE_UPLOAD_USE', 'oss')
        self.type = caches.get('config.S3_IMAGE_BUCKET', 'poc')
        self.authorization = caches.get('config.AUTHORIZATION',
                                        'admin.xcWigjiDhDTaBGRXeJ6ZzZ7qxGEusJi5LtiVQQpBz3mGkv7VUpxptnoLddGEC7g5')
 
    def run(self):
        print('上传线程')
        while True:
            remote,local=q.get()
            upload_video(remote,local,self.type,self.authorization)

            q.task_done()
    
    def upload_video(self,remote, local):
        if not os.path.exists(local):
            return None

        if self.upload_type == 's3':
            bucket_name = caches.get('config.S3_IMAGE_BUCKET')

            # 先取eu本机配置（局域网）
            endpoint = NAS_URL if NAS_URL else caches.get('config.S3_IMAGE_ENDPOINT')

            if isExceptionRequests():
                minioClient = minio.Minio(endpoint,
                                        access_key=caches.get('config.S3_IMAGE_ACCESS_KEY_ID'),
                                        secret_key=caches.get('config.S3_IMAGE_ACCESS_KEY_SECRET'),
                                        secure=False)

                date = datetime.datetime.now().strftime('%Y-%m-%d')
                remote = remote.replace('video/', 'video/' + date + '/')
                for _ in range(3):
                    try:
                        with open(local, 'rb') as file_data:
                            file_stat = os.stat(local)
                            minioClient.put_object(bucket_name, remote, file_data, file_stat.st_size, 'video/mp4')
                        break
                    except:
                        pass
            else:
                oss_image_access_key_id = caches.get('config.OSS_IMAGE_ACCESS_KEY_ID', 'LTAI5t7MSZ6uKgzq1Hny5mXM')
                oss_image_access_key_secret = caches.get('config.OSS_IMAGE_ACCESS_KEY_SECRET',
                                                        '******************************')
                oss_image_bucket = caches.get('config.OSS_IMAGE_BUCKET',
                                                        'zfkj-cps')
                oss_image_endpoint = caches.get('config.OSS_IMAGE_ENDPOINT', 'http://oss-cn-hangzhou.aliyuncs.com')
                oss_image_auth = oss2.Auth(oss_image_access_key_id, oss_image_access_key_secret)
                if oss_image_access_key_id=='LTAI5t7MSZ6uKgzq1Hny5mXM':
                    # 新oss id
                    oss_bucket = oss2.Bucket(oss_image_auth, oss_image_endpoint, oss_image_bucket)
                else:
                    oss_bucket = oss2.Bucket(oss_image_auth, 'http://oss-cn-hangzhou.aliyuncs.com', 'zfkj-cps')

                for _ in range(3):
                    try:
                        oss_bucket.put_object_from_file(remote, local)
                        break
                    except:
                        pass
        else:
            oss_image_access_key_id = caches.get('config.OSS_IMAGE_ACCESS_KEY_ID', 'LTAI5t7MSZ6uKgzq1Hny5mXM')
            oss_image_access_key_secret = caches.get('config.OSS_IMAGE_ACCESS_KEY_SECRET', '******************************')
            oss_image_bucket = caches.get('config.OSS_IMAGE_BUCKET','zfkj-cps')
            oss_image_endpoint = caches.get('config.OSS_IMAGE_ENDPOINT', 'http://oss-cn-hangzhou.aliyuncs.com')
            oss_image_auth = oss2.Auth(oss_image_access_key_id, oss_image_access_key_secret)
            if oss_image_access_key_id=='LTAI5t7MSZ6uKgzq1Hny5mXM':
                # 新oss id
                oss_bucket = oss2.Bucket(oss_image_auth, oss_image_endpoint, oss_image_bucket)
            else:
                oss_bucket = oss2.Bucket(oss_image_auth, 'http://oss-cn-hangzhou.aliyuncs.com', 'zfkj-cps')
            for _ in range(3):
                try:
                    oss_bucket.put_object_from_file(remote, local)
                    break
                except:
                    pass
         
        os.unlink(local)

if __name__ == '__main__':
    # 创建收集图片文件夹
    if not os.path.exists('pictures'):
        os.mkdir('pictures')
    if not os.path.exists('pictures/handle'):  # 图片裁剪,对比，处理等操作
        os.mkdir('pictures/handle')
    if not os.path.exists('pictures/3060_ocr'):  # 3060识别
        os.mkdir('pictures/3060_ocr')
    if not os.path.exists('pictures/own_ocr'):  # 自有识别
        os.mkdir('pictures/own_ocr')
    if not os.path.exists('pictures/pay_ocr'):  # 付费识别
        os.mkdir('pictures/pay_ocr')
    # 创建线程上传录屏
    u = UploadScreen()
    u.start()

    while True:
        t = datetime.datetime.now()
        eu_online()
        _t = (datetime.datetime.now() - t).seconds
        if _t < 30:
            print('等待 %s 秒' % (30 - _t))
            sleep(30 - _t)
