from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('行情/自选股/编辑/指数')
def _ab0928de7fadc3f0973a945662f32f35(device):
    错误校验点 = ''
    登录前页面处理(device)
    点击(device, '/底部导航栏/行情界面跳转按钮')
    目录校验(device, '一', '行情', '/行情/选中状态')
    点击(device, '/行情/自选股/自选股标签')
    # 目录校验('二', '自选股', '/行情/自选股')
    点击(device, '/行情/自选股/编辑按钮')
    目录校验(device, '二', '编辑', '/行情/自选股/智能/页面标题')
    点击(device, '/行情/自选股/编辑/指数/指数标签')
    sleep(5)
    # 上证100=获取对象位置和手机分辨率('/行情/自选股/编辑/指数/上证100')[0]
    # 第一个指数=获取对象位置和手机分辨率('/行情/自选股/编辑/指数/第一个指数')[0]
    第一个指数的文本信息1 = 获取文本信息(device, '/行情/自选股/编辑/指数/第一个指数的名称')
    对象拖动(device, '/行情/自选股/编辑/指数/上证100', '/行情/自选股/编辑/指数/第一个指数')
    第一个指数的文本信息2 = 获取文本信息(device, '/行情/自选股/编辑/指数/第一个指数的名称')
    结果 = 第一个指数的文本信息1 != 第一个指数的文本信息2
    if 第一个指数的文本信息1 == 第一个指数的文本信息2:
        错误校验点 = '指数未拖动成功'
    return 结果, 错误校验点
    # 还原环境
    sleep(1)
    对象拖动(device, '/行情/自选股/编辑/指数/上证指数', '/行情/自选股/编辑/指数/第一个指数')


@casescript('行情/自选股/编辑/自选股')
def _e93b83606928b25c6a6da6b3f283cc5c(device):
    登录前页面处理(device)
    点击(device, '/底部导航栏/行情界面跳转按钮')
    目录校验(device, '一', '行情', '/行情/选中状态')
    点击(device, '/行情/自选股/自选股标签')
    # 目录校验('二', '自选股', '/行情/自选股')
    点击(device, '/行情/自选股/编辑按钮')
    目录校验(device, '二', '编辑', '/行情/自选股/智能/页面标题')
    # 删除所有自选股
    while True:
        删除按钮 = 获取控件对象(device, '/行情/自选股/编辑/自选股/删除按钮')
        if 删除按钮 != '':
            点击(device, '/行情/自选股/编辑/自选股/删除按钮')
        else:
            break
    点击(device, '/行情/自选股/编辑/自选股/加自选')
    输入文本(device, '/搜索/搜索文本框', '600600')
    点击(device, '/搜索/加自选')
    点击(device, '/搜索/返回')
    第一只股票的代码 = 获取文本信息(device, '/行情/自选股/编辑/自选股/第一只股票的代码')
    结果 = 第一只股票的代码 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


