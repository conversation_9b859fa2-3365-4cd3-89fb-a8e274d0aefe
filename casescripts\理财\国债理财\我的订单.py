from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('理财/国债理财/我的订单/我的订单')
def _c81ca98036feb7d4922778218175e55d(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # 初始化
    登录前页面处理(device)
    理财登陆(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '理财', '/理财/选中状态')
    滑动控件至屏幕内_理财(device, '/理财/天天财')
    控件左滑_理财(device, '/理财/天天财')
    点击(device, '/理财/国债理财')
    目录校验(device, '二', '国债理财', '/理财/国债理财/页面标题')
    点击(device, '/理财/国债理财/我的订单')
    登录前页面处理(device)
    实际结果 = 获取文本信息(device, '/理财/国债理财/我的订单/当前持有')
    print(实际结果)
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


