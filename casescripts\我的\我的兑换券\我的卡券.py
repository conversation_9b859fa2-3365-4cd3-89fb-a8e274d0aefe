from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('我的/我的兑换券/我的卡券/我的卡券')
def _a993b893dfcfdbfa48a98ee1105f64ed(device, 账号='666630296771', 交易密码='124369', 通信密码='159753'):
    登录前页面处理(device)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    # 上滑()
    目录校验(device, '一', '我的', '/我的/选中状态')
    点击(device, '/我的/我的兑换券')
    目录校验(device, '二', '我的卡券', '/页面标题')
    try:
        实际结果 = 获取文本信息(device, '/登录标题')
    except:
        实际结果 = 获取文本信息(device, '/页面标题')
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


