from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('行情/市场/全球/国际汇率/港币人民币中间价/进入指数单券查看点数值')
def _06a484231a108faf763023be22329a43(device):
    登录前页面处理(device)
    print(device.appium.driver)
    点击(device, '/底部导航栏/行情界面跳转按钮')
    目录校验(device, '一', '行情', '/行情/选中状态')
    点击(device, '/行情/市场/市场标题栏')
    控件左滑(device, '/行情/市场/沪深按钮标签')
    点击(device, '/行情/市场/全球/全球标签')
    目录校验(device, '二', '全球', '/行情/更多/选中状态')
    滑动控件至屏幕内(device, '/行情/市场/全球/国际汇率/港币人民币中间价/证券名称')
    点击(device, '/行情/市场/全球/国际汇率/港币人民币中间价/证券名称')
    检查点数值 = 获取文本信息(device, '/行情/市场/全球/国际汇率/港币人民币中间价/检查点数值')
    print("/行情/市场/全球/国际汇率/港币人民币中间价/检查点数值:::", 检查点数值)
    结果 = 检查点数值 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


