from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('交易/港股通/委托撤单/委托撤单')
def _5a0b0cbc6196cb78fc8c839a8d0c3bc5(device, 账号='30011488', 交易密码='123123', 通信密码='123123', 预期结果='已提交'):
    错误校验点 = ''
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/港股通')
    目录校验(device, '二', '港股通', '/页面标题')
    点击(device, '/交易/普通交易/港股通/港股通首页/委托撤单按钮')
    结果 = True
    #
    while 结果:
        撤单按钮 = 获取控件对象(device, '/交易/普通交易/港股通/委托撤单页面/撤单按钮')
        if 撤单按钮 != '':
            点击(device, '/交易/普通交易/港股通/委托撤单页面/撤单按钮')
            点击(device, '/交易/普通交易/港股通/委托撤单页面/撤单确认按钮')
            实际结果 = 获取文本信息(device, '/交易/普通交易/港股通/委托撤单页面/提示信息')
            错误校验点 = 非空校验(device, 预期结果, 实际结果)
            点击(device, '/提示确认/确认按钮')
            结果 = 预期结果 in 实际结果
        else:
            print('全部撤单完毕！')
            break
    return 结果, 错误校验点


