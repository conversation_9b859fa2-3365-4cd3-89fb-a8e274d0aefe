from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('交易/普通交易/撤单/撤单')
def _3e2a18ab6b02224103dd88efa5fa7e25(device, 账号='666630296771', 交易密码='124369', 通信密码='159753', 预期结果='已提交'):
    错误校验点 = ''
    # 初始化
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/普通交易')
    点击(device, '/交易/普通交易/普通交易首页/撤单按钮')
    目录校验(device, '二', '撤单', '/交易/普通交易/撤单/选中状态')
    结果 = True
    #
    while 结果:
        撤单按钮 = 获取控件对象(device, '/交易/普通交易/撤单/撤单页面/撤单条数')
        if 撤单按钮 != '':
            点击(device, '/交易/普通交易/撤单/撤单页面/撤单条数')
            点击(device, '/委托撤单/确定按钮')
            实际结果 = 获取文本信息(device, '/交易/普通交易/撤单/撤单委托明细页面/撤单提示信息')
            错误校验点 = 非空校验(device, 预期结果, 实际结果)
            点击(device, '/提示确认/确认按钮')
            结果 = 预期结果 in 实际结果
        else:
            print('全部撤单完毕！')
            break
    return 结果, 错误校验点


