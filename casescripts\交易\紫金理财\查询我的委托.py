from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('交易/紫金理财/查询我的委托/我的订单_历史委托')
def _0f4aacccee387dfdac748b6ba2ce5cb7(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # 初始化
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/紫金理财')
    目录校验(device, '二', '紫金理财', '/页面标题')
    点击(device, '/交易/普通交易/紫金理财/紫金理财首页/查询我的委托')
    点击(device, '/交易/普通交易/紫金理财/查询我的委托/历史委托')
    sleep(2)
    try:
        实际结果 = 获取文本信息(device, '/交易/普通交易/基金交易/紫金基金页面/查询我的委托/历史第一条记录')
    except:
        实际结果 = 获取文本信息(device, '/交易/普通交易/基金交易/紫金基金/查询我的委托/历史委托页面/无记录')
    # 预期结果 = 获取文本信息('/交易/普通交易/基金交易/紫金基金页面/查询我的委托/历史第一条记录')
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


@casescript('交易/紫金理财/查询我的委托/我的订单_当前委托')
def _f8395dbec1f06a1024c7e4a6622c480b(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    错误校验点 = ''
    # hxf 生产账户
    # 初始化
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/紫金理财')
    目录校验(device, '二', '紫金理财', '/页面标题')
    点击(device, '/交易/普通交易/紫金理财/紫金理财首页/查询我的委托')
    点击(device, '/交易/普通交易/紫金理财/查询我的委托/当前委托')
    sleep(2)
    try:
        委托类型 = 获取文本信息(device, '/交易/普通交易/基金交易/紫金基金页面/查询我的委托/当前委托第一条记录')
        委托结果 = 获取文本信息(device, '/交易/普通交易/基金交易/紫金基金/查询我的委托/当前委托页面/第一条状态')
        # 结果 = ('已成' in 委托结果) or ('已撤' in 委托结果) or ('成交' in 委托结果)
        结果 = 委托类型 != '' and 委托结果 != ''
        错误校验点 = 为空校验(device)
    except:
        实际结果 = 获取控件对象(device, '/交易/普通交易/紫金理财/查询我的委托/当前委托')
        结果 = 实际结果 != ''
        错误校验点 = 为空校验(device)
    # print(委托类型)
    # print('委托结果:%s,结果：%s' % (委托结果, 结果))
    return 结果, 错误校验点


