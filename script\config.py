import configparser
import os
import requests


def _loadConfig():
    test_cfg = os.path.realpath(os.path.join(os.path.dirname(__file__), '../config.ini'))
    if os.path.exists(test_cfg):
        config_raw = configparser.ConfigParser()
        config_raw.read(test_cfg)
        _CONFIG = config_raw
        Config.API_URL = config_raw.get('cloud', 'API_URL', fallback=Config.API_URL)
        Config.API_TOKEN = config_raw.get('cloud', 'API_TOKEN', fallback=Config.API_TOKEN)
        Config.EU_ID = config_raw.get('cloud', 'EU_ID', fallback=Config.EU_ID)
        request.headers['Authorization'] = 'token %s' % Config.API_TOKEN


request = requests.session()


class Config:
    API_URL = "http://127.0.0.1:8000/api/"
    API_TOKEN = "3c522535f81072c446c86d56c45d6aef37aa4c4e"
    EU_ID = '2e148ceb-a380-11e9-98ac-00163e10d7d6'
    _CONFIG = None
    _cache = None

    @staticmethod
    def get(item):
        if Config._cache is None:
            res = request.get(Config.API_URL.rstrip('/') + "/eu-online", params={'id': Config.EU_ID, 'format': 'json'})
            Config._cache = res.json()['config']
        return Config._cache.get(item)

    @staticmethod
    def all():
        Config.get("_config_version")
        return Config._cache


_loadConfig()

# http://**************:8000/api/eu-online?id=4768f4bc-30bc-11ec-a281-00163e05f34a

if __name__ == '__main__':
    print(Config.all())
