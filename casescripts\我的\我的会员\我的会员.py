from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('我的/我的会员/我的会员/我的会员')
def _4641c27f8590213ca7b4c362213fcbff(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    错误校验点 = ''
    # 初始化
    登录前页面处理(device)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    目录校验(device, '一', '我的', '/我的/选中状态')
    会员等级 = 获取文本信息(device, '/我的/我的会员/会员等级')
    会员主页会员名称 = 获取文本信息(device, '/我的/我的会员/会员名称')
    结果 = 会员等级 != ''
    if 结果:
        点击(device, '/我的/我的会员/会员卡')
        sleep(4)
        会员详情会员名称 = 获取文本信息(device, '/我的/我的会员/会员卡/会员名称')
        print('会员主页会员名称::::: ', 会员主页会员名称)
        print('会员详情会员名称::::: ', 会员详情会员名称)
        # 因为会员名称在会员卡详情页面可能会被截断，因此取前面8个字符比较(测试的时候发现8个字符以后的字符会显示为...)
        会员详情会员名 = 会员详情会员名称[0:8]
        结果 = 会员详情会员名 in 会员主页会员名称
        错误校验点 = 非空校验(device, 会员主页会员名称, 会员详情会员名)
    else:
        错误校验点 = 为空校验(device)
    return 结果, 错误校验点


