from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('我的/我的关注/基金')
def _450f9ed0fcbece4dd5279d549f29169a(device, 账号='666630296771', 交易密码='124369', 通信密码='159753'):
    登录前页面处理(device)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    上滑(device)
    点击(device, '/我的/我的关注')
    # 登录(账号, 交易密码, 通信密码)
    点击(device, '/我的/我的关注/基金')
    if 获取控件对象(device, '/我的/我的关注/第一条记录'):
        实际结果 = 获取控件对象(device, '/我的/我的关注/第一条记录')
    else:
        实际结果 = 获取控件对象(device, '/我的/我的关注/无记录')
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


@casescript('我的/我的关注/理财')
def _42f02b899572df599bf546c12c63c832(device, 账号='666630296771', 交易密码='124369', 通信密码='159753'):
    登录前页面处理(device)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    上滑(device)
    点击(device, '/我的/我的关注')
    # 登录(账号, 交易密码, 通信密码)
    点击(device, '/我的/我的关注/理财')
    if 获取控件对象(device, '/我的/我的关注/第一条记录'):
        实际结果 = 获取控件对象(device, '/我的/我的关注/第一条记录')
    else:
        实际结果 = 获取控件对象(device, '/我的/我的关注/无记录')
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


@casescript('我的/我的关注/资讯')
def _2a4c7b90cf389608d5782ce7679e975f(device, 账号='666630296771', 交易密码='124369', 通信密码='159753'):
    登录前页面处理(device)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    上滑(device)
    点击(device, '/我的/我的关注')
    # 登录(账号, 交易密码, 通信密码)
    点击(device, '/我的/我的关注/资讯')
    if 获取控件对象(device, '/我的/我的关注/第一条记录'):
        实际结果 = 获取控件对象(device, '/我的/我的关注/第一条记录')
    else:
        实际结果 = 获取控件对象(device, '/我的/我的关注/无记录')
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


