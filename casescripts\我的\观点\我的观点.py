from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('我的/观点/我的观点/我的观点')
def _d11c63763cc34e0a39ef56a2652cba63(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # 初始化
    登录前页面处理(device)
    # 理财登陆(账号, 交易密码, 通信密码)
    # 交易登录(账号, 交易密码, 通信密码)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    目录校验(device, '一', '我的', '/我的/选中状态')
    循环滑动(device, '/我的/观点', "上")
    点击(device, '/我的/观点')
    目录校验(device, '二', '我的观点', '/页面标题')
    try:
        实际结果 = 获取控件对象(device, '/我的/观点/无记录')
    except:
        pass
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


