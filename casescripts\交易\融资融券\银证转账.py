from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('交易/融资融券/银证转账/资金转入')
def _7391d94a40676894fded815976fc9baf(device, 账号='01066033', 交易密码='123123', 通信密码='123123', 转入金额='100', 预期结果='成功'):
    # time.sleep(15)
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/融资融券')
    点击(device, '/交易/融资融券/融资融券首页/银证转账按钮')
    目录校验(device, '二', '银证转账', '/银证转账')
    点击(device, '/交易/融资融券/银证转账页面/资金转入')
    # 点击( '/交易/融资融券/银证转账/从银行转入页面/请输入转入金额对话框')
    输入文本(device, '/交易/融资融券/银证转账/从银行转入页面/请输入转入金额对话框', 转入金额)
    # 点击('/交易/融资融券/银证转账/从银行转入页面/查询银行卡余额')
    # time.sleep(3)  # 等3秒防止查询失败
    # 余额 = 获取文本信息('/交易/融资融券/银证转账/从银行转入页面/查询银行卡余额')
    # print('余额：%s,转入金额：%s' % (余额, 转入金额))
    点击(device, '/交易/融资融券/银证转账/从银行转入页面/确定按钮')
    # time.sleep(3)  # 等3秒防止查询失败
    实际结果 = 获取文本信息(device, '/交易/融资融券/银证转账/资金转入/转入结果页面/成功')
    错误校验点 = 非空校验(device, 预期结果, 实际结果)
    结果 = 预期结果 in 实际结果
    print('实际结果：%s,  结果：%s' % (实际结果, 结果))
    return 结果, 错误校验点
    # 点击('/交易/融资融券/银证转账/从银行转入页面/确定按钮')


@casescript('交易/融资融券/银证转账/资金转出')
def _68c10c94e92cee18defcd71952d94294(device, 账号='01066033', 交易密码='123123', 通信密码='123123', 资金密码='123123', 转入金额='100', 预期结果='输入密码'):
    # time.sleep(15)
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/融资融券')
    点击(device, '/交易/融资融券/融资融券首页/银证转账按钮')
    目录校验(device, '二', '银证转账', '/银证转账')
    点击(device, '/交易/融资融券/银证转账页面/资金转出')
    # 点击( '/交易/融资融券/银证转账/从银行转入页面/请输入转入金额对话框')
    输入文本(device, '/交易/融资融券/银证转账/从银行转入页面/请输入转入金额对话框', 转入金额)
    # 点击('/交易/融资融券/银证转账/从银行转入页面/查询银行卡余额')
    # time.sleep(3)  # 等3秒防止查询失败
    # 余额 = 获取文本信息('/交易/融资融券/银证转账/从银行转入页面/查询银行卡余额')
    # print('余额：%s,转入金额：%s' % (余额, 转入金额))
    点击(device, '/交易/融资融券/银证转账/从银行转入页面/确定按钮')
    # time.sleep(3)  # 等3秒防止查询失败
    # 输入文本('/交易/融资融券/银证转账/从银行转入页面/输入资金密码1', '1')
    # 输入文本('/交易/融资融券/银证转账/从银行转入页面/输入资金密码2', '2')
    # 输入文本('/交易/融资融券/银证转账/从银行转入页面/输入资金密码3', '3')
    # 输入文本('/交易/融资融券/银证转账/从银行转入页面/输入资金密码4', '1')
    # 输入文本('/交易/融资融券/银证转账/从银行转入页面/输入资金密码5', '2')
    # 输入文本('/交易/融资融券/银证转账/从银行转入页面/输入资金密码6', '3')
    # 点击('/交易/融资融券/银证转账/从银行转入页面/确定按钮')
    # 预期结果 = '成功'
    实际结果 = 获取文本信息(device, '/页面标题')
    # 实际结果 = 获取文本信息('/交易/融资融券/银证转账/资金转入/转入结果页面/成功')
    错误校验点 = 非空校验(device, 预期结果, 实际结果)
    结果 = 预期结果 in 实际结果
    # print('实际结果：%s,  结果：%s' % (实际结果, 结果))
    return 结果, 错误校验点


@casescript('交易/融资融券/银证转账/转账流水')
def _8fa4fc3d8c45777fc4cb7484790ceaf8(device, 账号='30002187', 交易密码='123123', 通信密码='123123'):
    错误校验点 = ''
    登录前页面处理(device)
    # time.sleep(15)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/融资融券')
    点击(device, '/交易/融资融券/融资融券首页/银证转账按钮')
    目录校验(device, '二', '银证转账', '/银证转账')
    点击(device, '/交易/融资融券/银证转账页面/转账流水')
    sleep(2)  # 等5秒防止查询失败
    try:
        第一条记录状态 = 获取文本信息(device, '/交易/融资融券/银证转账页面/当日转账记录/第一条记录状态')
        预期结果 = '成功'
        index = 第一条记录状态.index('状态：')
        # print('index:::',index)
        实际结果 = 第一条记录状态[index:]  # 实际结果： 状态：成功
        错误校验点 = 非空校验(device, 预期结果, 实际结果)
        # print('实际结果：',实际结果)
        结果 = 预期结果 in 实际结果
    except:
        实际结果 = 获取文本信息(device, '/交易/融资融券/银证转账页面/当日转账记录/无记录')
        结果 = 实际结果 != ''
        错误校验点 = 为空校验(device)
    return 结果, 错误校验点


@casescript('交易/融资融券/银证转账/银行余额')
def _ed9eb7b4359da614f20a5cf001d74488(device, 账号='30002187', 交易密码='123123', 通信密码='123123'):
    错误校验点 = ''
    登录前页面处理(device)
    # time.sleep(15)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/融资融券')
    点击(device, '/交易/融资融券/融资融券首页/银证转账按钮')
    目录校验(device, '二', '银证转账', '/银证转账')
    点击(device, '/交易/融资融券/银证转账页面/银行余额')
    sleep(5)  # 等5秒防止查询失败
    if 获取控件对象(device, '/登陆/系统提示') != '':
        为空校验(device)
        错误校验点 = 获取文本信息(device, '/登陆/系统提示内容')
        结果 = False
        点击(device, '/提示确认/确认按钮')
    else:
        当前余额 = 获取文本信息(device, '/交易/融资融券/银证转账页面/银行余额/当前余额')
        错误校验点 = 数字校验(device, 当前余额)
        结果 = 当前余额 != '' and 当前余额 != '--'
        print('当前余额：%s  ,  结果：%s' % (当前余额, 结果))
        点击(device, '/交易/融资融券/银证转账页面/银行余额/确定')
    return 结果, 错误校验点


