from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('行情/市场/沪深/龙虎榜/个股榜单/个股榜第一')
def _b8096a23e4c49f20274c53b8698e864e(device):
    登录前页面处理(device)
    点击(device, '/底部导航栏/行情界面跳转按钮')
    目录校验(device, '一', '行情', '/行情/选中状态')
    点击(device, '/行情/市场/市场标题栏')
    目录校验(device, '二', '沪深', '/行情/更多/选中状态')
    控件左滑(device, '/行情/市场/沪深/智能投顾栏目')
    点击(device, '/行情/市场/沪深/龙虎榜/龙虎榜按钮')
    点击(device, '/行情/市场/沪深/主力风云榜/个股榜单按钮')
    sleep(3)
    try:
        个股榜第一 = 获取文本信息(device, '/行情/市场/沪深/主力风云榜/个股榜单/个股榜第一')
    except:
        返回(device)
        点击(device, '/行情/市场/沪深/主力风云榜/个股榜单按钮')
        sleep(5)
        个股榜第一 = 获取文本信息(device, '/行情/市场/沪深/主力风云榜/个股榜单/个股榜第一')
    print("个股榜第一:::", 个股榜第一)
    结果 = 个股榜第一 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


