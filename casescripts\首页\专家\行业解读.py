from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('首页/专家/行业解读/行业解读')
def _ff9534d5f48394c426ab36174885d524(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # 初始化
    登录前页面处理(device)
    点击(device, '/底部导航栏/首页界面跳转按钮')
    目录校验(device, '一', '首页', '/首页/选中状态')
    点击(device, '/首页/专家')
    目录校验(device, '二', '专家', '/首页/专家/选中状态')
    循环滑动(device, '/首页/专家/行业解读/第一条消息', '上')
    点击(device, '/首页/专家/行业解读/第一条消息')
    # 点击( '/首页/专家/第一条消息/第一个嘉宾')
    sleep(2)
    实际结果 = 获取文本信息(device, '/首页/专家/行业解读/第一条消息/名字')
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


