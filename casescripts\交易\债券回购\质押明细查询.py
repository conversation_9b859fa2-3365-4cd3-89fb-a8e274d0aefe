from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('交易/债券回购/质押明细查询/质押明细查询')
def _abcf027e8cdcf577dbc8c84955d4c245(device, 账号='01028889', 交易密码='123123', 通信密码='123123', 证券代码='010107', 交易数量='1'):
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    上滑(device)
    sleep(1)
    点击(device, '/交易/普通交易/普通交易首页/其他交易')
    目录校验(device, '二', '其他交易', '/页面标题')
    点击(device, '/交易/普通交易/其他交易/其他交易首页/债券回购')
    点击(device, '/交易/普通交易/其他交易/其他交易首页/债券回购_质押明细查询')
    try:
        实际结果 = 获取文本信息(device, '/交易/普通交易/其他交易/其他交易首页/债券回购_质押明细查询/第一条记录')
    except:
        实际结果 = 获取控件对象(device, '/交易/普通交易/其他交易/其他交易首页/债券回购/无记录')
    错误校验点 = 为空校验(device)
    结果 = 实际结果 != ''
    return 结果, 错误校验点


