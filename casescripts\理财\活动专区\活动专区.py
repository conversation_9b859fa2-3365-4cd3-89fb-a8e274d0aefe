from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('理财/活动专区/活动专区/活动专区')
def _b27ecd9e716a39f95bac5d64fe9c48e8(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # 初始化
    登录前页面处理(device)
    理财登陆(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '理财', '/理财/选中状态')
    滑动控件至屏幕内_理财(device, '/理财/首发基金')
    控件左滑_理财(device, '/理财/首发基金')
    点击(device, '/理财/自定义添加')
    sleep(2)
    # 循环滑动('/理财/活动专区', '上')
    上滑(device)
    sleep(2)
    device.appium.driver.background_app(3)
    点击(device, '/理财/活动专区')
    目录校验(device, '二', '活动专区', '/页面标题')
    sleep(3)
    实际结果 = 获取控件对象(device, '/理财/活动专区/第一个活动')
    # print(实际结果)
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


