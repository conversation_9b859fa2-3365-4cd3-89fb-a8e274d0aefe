from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('交易/融资融券/担保卖出/卖券还款')
def _203a78cb6b741d568d812bcf0a270682(device, 账号='58008098', 交易密码='123123', 通信密码='123123', 证券代码='600536', 交易数量='100', 预期结果='委托成功'):
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/融资融券')
    点击(device, '/交易/融资融券/融资融券首页/担保品卖出按钮')
    if 获取控件对象(device, '/登陆/系统提示') != '':
        点击(device, '/系统通知/不再提示')
        点击(device, '/系统提示/通知/确定')
    点击(device, '/交易/融资融券/融资融券首页/卖券还款')
    目录校验(device, '二', '卖券还券', '/页面标题')
    输入文本(device, '/交易/融资融券/还款还券/卖券还款页面/请输入股票代码框', 证券代码)
    点击(device, '/交易/融资融券/增加数量')
    点击(device, '/交易/普通交易/卖出页面/涨停价')
    点击(device, '/交易/融资融券/还款还券/卖券还款页面/卖出按钮')
    点击(device, '/交易/融资融券/还款还券/卖券还款页面/卖出确定按钮')
    实际结果 = 获取文本信息(device, '/交易/融资融券/还款还券/卖券还款页面/系统提示')
    错误校验点 = 非空校验(device, 预期结果, 实际结果)
    结果 = 预期结果 in 实际结果
    点击(device, '/交易/融资融券/还款还券/卖券还款页面/委托确定按钮')
    return 结果, 错误校验点


@casescript('交易/融资融券/担保卖出/担保卖出')
def _e1f93fb97a0438340fe1b321200251f8(device, 账号='30002187', 交易密码='123123', 通信密码='123123', 证券代码='600628', 交易数量='100', 预期结果='委托成功'):
    # time.sleep(15)
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/融资融券')
    点击(device, '/交易/融资融券/融资融券首页/担保品卖出按钮')
    目录校验(device, '二', '担保卖出', '/页面标题')
    if 获取控件对象(device, '/登陆/系统提示') != '':
        点击(device, '/系统通知/不再提示')
        点击(device, '/系统提示/通知/确定')
    点击(device, '/交易/融资融券/融资融券首页/担保品卖出')
    输入文本(device, '/交易/融资融券/担保品卖出页面/请输入股票代码框', 证券代码)
    点击(device, '/交易/融资融券/增加数量')
    点击(device, '/交易/普通交易/卖出页面/涨停价')
    点击(device, '/交易/融资融券/担保品卖出页面/卖出按钮')
    点击(device, '/交易/融资融券/担保品卖出页面/卖出确定按钮')
    实际结果 = 获取文本信息(device, '/交易/融资融券/担保品卖出页面/系统提示')
    错误校验点 = 非空校验(device, 预期结果, 实际结果)
    结果 = 预期结果 in 实际结果
    点击(device, '/交易/融资融券/担保品卖出页面/委托确定按钮')
    return 结果, 错误校验点


@casescript('交易/融资融券/担保卖出/融券卖出')
def _9d293b1421a33ab6a4f2a66cdf5a3d61(device, 账号='01010653', 交易密码='123123', 通信密码='123123', 证券代码='600036', 交易数量='100', 预期结果='成功'):
    # 初始化
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/融资融券')
    点击(device, '/交易/融资融券/融资融券首页/担保品卖出按钮')
    if 获取控件对象(device, '/登陆/系统提示') != '':
        点击(device, '/系统通知/不再提示')
        点击(device, '/系统提示/通知/确定')
    点击(device, '/交易/融资融券/融资融券首页/融券卖出')
    目录校验(device, '二', '融券卖出', '/页面标题')
    点击(device, '/交易/融资融券/融券卖出页面/请输入股票代码')
    输入文本(device, '/交易/融资融券/融券卖出页面/请输入股票代码', 证券代码)
    点击(device, '/交易/融资融券/融券卖出页面/涨停价')
    点击(device, '/交易/融资融券/增加数量')
    点击(device, '/交易/普通交易/卖出页面/涨停价')
    点击(device, '/交易/融资融券/融券卖出页面/卖出按钮')
    点击(device, '/交易/融资融券/融资买入页面/委托买入确定按钮')
    实际结果 = 获取文本信息(device, '/交易/融资融券/融券卖出页面/委托提示')
    错误校验点 = 非空校验(device, 预期结果, 实际结果)
    结果 = 预期结果 in 实际结果
    点击(device, '/交易/融资融券/融券卖出页面/系统提示确定按钮')
    print(结果)
    return 结果, 错误校验点


