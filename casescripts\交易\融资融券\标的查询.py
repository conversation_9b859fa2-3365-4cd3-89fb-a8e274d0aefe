from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('交易/融资融券/标的查询/可融证券')
def _b99cc5a989125aa7e47a2234ff05e0ff(device, 账号='30002187', 交易密码='123123', 通信密码='123123'):
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/融资融券')
    sleep(1)
    上滑(device)
    点击(device, '/交易/普通交易/普通交易首页/普通交易')
    点击(device, '/交易/普通交易/普通交易首页/融资融券')
    sleep(2)
    点击(device, '/交易/融资融券/融资融券首页/标的查询')
    目录校验(device, '二', '标的查询', '/页面标题')
    try:
        点击(device, '/底部确定/确定按钮')
    except:
        pass
    点击(device, '/交易/融资融券/标的查询/标的查询首页/可融证券')
    if 获取控件对象(device, '/交易/融资融券/标的查询/可融证券页面/股票名称') == '':
        点击(device, '/交易/融资融券/标的查询/标的券')
        点击(device, '/交易/融资融券/标的查询/标的查询首页/可融证券')
    实际结果 = 获取文本信息(device, '/交易/融资融券/标的查询/可融证券页面/股票名称')
    print(实际结果)
    错误校验点 = 为空校验(device)
    结果 = 实际结果 != ''
    return 结果, 错误校验点


@casescript('交易/融资融券/标的查询/查询担保品')
def _51ccc48c8e1a1e160357a236b5516c4b(device, 账号='30002187', 交易密码='123123', 通信密码='123123'):
    # time.sleep(5)
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/融资融券')
    sleep(1)
    上滑(device)
    点击(device, '/交易/普通交易/普通交易首页/普通交易')
    点击(device, '/交易/普通交易/普通交易首页/融资融券')
    sleep(2)
    点击(device, '/交易/融资融券/融资融券首页/标的查询')
    目录校验(device, '二', '标的查询', '/页面标题')
    try:
        点击(device, '/底部确定/确定按钮')
    except:
        pass
    实际结果 = 获取文本信息(device, '/交易/融资融券/标的查询/担保品页面/股票名称')
    print(实际结果)
    错误校验点 = 为空校验(device)
    结果 = 实际结果 != ''
    return 结果, 错误校验点


@casescript('交易/融资融券/标的查询/标的券')
def _521b4d1684a8de2b995ab12cc22643e8(device, 账号='30002187', 交易密码='123123', 通信密码='123123'):
    # time.sleep(15)
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/融资融券')
    sleep(1)
    上滑(device)
    点击(device, '/交易/普通交易/普通交易首页/普通交易')
    点击(device, '/交易/普通交易/普通交易首页/融资融券')
    sleep(2)
    点击(device, '/交易/融资融券/融资融券首页/标的查询')
    目录校验(device, '二', '标的查询', '/页面标题')
    try:
        点击(device, '/底部确定/确定按钮')
    except:
        pass
    sleep(2)
    左滑(device)
    sleep(2)
    实际结果 = 获取文本信息(device, '/交易/融资融券/标的查询/标的券页面/股票名称')
    print(实际结果)  # 获取文本失败
    错误校验点 = 为空校验(device)
    结果 = 实际结果 != ''
    return 结果, 错误校验点


