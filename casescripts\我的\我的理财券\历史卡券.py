from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('我的/我的理财券/历史卡券/已使用')
def _2705dbca8f2de5beb0da7b38d9b350aa(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    点击(device, '/底部导航栏/我的界面跳转按钮')
    目录校验(device, '一', '我的', '/我的/选中状态')
    点击(device, '/我的/我的理财券')
    登录前页面处理(device)
    登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '二', '我的理财券', '/页面标题')
    sleep(5)
    点击(device, '/我的/我的理财券/历史卡券')
    if 获取控件对象(device, '/我的/我的理财券/历史卡券/已使用/第一条'):
        实际结果 = 获取控件对象(device, '/我的/我的理财券/历史卡券/已使用/第一条')
    else:
        实际结果 = 获取文本信息(device, '/我的/我的理财券/历史卡券/已使用/没有券')
    错误校验点 = 为空校验(device)
    结果 = 实际结果 != ''
    return 结果, 错误校验点


@casescript('我的/我的理财券/历史卡券/已过期')
def _67bc25507eaa4dbe53ae9bd7210307ed(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    点击(device, '/底部导航栏/我的界面跳转按钮')
    目录校验(device, '一', '我的', '/我的/选中状态')
    点击(device, '/我的/我的理财券')
    登录前页面处理(device)
    登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '二', '我的理财券', '/页面标题')
    # time.sleep(5)
    点击(device, '/我的/我的理财券/历史卡券')
    点击(device, '/我的/我的理财券/历史卡券/已过期')
    sleep(2)
    try:
        实际结果 = 获取文本信息(device, '/我的/我的理财券/历史卡券/已过期/第一条')
    except:
        实际结果 = 获取文本信息(device, '/我的/我的理财券/历史卡券/已过期/无记录')
    错误校验点 = 为空校验(device)
    结果 = 实际结果 != ''
    return 结果, 错误校验点


