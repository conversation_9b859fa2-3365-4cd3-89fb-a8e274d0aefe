from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('行情/市场/股指/跳转单券页面/中证沪深上证期货代码检查')
def _dd89b2aaebb7cc600a17acf2bfbb680f(device):
    登录前页面处理(device)
    print(device.appium.driver)
    点击(device, '/底部导航栏/行情界面跳转按钮')
    目录校验(device, '一', '行情', '/行情/选中状态')
    点击(device, '/行情/市场/市场标题栏')
    点击(device, '/行情/市场/股指/股指标签')
    目录校验(device, '二', '股指', '/行情/更多/选中状态')
    滑动控件至屏幕内(device, '/行情/市场/股指/股指期货/成分品种校验/IC主力品种')
    点击(device, '/行情/市场/股指/股指期货/成分品种校验/IC主力品种')
    IC主力合约的点数值 = 获取文本信息(device, '/行情/市场/股指/股指期货/跳转单券页面/IC主力合约的点数值')
    结果 = IC主力合约的点数值 != '' and IC主力合约的点数值 != '--'
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


