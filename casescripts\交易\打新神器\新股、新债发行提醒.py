from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('交易/打新神器/新股、新债发行提醒/打新提醒')
def _3878d1c2bcff9837080fb4a5549b8f6d(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119', 预期结果='我不需要新债提醒'):
    # 初始化
    登录前页面处理(device)
    # time.sleep(15)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/打新神器')
    目录校验(device, '二', '打新神器', '/页面标题')
    sleep(1)
    点击(device, '/交易/普通交易/打新神器/打新神器页面/新股、新债发行提醒')
    点击(device, '/交易/普通交易/打新神器/新股、新债发行提醒/打新提醒')
    # time.sleep(2)
    try:
        实际结果 = 获取文本信息(device, '/交易/普通交易/打新神器/新股、新债发行提醒/打新提醒/提示信息')
    except:
        点击(device, '/交易/普通交易/打新神器/新股、新债发行提醒/打新提醒')
        实际结果 = 获取文本信息(device, '/交易/普通交易/打新神器/新股、新债发行提醒/打新提醒/提示信息')
    错误校验点 = 非空校验(device, 预期结果, 实际结果)
    结果 = 预期结果 == 实际结果
    return 结果, 错误校验点


