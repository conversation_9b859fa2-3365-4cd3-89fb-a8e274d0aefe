from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('交易/紫金理财/紫金开户/紫金开户')
def _ee76d491c15661d6a987e5458f330962(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # print(1)
    sleep(5)
    # 初始化
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/紫金理财')
    目录校验(device, '二', '紫金理财', '/页面标题')
    点击(device, '/交易/普通交易/紫金理财/紫金理财首页/紫金开户')
    # 点击('/交易/普通交易/紫金理财/紫金开户/账户开通页面/加载检查')
    return True, None


