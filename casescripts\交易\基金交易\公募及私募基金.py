from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('交易/基金交易/公募及私募基金/分红设置')
def _a3ea933d134709a73d62552f530ed11a(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119', 预期结果='成功'):
    错误校验点 = ''
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    上滑(device)
    点击(device, '/交易/普通交易/普通交易首页/基金交易')
    点击(device, '/交易/普通交易/普通交易首页/公募及私募基金')
    目录校验(device, '二', '公募及私募基金', '/页面标题')
    点击(device, '/交易/普通交易/基金交易/公募及私募基金/公募及私募基金首页/更多')
    点击(device, '/交易/普通交易/基金交易/公募及私募基金/更多/更多首页/分红设置')
    # 点击('/交易/普通交易/普通交易首页/基金交易/更多/分红设置页面/分红方式')#没有路径
    # 点击('/交易/普通交易/普通交易首页/基金交易/更多/分红设置页面/份额分红')#需要点击分红方式以后
    点击(device, '/交易/普通交易/基金交易/公募及私募基金/更多/分红设置页面/请选择产品')
    点击(device, '/交易/普通交易/普通交易首页/基金交易/更多/分红设置页面/第一个产品代码')  # 没有产品
    点击(device, '/交易/普通交易/普通交易首页/基金交易/更多/分红设置页面/请选择分红方式')  # 没有产品
    点击(device, '/交易/普通交易/普通交易首页/基金交易/更多/分红设置页面/第一个分红方式')  # 没有产品
    点击(device, '/交易/普通交易/基金交易/公募及私募基金/更多/分红设置页面/确定')
    if 获取控件对象(device, '/交易/普通交易/更多/网络投票/投票查询/投票记录详细页面/提示框内容'):
        实际结果 = 获取文本信息(device, '/交易/普通交易/更多/网络投票/投票查询/投票记录详细页面/提示框内容')
    else:
        实际结果 = 获取文本信息(device, '/页面/弹出框内容')
    # print("实际结果为", 实际结果)
    结果 = 预期结果 in 实际结果
    错误校验点 = 非空校验(device, 预期结果, 实际结果)
    if 预期结果 in 实际结果:
        返回(device)
        # time.sleep(3)
        返回(device)
        # time.sleep(3)
        点击(device, '/交易/普通交易/基金交易/紫金基金页面/撤销委托')
        点击(device, '/交易/普通交易/基金交易/紫金基金/撤单页面/刷新')
        预期结果 = '成功'
        try:
            # 实际结果 = 获取文本信息('/交易/普通交易/基金交易/紫金基金/撤单页面/第一条记录代码')
            print(实际结果)
            sleep(3)
            点击(device, '/交易/普通交易/基金交易/公募及私募基金/撤销委托/撤单页面/分红撤单')
            点击(device, '/交易/普通交易/基金交易/紫金基金/撤单/撤单确认/确定')
            实际结果 = 获取文本信息(device, '/交易/普通交易/基金交易/紫金基金/撤单/提示信息/结果')
            错误校验点 = 非空校验(device, 预期结果, 实际结果)
            # 点击('/交易/普通交易/基金交易/紫金基金/撤单/提示信息/确定')
        except:
            实际结果 = 获取文本信息(device, '/交易/普通交易/基金交易/紫金基金/撤单页面/无记录')
            错误校验点 = 非空校验(device, 预期结果, 实际结果)
    return 结果, 错误校验点


@casescript('交易/基金交易/公募及私募基金/基金开户')
def _65b77487a5823b6b30248f85bc905aaf(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119', 预期结果1='提交', 预期结果2='已存在'):
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    # time.sleep(15)
    目录校验(device, '一', '交易', '/交易/选中状态')
    上滑(device)
    点击(device, '/交易/普通交易/普通交易首页/基金交易')
    点击(device, '/交易/普通交易/普通交易首页/公募及私募基金')
    目录校验(device, '二', '公募及私募基金', '/页面标题')
    点击(device, '/交易/普通交易/基金交易/公募及私募基金/公募及私募基金首页/开户')
    点击(device, '/交易/普通交易/基金交易/公募及私募基金/基金开户/基金开户/请选择基金公司按钮')
    点击(device, '/交易/普通交易/基金交易/公募及私募基金/基金开户/基金开户/南方基金')
    点击(device, '/确定/确定按钮')
    实际结果 = 获取文本信息(device, '/交易/普通交易/更多/网络投票/投票查询/投票记录详细页面/提示框内容')
    # 点击('/底部确定/确定按钮')
    print("实际结果为", 实际结果)
    错误校验点 = 非空校验(device, 预期结果1 + '或者' + 预期结果2, 实际结果)
    结果 = 预期结果1 in 实际结果 or 预期结果2 in 实际结果
    return 结果, 错误校验点


@casescript('交易/基金交易/公募及私募基金/基金购买')
def _7a10da6f26ba739b5b70fa42650d2cb9(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119', 证券代码='160222', 预期结果='已提交'):
    错误校验点 = ''
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    上滑(device)
    点击(device, '/交易/普通交易/普通交易首页/基金交易')
    点击(device, '/交易/普通交易/普通交易首页/公募及私募基金')
    目录校验(device, '二', '公募及私募基金', '/页面标题')
    点击(device, '/交易/普通交易/基金交易/公募及私募基金/公募及私募基金首页/基金购买')
    点击(device, '/交易/普通交易/基金交易/公募及私募基金/基金购买/认购申购页面/代码输入框')
    输入文本(device, '/交易/普通交易/基金交易/公募及私募基金/基金购买/认购申购页面/代码输入框', 证券代码)
    点击(device, '/交易/普通交易/基金交易/公募及私募基金/基金购买/认购申购页面/立即购买按钮')
    点击(device, '/交易/普通交易/基金交易/公募及私募基金/基金购买/认购申购页面/我已知悉并同意')
    点击(device, '/底部确认/确认按钮')
    点击(device, '/交易/普通交易/基金交易/公募及私募基金/基金购买/认购申购页面/同意合同')
    点击(device, '/交易/普通交易/基金交易/公募及私募基金/基金购买/认购申购页面/确定按钮')
    # 点击('/交易/普通交易/基金交易/公募及私募基金/基金购买/认购申购页面/金额输入框')
    点击(device, '/交易/普通交易/基金交易/公募及私募基金/基金购买/认购申购页面/金额输入框1')
    点击(device, '/交易/普通交易/基金交易/公募及私募基金/基金购买/认购申购页面/金额输入框0')
    点击(device, '/交易/普通交易/基金交易/公募及私募基金/基金购买/认购申购页面/金额确定')
    点击(device, '/交易/普通交易/基金交易/公募及私募基金/基金购买/认购申购页面/确定购买')
    实际结果 = 获取文本信息(device, '/交易/普通交易/基金交易/公募及私募基金/基金购买/认购申购页面/购买完成提示页面')
    # 预期结果2 = '交易时间非法'
    错误校验点 = 非空校验(device, 预期结果, 实际结果)
    结果 = 预期结果 in 实际结果
    if 结果:
        返回(device)
        返回(device)
        点击(device, '/交易/普通交易/基金交易/公募及私募基金/公募及私募基金首页/撤销委托')
        滑动控件至屏幕内(device, '/交易/普通交易/基金交易/公募及私募基金/撤销委托/撤单页面/申购撤单')
        点击(device, '/交易/普通交易/基金交易/公募及私募基金/撤销委托/撤单页面/申购撤单')
        点击(device, '/底部确定/确定按钮')
        实际结果 = 获取文本信息(device, '/交易/普通交易/基金交易/公募及私募基金/撤销委托/撤单页面/提示信息')
        预期结果 = '成功'
        结果 = 预期结果 in 实际结果
        错误校验点 = 非空校验(device, 预期结果, 实际结果)
    # 点击('/底部确定/确定按钮')
    return 结果, 错误校验点


@casescript('交易/基金交易/公募及私募基金/基金购买_认购申购_当前委托')
def _897108a24cae33cc1891dbf9d893ee81(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119', 证券代码='160222', 预期结果='已提交'):
    错误校验点 = ''
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    上滑(device)
    点击(device, '/交易/普通交易/普通交易首页/基金交易')
    点击(device, '/交易/普通交易/普通交易首页/公募及私募基金')
    目录校验(device, '二', '公募及私募基金', '/页面标题')
    点击(device, '/交易/普通交易/基金交易/公募及私募基金/公募及私募基金首页/基金购买')
    点击(device, '/交易/普通交易/基金交易/公募及私募基金/基金购买/认购申购页面/代码输入框')
    输入文本(device, '/交易/普通交易/基金交易/公募及私募基金/基金购买/认购申购页面/代码输入框', 证券代码)
    sleep(3)
    点击(device, '/交易/普通交易/基金交易/公募及私募基金/基金购买/认购申购页面/立即购买按钮')
    点击(device, '/交易/普通交易/基金交易/公募及私募基金/基金购买/认购申购页面/我已知悉并同意')
    sleep(3)
    点击(device, '/底部确认/确认按钮')
    点击(device, '/交易/普通交易/基金交易/公募及私募基金/基金购买/认购申购页面/同意合同')
    点击(device, '/交易/普通交易/基金交易/公募及私募基金/基金购买/认购申购页面/确定按钮')
    # 点击('/交易/普通交易/基金交易/公募及私募基金/基金购买/认购申购页面/金额输入框')
    点击(device, '/交易/普通交易/基金交易/公募及私募基金/基金购买/认购申购页面/金额输入框1')
    点击(device, '/交易/普通交易/基金交易/公募及私募基金/基金购买/认购申购页面/金额输入框0')
    点击(device, '/交易/普通交易/基金交易/公募及私募基金/基金购买/认购申购页面/金额确定')
    点击(device, '/交易/普通交易/基金交易/公募及私募基金/基金购买/认购申购页面/确定购买')
    实际结果 = 获取文本信息(device, '/交易/普通交易/基金交易/公募及私募基金/基金购买/认购申购页面/购买完成提示页面')
    # 预期结果2 = '交易时间非法'
    错误校验点 = 非空校验(device, 预期结果, 实际结果)
    结果 = 预期结果 in 实际结果
    if 结果:
        返回(device)
        返回(device)
        # 点击('/交易/普通交易/基金交易/公募及私募基金/公募及私募基金首页/撤销委托')
        点击(device, '/交易/普通交易/基金交易/公募及私募基金/公募及私募基金首页/查询我的委托')
        sleep(2)
        点击(device, '/交易/普通交易/基金交易/公募及私募基金/公募及私募基金首页/查询我的委托/当前委托')
        滑动控件至屏幕内(device, '/交易/普通交易/基金交易/公募及私募基金/撤销委托/撤单页面/申购撤单')
        申购代码 = 获取文本信息(device, '/交易/基金交易/公募及私募基金/查询委托/申购代码')
        结果 = 申购代码 == 证券代码
        if 结果:
            点击(device, '/交易/普通交易/基金交易/公募及私募基金/撤销委托/撤单页面/申购撤单')
            点击(device, '/底部确定/确定按钮')
            实际结果 = 获取文本信息(device, '/交易/普通交易/基金交易/公募及私募基金/撤销委托/撤单页面/提示信息')
            预期结果 = '成功'
            结果 = 预期结果 in 实际结果
            错误校验点 = 非空校验(device, 预期结果, 实际结果)
        else:
            错误校验点 = '证券代码为：%s；申购代码为：%s' % (证券代码, 申购代码)
    # 点击('/底部确定/确定按钮')
    return 结果, 错误校验点


@casescript('交易/基金交易/公募及私募基金/基金赎回')
def _3a0b775ffb69c52653bc5246d0d638d6(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119', 交易数量='1', 证券代码='160222'):
    错误校验点 = ''
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    上滑(device)
    点击(device, '/交易/普通交易/普通交易首页/基金交易')
    点击(device, '/交易/普通交易/普通交易首页/公募及私募基金')
    目录校验(device, '二', '公募及私募基金', '/页面标题')
    点击(device, '/交易/普通交易/基金交易/公募及私募基金/公募及私募基金首页/基金赎回')
    点击(device, '/交易/普通交易/基金交易/公募及私募基金/基金赎回/场外基金赎回页面/代码输入框')
    输入文本(device, '/交易/普通交易/基金交易/公募及私募基金/基金赎回/场外基金赎回页面/代码输入框', 证券代码)
    sleep(2)
    # print('可赎回份额：：：：：', 获取文本信息('/交易/基金交易/公募及私募基金/基金赎回/可赎回份额'))
    赎回前份额 = 截取合同号(device, 获取文本信息(device, '/交易/基金交易/公募及私募基金/基金赎回/可赎回份额'))
    点击(device, '/交易/普通交易/基金交易/公募及私募基金/基金赎回/场外基金赎回页面/请输入赎回份额')
    输入文本(device, '/交易/普通交易/基金交易/公募及私募基金/基金赎回/场外基金赎回页面/请输入赎回份额', 交易数量)
    点击(device, '/交易/普通交易/基金交易/公募及私募基金/基金赎回/场外基金赎回页面/立即赎回')
    try:
        点击(device, '/确认/确认按钮')
        实际结果 = 获取文本信息(device, '/交易/普通交易/基金交易/公募及私募基金/基金赎回/场外基金赎回页面/立即赎回/赎回结果')
        预期结果 = '提交'
        原因 = ''
        if 预期结果 in 实际结果:
            结果 = True
            # 校验份额是否减少
            点击(device, '/交易/普通交易/基金交易/紫金基金/基金购买/基金购买页面/完成')
            点击(device, '/交易/普通交易/基金交易/公募及私募基金/公募及私募基金首页/基金赎回')
            点击(device, '/交易/普通交易/基金交易/公募及私募基金/基金赎回/场外基金赎回页面/代码输入框')
            输入文本(device, '/交易/普通交易/基金交易/公募及私募基金/基金赎回/场外基金赎回页面/代码输入框', 证券代码)
            sleep(2)
            赎回后份额 = 截取合同号(device, 获取文本信息(device, '/交易/基金交易/公募及私募基金/基金赎回/可赎回份额'))
            print('赎回前份额为：%s；赎回后份额为：%s' % (赎回前份额, 赎回后份额))
            结果 = 赎回前份额 > 赎回后份额
            if 结果:
                # 返回()
                # time.sleep(2)
                # 返回()
                # time.sleep(2)
                返回(device)
                点击(device, '/交易/普通交易/基金交易/公募及私募基金/公募及私募基金首页/撤销委托')
                sleep(3)
                点击(device, '/交易/普通交易/基金交易/公募及私募基金/撤销委托/撤单页面/赎回撤单')
                点击(device, '/底部确定/确定按钮')
                实际结果 = 获取文本信息(device, '/交易/普通交易/基金交易/公募及私募基金/撤销委托/撤单页面/提示信息')
                预期结果 = '成功'
                结果 = 预期结果 in 实际结果
                错误校验点 = 非空校验(device, 预期结果, 实际结果)
            else:
                截屏(device)
                错误校验点 = '赎回前份额为：%s；赎回后份额为：%s' % (赎回前份额, 赎回后份额)
        else:
            原因 = 获取文本信息(device, '/交易/普通交易/基金交易/公募及私募基金/基金赎回/场外基金赎回页面/立即赎回/失败原因')
            结果 = 原因 != ''
            错误校验点 = 非空校验(device, 预期结果, 原因)
    except:
        提示信息 = 获取文本信息(device, '/交易/普通交易/基金交易/公募及私募基金/基金赎回/场外基金赎回页面/立即赎回/提示信息')
        预期结果 = '提交'
        结果 = 提示信息 != ''
        错误校验点 = 非空校验(device, 预期结果, 提示信息)
        点击(device, '/交易/普通交易/基金交易/公募及私募基金/基金赎回/场外基金赎回页面/立即赎回/确认按钮')
    return 结果, 错误校验点


@casescript('交易/基金交易/公募及私募基金/基金转换')
def _f6b4dcd9e06849a4afbb430eec3ecfc9(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119', 交易数量='10', 预期结果='成功'):
    错误校验点 = ''
    # 登录前页面处理()
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    上滑(device)
    点击(device, '/交易/普通交易/普通交易首页/基金交易')
    点击(device, '/交易/普通交易/普通交易首页/公募及私募基金')
    目录校验(device, '二', '公募及私募基金', '/页面标题')
    点击(device, '/交易/普通交易/基金交易/公募及私募基金/公募及私募基金首页/更多')
    点击(device, '/交易/普通交易/基金交易/公募及私募基金/更多/更多首页/基金转换')
    点击(device, '/交易/普通交易/基金交易/公募及私募基金/更多/基金转换页面/请选择转出基金')
    点击(device, '/交易/普通交易/基金交易/公募及私募基金/更多/基金转换页面/转出基金/第一个基金')
    点击(device, '/交易/普通交易/基金交易/公募及私募基金/更多/基金转换页面/转入基金')
    sleep(5)
    点击(device, '/交易/普通交易/基金交易/公募及私募基金/更多/基金转换页面/转入基金/第一个基金')
    输入文本(device, '/交易/普通交易/基金交易/公募及私募基金/更多/基金转换页面/转换份额', 交易数量)
    # # 点击('/交易/普通交易/基金交易/公募及私募基金/更多/查询基金账户页面/最后一条确定按钮')
    # 点击('/交易/普通交易/基金交易/公募及私募基金/更多/基金转换页面/退出键盘')
    点击(device, '/交易/普通交易/基金交易/公募及私募基金/更多/基金转换页面/确定')
    点击(device, '/交易/普通交易/公用页面/场外基金_适当性确认书页面/我已悉知按钮')
    点击(device, '/底部确认/确认按钮')
    点击(device, '/交易/普通交易/公用页面/协议告知及合同签署页面/复选框')
    # 点击('/底部确认/确认按钮')
    sleep(5)
    点击(device, '/交易/普通交易/公用页面/协议告知及合同签署页面/再次确定')
    点击(device, '/确定/确定按钮')
    实际结果 = 获取文本信息(device, '/交易/普通交易/基金交易/公募及私募基金/更多/基金转换页面/结果')
    原因 = ''
    if 预期结果 in 实际结果:
        结果 = True
        返回(device)
        # time.sleep(2)
        返回(device)
        # time.sleep(2)
        返回(device)
        点击(device, '/交易/普通交易/基金交易/公募及私募基金/公募及私募基金首页/撤销委托')
        sleep(3)
        点击(device, '/交易/普通交易/基金交易/公募及私募基金/撤销委托/撤单页面/基金转换撤单')
        点击(device, '/底部确定/确定按钮')
        控件对象 = 获取控件对象(device, '/交易/普通交易/基金交易/公募及私募基金/公募及私募基金首页/撤销委托')
        实际结果 = 获取文本信息(device, '/交易/普通交易/基金交易/公募及私募基金/撤销委托/撤单页面/提示信息')
        预期结果 = '成功'
        结果 = 预期结果 in 实际结果
        错误校验点 = 非空校验(device, 预期结果, 实际结果)
        # 点击('/交易/普通交易/基金交易/公募及私募基金/撤销委托/撤单页面/提示确定按钮')
    else:
        原因 = 获取文本信息(device, '/交易/普通交易/基金交易/公募及私募基金/更多/基金转换页面/失败原因')
        结果 = 原因 != ''
        错误校验点 = 非空校验(device, 预期结果, 原因)
    return 结果, 错误校验点


@casescript('交易/基金交易/公募及私募基金/我的公募及私募基金')
def _29e821962b184560d5cd3a4a5f5a8918(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    上滑(device)
    点击(device, '/交易/普通交易/普通交易首页/基金交易')
    点击(device, '/交易/普通交易/普通交易首页/公募及私募基金')
    目录校验(device, '二', '公募及私募基金', '/页面标题')
    点击(device, '/交易/普通交易/基金交易/公募及私募基金/公募及私募基金首页/我的公募及私募基金')
    sleep(3)
    实际结果 = 获取文本信息(device, '/交易/普通交易/基金交易/公募及私募基金/公募及私募基金首页/我的公募及私募基金/基金总持仓')
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


@casescript('交易/基金交易/公募及私募基金/撤销委托')
def _0dcb8c2b6ef5d7aa5a6a773db3b3d763(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    错误校验点 = ''
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    上滑(device)
    点击(device, '/交易/普通交易/普通交易首页/基金交易')
    点击(device, '/交易/普通交易/普通交易首页/公募及私募基金')
    目录校验(device, '二', '公募及私募基金', '/页面标题')
    点击(device, '/交易/普通交易/基金交易/公募及私募基金/公募及私募基金首页/撤销委托')
    sleep(5)
    结果 = False
    if 获取控件对象(device, '/交易/普通交易/基金交易/公募及私募基金/公募及私募基金首页/撤销委托/无记录'):
        实际结果 = 获取文本信息(device, '/交易/普通交易/基金交易/公募及私募基金/公募及私募基金首页/撤销委托/无记录')
        预期结果 = '当前没有可撤销的订单'
        错误校验点 = 非空校验(device, 预期结果, 实际结果)
        结果 = 预期结果 in 实际结果
    else:
        买卖方向 = 获取文本信息(device, '/交易/基金交易/公募及私募基金/撤单/第一条记录/买卖方向')
        if 买卖方向 == '其它':
            证券代码 = 获取文本信息(device, '/交易/普通交易/基金交易/公募及私募基金/撤销委托/撤单页面/其它代码')
        elif 买卖方向 == '申购':
            证券代码 = 获取文本信息(device, '/交易/基金交易/公募及私募基金/查询委托/申购代码')
        elif 买卖方向 == '赎回':
            证券代码 = 获取文本信息(device, '/交易/基金交易/公募及私募基金/查询委托/赎回代码')
        结果 = 证券代码 != ''
        if 结果:
            点击(device, '/交易/普通交易/基金交易/公募及私募基金/撤销委托/撤单页面/撤单按钮')
            点击(device, '/底部确定/确定按钮')
            # 控件对象 = 获取控件对象('/交易/普通交易/基金交易/公募及私募基金/公募及私募基金首页/撤销委托')
            实际结果 = 获取文本信息(device, '/交易/普通交易/基金交易/公募及私募基金/撤销委托/撤单页面/提示信息')
            预期结果 = '成功'
            点击(device, '/交易/普通交易/基金交易/公募及私募基金/撤销委托/撤单页面/提示确定按钮')
            错误校验点 = 非空校验(device, 预期结果, 实际结果)
            结果 = 预期结果 in 实际结果
        else:
            错误校验点 = 为空校验(device)
    return 结果, 错误校验点


@casescript('交易/基金交易/公募及私募基金/查询基金账户')
def _05ddc8879d6aa3506bb5d2a3f23d9b86(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # time.sleep(15)
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    上滑(device)
    点击(device, '/交易/普通交易/普通交易首页/基金交易')
    点击(device, '/交易/普通交易/普通交易首页/公募及私募基金')
    目录校验(device, '二', '公募及私募基金', '/页面标题')
    点击(device, '/交易/普通交易/基金交易/公募及私募基金/公募及私募基金首页/更多')
    sleep(5)
    点击(device, '/交易/普通交易/基金交易/公募及私募基金/更多/更多首页/查询基金账户')
    实际结果 = 获取控件对象(device, '/交易/普通交易/基金交易/公募及私募基金/更多/查询基金账户页面/第一条记录')
    print("实际结果为", 实际结果)
    错误校验点 = 为空校验(device)
    结果 = 实际结果 != ''
    return 结果, 错误校验点


@casescript('交易/基金交易/公募及私募基金/查询我的委托_历史委托')
def _268a096d758561c7f5373a1904074123(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    错误校验点 = ''
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    上滑(device)
    点击(device, '/交易/普通交易/普通交易首页/基金交易')
    点击(device, '/交易/普通交易/普通交易首页/公募及私募基金')
    目录校验(device, '二', '公募及私募基金', '/页面标题')
    点击(device, '/交易/普通交易/基金交易/公募及私募基金/公募及私募基金首页/查询我的委托')
    sleep(2)
    点击(device, '/交易/普通交易/基金交易/公募及私募基金/公募及私募基金首页/查询我的委托/历史委托')
    sleep(3)
    if 获取文本信息(device, '/交易/普通交易/基金交易/公募及私募基金/公募及私募基金首页/查询我的委托/历史委托/历史委托第一条记录') != '':
        # 滑动控件至屏幕内('/交易/普通交易/基金交易/公募及私募基金/公募及私募基金首页/查询我的委托/历史委托/成功记录')
        # 点击('/交易/普通交易/基金交易/公募及私募基金/公募及私募基金首页/查询我的委托/历史委托/成功记录')
        点击(device, '/交易/普通交易/基金交易/公募及私募基金/公募及私募基金首页/查询我的委托/历史委托/历史委托第一条记录')
        实际结果 = 获取文本信息(device, '/交易/普通交易/基金交易/公募及私募基金/公募及私募基金首页/查询我的委托/历史委托/成功记录/基金代码')
        错误校验点 = 为空校验(device)
        结果 = 实际结果 != ''
    else:
        实际结果 = 获取控件对象(device, '/交易/普通交易/基金交易/公募及私募基金/公募及私募基金首页/撤销委托/无记录')
        # 预期结果 = '暂无'
        # 全局变量.错误校验点 = 非空校验(预期结果, 实际结果)
        # 结果 = 预期结果 in 实际结果
        错误校验点 = 为空校验(device)
        结果 = 实际结果 != ''
    return 结果, 错误校验点


@casescript('交易/基金交易/公募及私募基金/查询我的委托_当前委托')
def _2eb4fbaa6f3dc17d31af461c5d2f363e(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    错误校验点 = ''
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    上滑(device)
    点击(device, '/交易/普通交易/普通交易首页/基金交易')
    点击(device, '/交易/普通交易/普通交易首页/公募及私募基金')
    目录校验(device, '二', '公募及私募基金', '/页面标题')
    点击(device, '/交易/普通交易/基金交易/公募及私募基金/公募及私募基金首页/查询我的委托')
    sleep(2)
    点击(device, '/交易/普通交易/基金交易/公募及私募基金/公募及私募基金首页/查询我的委托/当前委托')
    if 获取控件对象(device, '/交易/普通交易/基金交易/公募及私募基金/公募及私募基金首页/查询我的委托/当前委托/当前委托第一条记录'):
        买卖方向 = 获取文本信息(device, '/交易/基金交易/公募及私募基金/查询我的委托/当前/第一条/买卖方向')
        if 买卖方向 == '其它':
            证券代码 = 获取文本信息(device, '/交易/普通交易/基金交易/公募及私募基金/撤销委托/撤单页面/其它代码')
        elif 买卖方向 == '申购':
            证券代码 = 获取文本信息(device, '/交易/普通交易/基金交易/公募及私募基金/撤销委托/撤单页面/申购代码')
        elif 买卖方向 == '赎回':
            证券代码 = 获取文本信息(device, '/交易/普通交易/基金交易/公募及私募基金/撤销委托/撤单页面/赎回代码')
        结果 = 证券代码 != ''
        if not 结果:
            错误校验点 = 为空校验(device)
    else:
        实际结果 = 获取控件对象(device, '/交易/普通交易/基金交易/公募及私募基金/公募及私募基金首页/撤销委托/无记录')
        # 预期结果 = '暂无当前委托订单'
        # 全局变量.错误校验点 = 非空校验(预期结果, 实际结果)
        # 结果 = 预期结果 in 实际结果
        错误校验点 = 为空校验(device)
        结果 = 实际结果 != ''
    return 结果, 错误校验点


