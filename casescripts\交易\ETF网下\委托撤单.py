from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('交易/ETF网下/委托撤单/委托撤单')
def _25049a7f645db30aef6d8ab9685810d6(device, 账号='01028889', 交易密码='123123', 通信密码='123123'):
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一','交易','/交易/选中状态')
    上滑(device)
    sleep(1)
    点击(device, '/交易/普通交易/普通交易首页/其他交易')
    目录校验(device, '二', '其他交易', '/页面标题')
    点击(device, '/交易/普通交易/其他交易/其他交易首页/ETF网下')
    点击(device, '/交易/普通交易/其他交易/其他交易首页/ETF网下_委托撤单')
    try:
        实际结果 = 获取文本信息(device, '/交易/普通交易/其他交易/其他交易首页/股转系统_委托查询/无记录')
    except:
        实际结果 = 获取文本信息(device, '/交易/普通交易/其他交易/其他交易首页/股转系统_委托查询/第一条记录')
    # 预期结果 = '现金认购'
    # 全局变量.错误校验点 = 非空校验(预期结果, 实际结果)
    # 结果 = 预期结果 in 实际结果
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


