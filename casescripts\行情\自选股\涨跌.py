from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('行情/自选股/涨跌/检查首支个股最新涨跌')
def _0067b601b29fed6534df35549e37ac63(device, 股票代码='600123'):
    错误校验点 = ''
    登录前页面处理(device)
    点击(device, '/底部导航栏/行情界面跳转按钮')
    目录校验(device, '一', '行情', '/行情/选中状态')
    点击(device, '/行情/自选股/自选股标签')
    目录校验(device, '二', '自选股', '/行情/自选股')
    try:
        第一条个股涨跌 = 获取文本信息(device, '/行情/自选股/涨跌/无记录')
        点击(device, '/行情/自选股/添加自选股')
        输入文本(device, '/行情/自选股/添加自选股/输入框', 股票代码)
        点击(device, '/行情/自选股/添加自选股/搜索')
        点击(device, '/行情/自选股/添加自选股/添加')
        点击(device, '/行情/自选股/添加自选股/返回')
        第一条个股涨跌 = 获取文本信息(device, '/行情/自选股/涨跌/第一条个股涨跌')
    except:
        第一条个股涨跌 = 获取文本信息(device, '/行情/自选股/涨跌/第一条个股涨跌')
    print('第一条个股涨跌::', 第一条个股涨跌)
    结果 = 第一条个股涨跌 != ''
    if 第一条个股涨跌 == '':
        错误校验点 = '无个股涨跌数据'
    return 结果, 错误校验点


