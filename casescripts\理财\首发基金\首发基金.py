from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('理财/首发基金/首发基金/首发基金')
def _73647a1cd44a5ca18bfd11fa9ccf84f7(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # 初始化
    登录前页面处理(device)
    理财登陆(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '理财', '/理财/选中状态')
    # time.sleep(2)
    # 上滑()
    # time.sleep(2)
    # 上滑()
    # # 循环滑动('/理财/快捷功能/华泰专属理财', '上')
    # time.sleep(5)
    滑动控件至屏幕内_理财(device, '/理财/首发基金')
    点击(device, '/理财/首发基金')
    目录校验(device, '二', '首发基金', '/页面标题')
    sleep(5)
    实际结果 = 获取文本信息(device, '/理财/首发基金/第一个基金名称')
    print(实际结果)
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


