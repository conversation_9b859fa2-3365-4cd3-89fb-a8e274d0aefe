from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('行情/市场/沪深/换手率榜/换手率榜')
def _3bdbfd82d67c9c26e777663a9de198e9(device):
    登录前页面处理(device)
    点击(device, '/底部导航栏/行情界面跳转按钮')
    目录校验(device, '一', '行情', '/行情/选中状态')
    点击(device, '/行情/市场/市场标题栏')
    目录校验(device, '二', '沪深', '/行情/更多/选中状态')
    滑动控件至屏幕内(device, '/行情/市场/沪深/换手率榜/换手率榜标签')
    点击(device, '/行情/市场/沪深/换手率榜/换手率榜标签')
    实际结果 = 获取文本信息(device, '/行情/市场/沪深/换手率榜/首只个股最新价')
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


