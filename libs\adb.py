import _thread
import datetime
import logging
import os
import re
import sys
import signal
from site import USER_BASE
import socket
import subprocess
import threading
import platform
from time import sleep
import inspect
import ctypes
from PIL import Image, ImageDraw, ImageFont

from libs.appium_server import APPIUM_SERVER_MAP, appium_server_start, _appium_port, _appium_ip, appium_server_finish, port_is_open
from libs.driver import DeviceDriver,ElementNotExist
from libs.log import log_control
from libs.sdk import *
from libs.traffic_monitor import get_traffic_monitor

from queue import Queue
q = Queue()

thread_dict={}
devices_s = []

_stdout_path = os.getcwd() + '/log/appium/'
if not os.path.exists(_stdout_path):
    os.makedirs(_stdout_path)

OS = platform.system()

def isExceptionRequests():
    try:
        # 先取eu本机配置（局域网）
        endpoint = NAS_URL if NAS_URL else caches.get('config.S3_IMAGE_ENDPOINT')
        nas_url='http://' + endpoint
        response = requests.get(nas_url,timeout=20).text
        if 'AccessDenied' in response:
            return True
        return False
    except:
        return False

# def upload_file(file_path,storageName,filename,authorization):
#     """
#     上传文件
#     """
#     try:
#         with open(file_path, 'rb') as file:
#             res = requests.post('http://***************:8077' + "/upload",
#                                 headers={'authorization': authorization},
#                                 data={'storageName': storageName,
#                                       'filename': filename},
#                                 files={'file': file})
#             result = res.json()
#             res.close()
#             # 注意，这里的 url 是不包含域名的。/admin/default/2022-10-31/wx.png
#             return result['url']
#     except:
#         return None

def upload_video(remote, local,type,authorization):
    if not os.path.exists(local):
        return None
    upload_type = caches.get('config.IMAGE_UPLOAD_USE', 'oss')
    bucket_name = caches.get('config.S3_IMAGE_BUCKET')

    if upload_type == 's3':
        # try:
        #     upload_file(local, bucket_name, remote.split('/')[-1], authorization)
        # except:
        #     oss_image_access_key_id = caches.get('config.OSS_IMAGE_ACCESS_KEY_ID', 'LTAIkJXCmgeSHlZk')
        #     oss_image_access_key_secret = caches.get('config.OSS_IMAGE_ACCESS_KEY_SECRET',
        #                                              '******************************')
        #     oss_image_bucket = caches.get('config.OSS_IMAGE_BUCKET','zfkj-cps')
        #     oss_image_endpoint = caches.get('config.OSS_IMAGE_ENDPOINT', 'http://oss-cn-hangzhou.aliyuncs.com')
        #     oss_image_auth = oss2.Auth(oss_image_access_key_id, oss_image_access_key_secret)
            
        #     if oss_image_access_key_id=='LTAI5t7MSZ6uKgzq1Hny5mXM':
        #         # 新oss id
        #         oss_bucket = oss2.Bucket(oss_image_auth, oss_image_endpoint, oss_image_bucket)
        #     else:
        #         oss_bucket = oss2.Bucket(oss_image_auth, 'http://oss-cn-qingdao.aliyuncs.com', 'case-video')

        #     for _ in range(3):
        #         try:
        #             oss_bucket.put_object_from_file(remote, local)
        #             break
        #         except:
        #             pass

        # # minio方式 先取eu本机配置（局域网）
        endpoint = NAS_URL if NAS_URL else caches.get('config.S3_IMAGE_ENDPOINT')
        if isExceptionRequests():
            minioClient = minio.Minio(endpoint,
                                      access_key=caches.get('config.S3_IMAGE_ACCESS_KEY_ID'),
                                      secret_key=caches.get('config.S3_IMAGE_ACCESS_KEY_SECRET'),
                                      secure=False)

            date = datetime.datetime.now().strftime('%Y-%m-%d')
            remote = remote.replace('video/', 'video/' + date + '/')
            for _ in range(3):
                try:
                    with open(local, 'rb') as file_data:
                        file_stat = os.stat(local)
                        minioClient.put_object(bucket_name, remote, file_data, file_stat.st_size, 'video/mp4')
                    break
                except:
                    pass
        else:
            oss_image_access_key_id = caches.get('config.OSS_IMAGE_ACCESS_KEY_ID', 'LTAI5t7MSZ6uKgzq1Hny5mXM')
            oss_image_access_key_secret = caches.get('config.OSS_IMAGE_ACCESS_KEY_SECRET',
                                                     '******************************')
            oss_image_endpoint = caches.get('config.OSS_IMAGE_ENDPOINT', 'http://oss-cn-hangzhou.aliyuncs.com')
            oss_image_auth = oss2.Auth(oss_image_access_key_id, oss_image_access_key_secret)
            
            if oss_image_access_key_id=='LTAI5t7MSZ6uKgzq1Hny5mXM':
                # 新oss id
                remote = bucket_name + '/' + remote
                oss_bucket = oss2.Bucket(oss_image_auth, oss_image_endpoint, oss_image_bucket)
            else:
                oss_bucket = oss2.Bucket(oss_image_auth, 'http://oss-cn-hangzhou.aliyuncs.com', 'zfkj-cps')

            for _ in range(3):
                try:
                    oss_bucket.put_object_from_file(remote, local)
                    break
                except:
                    pass
    else:
        oss_image_access_key_id = caches.get('config.OSS_IMAGE_ACCESS_KEY_ID', 'LTAI5t7MSZ6uKgzq1Hny5mXM')
        oss_image_access_key_secret = caches.get('config.OSS_IMAGE_ACCESS_KEY_SECRET', '******************************')
        oss_image_bucket = caches.get('config.OSS_IMAGE_BUCKET','zfkj-cps')
        oss_image_endpoint = caches.get('config.OSS_IMAGE_ENDPOINT', 'http://oss-cn-hangzhou.aliyuncs.com')
        oss_image_auth = oss2.Auth(oss_image_access_key_id, oss_image_access_key_secret)

        if oss_image_access_key_id=='LTAI5t7MSZ6uKgzq1Hny5mXM':
            # 新oss id
            remote = bucket_name + '/' + remote
            oss_bucket = oss2.Bucket(oss_image_auth, oss_image_endpoint, oss_image_bucket)
        else:
            oss_bucket = oss2.Bucket(oss_image_auth, 'http://oss-cn-hangzhou.aliyuncs.com', 'zfkj-cps')

        for _ in range(3):
            try:
                oss_bucket.put_object_from_file(remote, local)
                break
            except:
                pass

    # upload_file(local, bucket_name, remote.split('/')[-1], authorization)
    os.unlink(local)

def upload_logcat_file(remote, local):
    if not os.path.exists(local):
        return None
    upload_type = caches.get('config.IMAGE_UPLOAD_USE', 'oss')
    bucket_name = caches.get('config.S3_IMAGE_BUCKET')

    if upload_type == 's3':
        # # minio方式 先取eu本机配置（局域网）
        endpoint = NAS_URL if NAS_URL else caches.get('config.S3_IMAGE_ENDPOINT')
        if isExceptionRequests():
            minioClient = minio.Minio(endpoint,
                                      access_key=caches.get('config.S3_IMAGE_ACCESS_KEY_ID'),
                                      secret_key=caches.get('config.S3_IMAGE_ACCESS_KEY_SECRET'),
                                      secure=False)

            date = datetime.datetime.now().strftime('%Y-%m-%d')
            remote = remote.replace('logcat/', 'logcat/' + date + '/')
            for _ in range(3):
                try:
                    with open(local, 'rb') as file_data:
                        file_stat = os.stat(local)
                        minioClient.put_object(bucket_name, remote, file_data, file_stat.st_size, get_content_type(local))
                    break
                except:
                    pass
        else:
            oss_image_access_key_id = caches.get('config.OSS_IMAGE_ACCESS_KEY_ID', 'LTAI5t7MSZ6uKgzq1Hny5mXM')
            oss_image_access_key_secret = caches.get('config.OSS_IMAGE_ACCESS_KEY_SECRET',
                                                     '******************************')
            oss_image_endpoint = caches.get('config.OSS_IMAGE_ENDPOINT', 'http://oss-cn-hangzhou.aliyuncs.com')
            oss_image_auth = oss2.Auth(oss_image_access_key_id, oss_image_access_key_secret)
            
            if oss_image_access_key_id=='LTAI5t7MSZ6uKgzq1Hny5mXM':
                # 新oss id
                remote = bucket_name + '/' + remote
                oss_bucket = oss2.Bucket(oss_image_auth, oss_image_endpoint, oss_image_bucket)
            else:
                oss_bucket = oss2.Bucket(oss_image_auth, 'http://oss-cn-hangzhou.aliyuncs.com', 'zfkj-cps')

            for _ in range(3):
                try:
                    oss_bucket.put_object_from_file(remote, local)
                    break
                except:
                    pass
    else:
        oss_image_access_key_id = caches.get('config.OSS_IMAGE_ACCESS_KEY_ID', 'LTAI5t7MSZ6uKgzq1Hny5mXM')
        oss_image_access_key_secret = caches.get('config.OSS_IMAGE_ACCESS_KEY_SECRET', '******************************')
        oss_image_bucket = caches.get('config.OSS_IMAGE_BUCKET','zfkj-cps')
        oss_image_endpoint = caches.get('config.OSS_IMAGE_ENDPOINT', 'http://oss-cn-hangzhou.aliyuncs.com')
        oss_image_auth = oss2.Auth(oss_image_access_key_id, oss_image_access_key_secret)

        if oss_image_access_key_id=='LTAI5t7MSZ6uKgzq1Hny5mXM':
            # 新oss id
            remote = bucket_name + '/' + remote
            oss_bucket = oss2.Bucket(oss_image_auth, oss_image_endpoint, oss_image_bucket)
        else:
            oss_bucket = oss2.Bucket(oss_image_auth, 'http://oss-cn-hangzhou.aliyuncs.com', 'zfkj-cps')

        for _ in range(3):
            try:
                oss_bucket.put_object_from_file(remote, local)
                break
            except:
                pass
    # os.unlink(local)


def get_adb_devices():
    p = subprocess.Popen('adb devices', shell=True, stdout=subprocess.PIPE)
    p.stdout.readline()
    devices_list = []
    for line in p.stdout:
        line = line.decode().strip()
        if len(line) > 0:
            d = line.split('\t')
            if len(d) >= 2 and d[1] == 'device':
                devices_list.append(d[0])
    p.wait()
    return devices_list


def adb_reconnect_offline():
    # close_fds=True
    if OS == "Darwin":
        p_r = subprocess.Popen('adb reconnect offline', shell=True,close_fds=True, stdout=subprocess.PIPE)
        p_r.stdout.readline()
    else:
        p_r = subprocess.Popen('adb reconnect offline', shell=True, stdout=subprocess.PIPE)
        p_r.stdout.readline()

    reconnect_devices_list = []
    for line in p_r.stdout:
        line = line.decode().strip()
        if len(line) > 0:
            d = line.split('\t')
            if len(d) >= 2 and d[0] == 'reconnecting':
                reconnect_devices_list.append(d[1])
    p_r.wait()
    return reconnect_devices_list


# adb局域网模式，获取手机device_id  ip
def get_eu_devices():
    try:
        eu_devices = cloud_api('get_eu_devices', eu_id=EU_ID)
        return eu_devices['devices']
    except Exception:
        return []


# 局域网连接adb
def connect_adb():
    devices_list = get_adb_devices()

    eu_devices = get_eu_devices()

    ip_port_device_dict = {}

    for device in eu_devices:
        device_connect_url = device['ip']+':'+device['eu_adb_port']
        
        if (device_connect_url not in devices_list) and device['ip']!='' and device['eu_adb_port'] != '':
            # 创建设备与eu本地端口 监听关系
            # p_c = subprocess.Popen('adb -s %s tcpip %s' % (device['number'], device['eu_adb_port']), shell=True, stdout=subprocess.PIPE)
            # d_c = p_c.stdout.readline()

            # 设备与eu 创建链接
            p_l = subprocess.Popen('adb connect %s:%s' % (device['ip'], device['eu_adb_port']), shell=True, stdout=subprocess.PIPE)
            d_l = p_l.stdout.readline()
            print(d_l)

            ip_port = device['ip'] + ':' + device['eu_adb_port']
            ip_port_device_dict[ip_port] = device['number']
        else:
            ip_port = device['ip'] + ':' + device['eu_adb_port']
            ip_port_device_dict[ip_port] = device['number'] 
    
    caches.set('ip_port_device_dict', ip_port_device_dict, 86400)       


DEVICES_LIST = []


def _listen_callback():
    while True:
        if WIRED=='true':
            # adb已连接设备
            new_devices = get_adb_devices()

            # ip:端口 与 device_id 对应关系
            ip_port_device_dict = caches.get('ip_port_device_dict')
            
            for device_ip_port in new_devices:

                device_id = ip_port_device_dict[device_ip_port]
                if device_id in DEVICES_LIST:
                    continue

                log_control.debug("连接", device_id)
                local_device = LocalDevice(device_id,adb_connect=device_ip_port,eu_adb_port = device_ip_port.split(':')[1],ip=device_ip_port.split(':')[0])
                register_device(local_device)
                cloud_api('device-status', device=device_id, status='online')
                for func in _listen_callback.connect:
                    func(local_device)
                DEVICES_LIST.append(device_id)
                sleep(5)
                local_device.appium_server_start()  # 启动appium服务端
                sleep(10)

            for device_id in DEVICES_LIST:
                device_ip_port = list(ip_port_device_dict.keys())[list(ip_port_device_dict.values()).index(device_id)]

                if device_ip_port in new_devices:
                    continue
                log_control.debug("断开", device_id)
                local_device = LocalDevice(device_id,adb_connect=device_ip_port,eu_adb_port = device_ip_port.split(':')[1],ip=device_ip_port.split(':')[0])
                cloud_api('device-status', device=device_id, status='offline')
                local_device.clean_appium()  # 清理appium
                for func in _listen_callback.disconnect:
                    func(local_device)
                DEVICES_LIST.remove(device_id)

            sleep(10)
        
        else:
            # usb线连接设备模式
            new_devices = get_adb_devices()
            # log_control.info('adb在线设备：【%s】' % ";".join(new_devices))

            for device_id in new_devices:
                if device_id in DEVICES_LIST:
                    continue

                log_control.debug("连接", device_id)
                local_device = LocalDevice(device_id)
                register_device(local_device)
                cloud_api('device-status', device=device_id, status='online')
                for func in _listen_callback.connect:
                    func(local_device)
                DEVICES_LIST.append(device_id)
                sleep(5)
                local_device.appium_server_start()  # 启动appium服务端
                sleep(10)

            # log_control.info('eu中在线设备：【%s】' % ";".join(DEVICES_LIST))
            for device_id in DEVICES_LIST:
                if device_id in new_devices:
                    continue
                log_control.debug("断开", device_id)
                cloud_api('device-status', device=device_id, status='offline')
                local_device = LocalDevice(device_id)
                local_device.clean_appium()  # 清理appium
                for func in _listen_callback.disconnect:
                    func(local_device)
                DEVICES_LIST.remove(device_id)
            sleep(2)
            
            if AUTO_RESTART=='true':
                print(thread_dict)
                # eu adb 中设备在线，平台上设备离线，重启appium
                eu_devices = get_eu_devices()
                for device in eu_devices:
                    if device['status'] == 'offline' and device['number'] in new_devices and device['number'] in APPIUM_SERVER_MAP:
                        if device['number'] in thread_dict:
                            log_control.info("线程重启：【%s】", device['number'])
                            log_control.info("线程【%s】",str(thread_dict[device['number']]))
                            try:

                                log_control.info('关闭线程之前' + str(thread_dict[device['number']].isAlive()))
                                stop_thread(thread_dict[device['number']])
                                thread_dict[device['number']].join()
                                log_control.info('关闭线程之后' + str(thread_dict[device['number']].isAlive()))
                            except Exception as e:
                                log_control.info(e)

                            DEVICES_LIST.remove(device['number'])
                            devices_s.remove(device['number'])
                            sleep(3)

                            local_device = LocalDevice(device['number'])
                            register_device(local_device)
                            log_control.info("更新在线")
                            cloud_api('device-status', device=device['number'], status='online')

                            for func in _listen_callback.connect:
                                func(local_device)
                            DEVICES_LIST.append(device['number'])
                            sleep(5)
                            local_device.appium_server_start()  # 启动appium服务端
                            sleep(10)
                sleep(20)
            reconnect_devices_list = adb_reconnect_offline()
            for device in reconnect_devices_list:
                log_control.info('断线重连：【%s】' % str(device))
            sleep(5)
    pass


_listen_callback.connect = []
_listen_callback.disconnect = []
_listen_callback.thread = False


def listen_device_connect(func):
    """
    对一个方法使用此装饰器来启动一个线程，监听所有新接入的设备
    :type func: function
    """
    _listen_callback.connect.append(func)
    if not _listen_callback.thread:
        _thread.start_new_thread(_listen_callback, ())
        _listen_callback.thread = True
    return func


def listen_device_disconnect(func):
    """
    对一个方法使用此装饰器来启动一个线程，当设备断开时触发
    :return function
    """
    _listen_callback.disconnect.append(func)
    if not _listen_callback.thread:
        _thread.start_new_thread(_listen_callback, ())
        _listen_callback.thread = True
    return func


def async_func(func):
    """
    将方法在新的线程中执行
    :param function func:
    :return:
    """

    def wrapper(*args, **kwargs):
        # thr = _thread.start_new_thread(func, args, kwargs)
        thr = threading.Thread(target=func, daemon=False,args=args, kwargs=kwargs)
        thr.start()
        if len(args)>0:
            if hasattr(args[0],"device_id"):
                print(thr.isAlive())
                thread_dict[args[0].device_id]=thr

    return wrapper


def synchronized(func):
    """
    让方法线程安全。
    :type func:function
    """
    func.__lock__ = threading.Lock()

    def lock_func(*args, **kwargs):
        with func.__lock__:
            return func(*args, **kwargs)

    return lock_func


def _async_raise(tid, exctype):
    """Raises an exception in the threads with id tid"""
    if not inspect.isclass(exctype):
        raise TypeError("Only types can be raised (not instances)")
    res = ctypes.pythonapi.PyThreadState_SetAsyncExc(ctypes.c_long(tid), ctypes.py_object(exctype))
    if res == 0:
        raise ValueError("invalid thread id")
    elif res != 1:
        # """if it returns a number greater than one, you're in trouble,
        # and you should call it again with exc=NULL to revert the effect"""
        ctypes.pythonapi.PyThreadState_SetAsyncExc(tid, None)
        raise SystemError("PyThreadState_SetAsyncExc failed")


def stop_thread(thread):
    _async_raise(thread.ident, SystemExit)


def increase_int() -> int:
    increase_int.MAX += 1
    return increase_int.MAX


increase_int.MAX = 0

local_device_lock = threading.Lock()

_instance = {}  # multiprocessing.Manager().dict()


class LocalDevice(object):

    def __new__(cls, *args, **kwargs):
        device_id = args[0]
        local_device_lock.acquire()
        global _instance
        if _instance.get(device_id) is None:
            _instance[device_id] = super().__new__(cls)
        local_device_lock.release()
        return _instance[device_id]

    def __init__(self, device_id,adb_connect='',ip='',eu_adb_port=''):
        self.log = logging.getLogger(device_id)
        self.device_id = device_id
        # 无线adb模式下，获取devices 为ip:port 连接，用于无线模式下连接 adb shell
        self.adb_connect = adb_connect if adb_connect !='' else device_id
        # 无线adb模式下端口
        self.eu_adb_port = eu_adb_port
        # 无线adb模式下设备局域网ip
        self.ip = ip

        self.record_proces = None

        # 设备正在运行的用例
        self.running_case_id = ''
        self.result_id = ''
        
        # 首屏启动时间
        self.cold_start_time = 0
        self.msg_result = ''

        # 流量监控器
        self._traffic_monitor = None


    def get_element_images(self,path):
        params = {
            'path': path
        }
        _view_element_image = caches.get('config.VIEW_ELEMENT_IMAGE',
                                         'http://cpspoc.njzfit.cn/api/get_view_element_image')
        image_json = requests.get(_view_element_image, params=params).json()
        image_cv = ''
        if image_json['code'] == 200:
            imgdata = base64.b64decode(image_json['base64_data'])
            file = open(local_element_image_path, 'wb')
            file.write(imgdata)
            file.close()
            nparr = np.fromstring(imgdata, np.uint8)
            image_cv = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
        if image_cv:
            parent_image = str(int((time.time() * 10000))) + ".png"
            self.driver.save_screenshot(parent_image)
            parent_image_cv = cv2.imread(filename)  # 要找的大图
            has_image, location = find_element_image(image_cv, parent_image_cv)
            return [has_image,location]
        return False

    def shell(self, command):
        p = subprocess.Popen('adb -s %s shell "%s"' % (self.adb_connect, command), shell=True, stdout=subprocess.PIPE)
        d = p.stdout.readline()
        return d.strip().decode()

    def network_check(self):
        self.log.info('开始网络检测')
        host = caches.get('config.FAIL_NETWORK_CHECK_PING_HOST')
        sh = self.shell('"ping -c 3 %s|grep rtt"' % host)
        try:
            if sh:
                self.log.info('网络检测：%s' % sh)
                f = float(sh.split(' = ').pop().split('/')[2])
                if f < float(caches.get('config.FAIL_NETWORK_CHECK_PING_TIMEOUT')):
                    return True
            return False
        except:
            return False

    def shell_getprop(self, name):
        return self.shell('getprop %s' % name)

    case_running = None
    case_start_time = None
    find_element_search_map = None

    find_element_search_last = None

    screenrecord_last_reload = None

    def case_start(self, running):
        self.case_running = running
        self.case_start_time = datetime.datetime.now()

    def case_finish(self):
        self.case_running = None
        start = self.case_start_time
        self.case_start_time = None
        return datetime.datetime.now() - start

    _info = None

    @property
    def device_ip(self):
        if not isWin:
            _device_ip = self.shell('ip addr show wlan0 | grep "inet "| cut -f1 -d "/" |cut -f2 -d "t"')
            return _device_ip
        return ''

    @property
    def pid(self):
        _pid = self.shell('pidof %s' % self.app['package_name']).split(' ')[0]
        return _pid
    
    @property
    def device_wifi(self):
        if not isWin:
            # _device_wifi = self.shell('dumpsys netstats | grep -E "iface=wlan.*networkId"|grep -o '".*"' -m1')
            _device_wifi_str = self.shell('dumpsys netstats | grep -E "iface=wlan.*networkId" -m1')
            if 'networkId=' in _device_wifi_str:
                _s = _device_wifi_str.split('networkId=')[1]
                if ',' in _s:
                    return _s.split(',')[0].replace('"','')
        return ''

    @property
    def level(self):
        if self._info is None:
            self._info = dict()
        if self._info.get('level') is None:
            self._info['level'] = self.shell_getprop('ro.build.version.release')
        return self._info['level']
    
    @property
    def record_level(self):
        if self._info is None:
            self._info = dict()
        if self._info.get('record_level') is None:
            if isWin:
                p = subprocess.Popen('adb -s %s shell dumpsys package com.zfkj.ybc.screenrecord | findstr versionName' % (self.adb_connect), shell=True, stdout=subprocess.PIPE)
                d = p.stdout.readline()
                record_level = d.strip().decode()
            else:
                p = subprocess.Popen('adb -s %s shell dumpsys package com.zfkj.ybc.screenrecord | grep versionName' % (self.adb_connect), shell=True, stdout=subprocess.PIPE)
                d = p.stdout.readline()
                record_level = d.strip().decode()
                
            if '=' in record_level:
                self._info['record_level'] = record_level.split('=')[1]
            else:
                self._info['record_level'] = ''
        return self._info['record_level']
    
    @property
    def app_level(self):
        if self._info is None:
            self._info = dict()
        if self._info.get('app_level') is None and 'package_name' in self.app:
            if isWin:
                p = subprocess.Popen('adb -s %s shell dumpsys package %s | findstr versionName' % (self.adb_connect,self.app['package_name']), shell=True, stdout=subprocess.PIPE)
                d = p.stdout.readline()
                app_level = d.strip().decode()
            else:
                if OS == "Darwin":
                    p = subprocess.Popen('adb -s %s shell dumpsys package %s | grep versionName' % (self.adb_connect,self.app['package_name']), shell=True,close_fds=True, stdout=subprocess.PIPE)
                else:
                    p = subprocess.Popen('adb -s %s shell dumpsys package %s | grep versionName' % (self.adb_connect,self.app['package_name']), shell=True, stdout=subprocess.PIPE)
                d = p.stdout.readline()
                app_level = d.strip().decode()
                
            if '=' in app_level:
                self._info['app_level'] = app_level.split('=')[1]
            else:
                self._info['app_level'] = ''
        # 华龙获取版本号
        if caches.get('config.app_version', 'false') == 'true' and 'package_name' in self.app:
            if isWin:
                p = subprocess.Popen('adb -s %s shell dumpsys package %s | findstr versionName' % (
                self.adb_connect, self.app['package_name']), shell=True, stdout=subprocess.PIPE)
                d = p.stdout.readline()
                app_level = d.strip().decode()
            else:
                if OS == "Darwin":
                    p = subprocess.Popen('adb -s %s shell dumpsys package %s | grep versionName' % (
                    self.adb_connect, self.app['package_name']), shell=True, close_fds=True, stdout=subprocess.PIPE)
                else:
                    p = subprocess.Popen('adb -s %s shell dumpsys package %s | grep versionName' % (
                    self.adb_connect, self.app['package_name']), shell=True, stdout=subprocess.PIPE)
                d = p.stdout.readline()
                app_level = d.strip().decode()

            if '=' in app_level:
                self._info['app_level'] = app_level.split('=')[1]
            else:
                self._info['app_level'] = ''
        return self._info['app_level']

    @property
    def model(self):
        if self._info is None:
            self._info = dict()
        if self._info.get('model') is None:
            self._info['model'] = self.shell_getprop('ro.product.model')
        return self._info['model']

    @property
    def os_version(self):
        if self._info is None:
            self._info = dict()
        if self._info.get('os_version') is None:
            self._info['os_version'] = self.shell_getprop('ro.build.version.incremental')
        return self._info['os_version']
    
    @property
    def brand(self):
        # 手机品牌
        if self._info is None:
            self._info = dict()
        if self._info.get('brand') is None:
            self._info['brand'] = self.shell_getprop('ro.product.brand')
        return self._info['brand']
    
    @property
    def screen_size(self):
        if self._info is None:
            self._info = dict()
        if self._info.get('screen_size') is None:
            size = self.shell('wm size')
            size = size.split(':')[1]
            self._info['screen_size'] = size.strip()
        return self._info['screen_size']

    @property
    def screen_width(self):
        return int(self.screen_size.split('x')[0])

    @property
    def screen_height(self):
        return int(self.screen_size.split('x')[1])

    def adb_command(self, shell):
        if OS == 'Windows':
            p = subprocess.Popen("adb -s %s %s" % (self.adb_connect, shell), shell=True,
                                 stdout=subprocess.DEVNULL, creationflags=subprocess.CREATE_NEW_PROCESS_GROUP)
        else:
            p = subprocess.Popen("adb -s %s %s" % (self.adb_connect, shell), shell=True,
                                 stdout=subprocess.DEVNULL)
        while p.poll() is None:
            sleep(0.1)
        sleep(1)

    def pull(self, device_path, local_path):
        # self.log.info('传输开始 pull %s %s' % (device_path, local_path))

        if OS == 'Windows':
            p = subprocess.Popen("adb -s %s pull %s %s" % (self.adb_connect, device_path, local_path), shell=True,
                                 stdout=subprocess.DEVNULL, creationflags=subprocess.CREATE_NEW_PROCESS_GROUP)
        else:
            p = subprocess.Popen("adb -s %s pull '%s' '%s'" % (self.adb_connect, device_path, local_path), shell=True,
                                 stdout=subprocess.DEVNULL)
        while p.poll() is None:
            sleep(0.1)
        sleep(2)
        # self.log.info('传输结束')

    def push(self, local_path, device_path):
        self.log.info('传输开始 push %s %s' % (device_path, local_path))

        if OS == 'Windows':
            p = subprocess.Popen("adb -s %s push %s %s" % (self.adb_connect, local_path, device_path), shell=True,
                                 stdout=subprocess.DEVNULL, creationflags=subprocess.CREATE_NEW_PROCESS_GROUP)
        else:
            p = subprocess.Popen("adb -s %s push '%s' '%s'" % (self.adb_connect, local_path, device_path), shell=True,
                                 stdout=subprocess.DEVNULL)
        while p.poll() is None:
            sleep(0.1)
        self.log.info('传输结束  pull %s %s' % (device_path, local_path))
        sleep(2)

    screencap_history = None


    def cv2ImgAddText(self,img, text, left, top, textColor=(0, 255, 0), textSize=20):
        if (isinstance(img, numpy.ndarray)):  # 判断是否OpenCV图片类型
            img = Image.fromarray(cv2.cvtColor(img, cv2.COLOR_BGR2RGB))
        # 创建一个可以在给定图像上绘图的对象
        draw = ImageDraw.Draw(img)
        # 字体的格式

        fontStyle = ImageFont.truetype("./fonts/simsun.ttc", textSize, encoding="utf-8")
        # 绘制文本
        draw.text((left, top), text, textColor, font=fontStyle)
        # 转换回OpenCV格式
        return cv2.cvtColor(numpy.asarray(img), cv2.COLOR_RGB2BGR)

    def add_msg_result(self, msg):
        """
        自定义执行结果展示
        """
        self.msg_result = msg

    def add_pictures(self,path):
        """
        添加图片日志
        """
        from shutil import copyfile
        if not self.screencap_history:
            self.screencap_history = []
        _t = time.strftime('%Y/%m/%d')
        _t2 = time.strftime('%H_%M_%S')
        dir_path = 'log/video/%s/%s' % (_t, self.device_id.replace(':', ''))
        if not os.path.exists(dir_path):
            os.makedirs(dir_path)
        localpath = '%s/%s.png' % (dir_path, _t2 + "_" + str(int(time.time()*1000)))
        copyfile(path,localpath)
        self.screencap_history.append(localpath)

        if log_join == 'true':
            _name_flag = localpath.split('/')[-2] + '/' + localpath.split('/')[-1] if len(localpath.split('/')) > 2 else localpath
            self.log.info('自定义图像$$%s' % (_name_flag))
        

    def screencap(self, localpath=None, with_uix=False,name='',ele='',tips=''):
        if localpath is None:
            _t = time.strftime('%Y/%m/%d')
            _t2 = time.strftime('%H_%M_%S')
            dir_path = 'log/video/%s/%s' % (_t, self.device_id.replace(':', ''))
            if not os.path.exists(dir_path):
                os.makedirs(dir_path)
            
            # 华泰需求 埋点页面截图处理
            if name == '':
                localpath = '%s/%s.png' % (dir_path, _t2)
            else:
                localpath = '%s/%s___%s.png' % (dir_path, _t2,name)

            self.screencap_history = list() if self.screencap_history is None else self.screencap_history
            self.screencap_history.append(localpath)
            if with_uix:
                _page_source_localpath = localpath.replace('.png', '.uxi')
                self.shell("uiautomator dump /sdcard/app.uix")
                self.pull("/sdcard/app.uix", os.path.abspath(_page_source_localpath).replace('\\', '/'))
                self.shell("rm /sdcard/app.uix")
                self.screencap_history.append(_page_source_localpath)
        
        if log_join == 'true':
            _name_flag = localpath.split('/')[-2] + '/' + localpath.split('/')[-1] if len(localpath.split('/')) > 2 else localpath
            self.log.info('页面截屏$$%s' % (_name_flag))

        _i = increase_int()
        dir_path = os.path.dirname(localpath)
        if not os.path.exists(dir_path):
            os.makedirs(dir_path)
        self.shell("screencap -p /sdcard/%s.png" % _i)
        self.pull("/sdcard/%s.png" % _i, os.path.abspath(localpath).replace('\\', '/'))
        self.shell("rm /sdcard/%s.png" % _i)
        
        # 埋点控件定位标注
        if name!='' and ele !='':
            try:
                import cv2
                if ele:
                    left = ele.location[0]
                    top = ele.location[1]
                    right = ele.location[0] + ele.size[0]
                    bottom = ele.location[1] + ele.size[1]
                    img_path = os.path.abspath(localpath).replace('\\', '/')
                    if os.path.exists(img_path):
                        img = cv2.imread(img_path)
                        pt1 = (left, top) #左边，上边   #数1 ， 数2
                        pt2 = (right, bottom) #右边，下边

                        cv2.rectangle(img, pt1, pt2, (0, 255, 0), 8)

                        if tips !='':
                            left=100
                            top = 1000
                            tips_str = re.sub(r"(.{10})", "\\1\r\n", tips)
                            img = self.cv2ImgAddText(img, tips_str, left, top, (0, 0, 255), 100)

                            # tips_str = tips
                            # font = cv2.FONT_HERSHEY_SIMPLEX  # 定义字体
                            # imgzi = cv2.putText(img, tips_str, (left, top-15), font, 2, (220, 20, 60), 6)
                        else:
                            font = cv2.FONT_HERSHEY_SIMPLEX  # 定义字体
                            imgzi = cv2.putText(img, '{}'.format(name), (left, top-15), font, 2, (220, 20, 60), 6)
                                        # 图像，      文字内容，      坐标(右上角坐标)，字体， 大小，  颜色，    字体厚度
                        
                        cv2.imwrite(img_path, img)
            except Exception as e:
                print(e)

        pass

    _on_screenrecord = None

    def screenrecord_reload(self,case_id):
        if self.record_level=='1.3.2':
            print('重启录屏')
            self.shell('am force-stop com.zfkj.ybc.screenrecord')
            sleep(1)
            self.shell('am start -n com.zfkj.ybc.screenrecord/com.zfkj.ybc.screenrecord.activity.MainActivity')
            sleep(2)
            self.screenrecord_last_reload = time.time()
        else:
            self.screenrecord_last_reload = time.time()
            print('重启录屏')
            self.begin_record(case_id)

    @hide_error
    def screenrecord_start(self, case_id, size="540x960", bit_rate=500000, time_limit=180):
        if self._on_screenrecord is not None:
            raise Exception('有其他进程正在录屏')

        self._on_screenrecord = case_id

        if self.record_level=='1.3.2':
            if self.screenrecord_last_reload is None:
                self.screenrecord_last_reload = time.time()

            elif request.get('http://%s/%s?count=write' % (SOCKET_HOST,self.device_id)).json().get(self.device_id) < 2:
                self.screenrecord_reload(case_id)
                import json
                message = json.dumps({'type': 'start', 'uuid': case_id, 'device': self.device_id}, ensure_ascii=False, separators=(',', ':'))
                request.get('http://%s/%s' % (SOCKET_HOST,self.device_id), params={'message': message})
        else:
            # 新版录屏
            if self.screenrecord_last_reload is None:
                self.screenrecord_last_reload = time.time()
                self.begin_record(case_id)
            else:
                self.screenrecord_reload(case_id)


    def begin_record(self,case_id):
        if record_type == 'scrcpy':
            _port = scrcpy_port(self.device_id)
            local_path = 'log/video_cache/'
            if not os.path.exists(os.path.dirname(local_path)):
                os.makedirs(os.path.dirname(local_path))
            
            if not port_is_open("127.0.0.1",_port):
                # _sh = 'scrcpy --serial {} -m 1024 -p {} --no-display --record {}'.format('"%s"' % self.device_id, _port, local_path + case_id + ".mp4")
                _sh = 'scrcpy --serial {} -m 1024 -p {} --no-audio --no-playback --record {}'.format('"%s"' % self.device_id, _port, local_path + case_id + ".mp4")
                _p = subprocess.Popen(_sh, shell=True, stdin=subprocess.PIPE, stdout=subprocess.PIPE)
                record_p_map[self.device_id]  = _p

        else:
            self.shell('am force-stop com.zfkj.ybc.screenrecord')
            sleep(0.5)
            self.shell('am start -n com.zfkj.ybc.screenrecord/com.zfkj.ybc.screenrecord.activity.MainActivity --es type start --es uuid %s'% case_id)
            sleep(1)
            self.click_start()

    def click_start(self):
        # 点击开始录屏
        if (self.model == 'Pixel 5' or self.model == 'AOSP on redfin'):
            # pixel5
            self.shell('input tap 500 230')
        else:
            self.shell('input tap 500 150')
        sleep(1)
        if int(self.level)>9:
            if (self.model=='V2148A'):
                self.shell('input tap 840 1580')
                sleep(0.5)
                self.shell('input tap 840 1580')
            elif (self.model=='PGCM10'):
                self.shell('input tap 780 1440')
                sleep(0.5)
                self.shell('input tap 780 1440')
            elif (self.model=='NCO-AL00'):
                # nova10
                # 低分辨率模式720*1600
                # self.shell('input tap 500 1500')
                # sleep(1)
                # self.shell('input tap 500 1500')
                self.shell('input tap 800 2250')
                sleep(0.5)
                self.shell('input tap 800 2250')
            elif (self.model == 'CLK-AN00'):
                self.shell('input tap 780 2280')
                sleep(0.5)
                self.shell('input tap 780 2280')
            elif (self.model == 'LLY-AN00'):
                # X50i+
                self.shell('input tap 780 2280')
                sleep(0.5)
                self.shell('input tap 780 2280')
            elif (self.model == 'PJH110'):
                # OPPO R11
                self.shell('input tap 750 1470')
                sleep(0.5)
                self.shell('input tap 750 1470')
            elif (self.model == '2312DRA50C'):
                # Redmi N13 pro
                if self.os_version.startswith('OS2'):
                    # 小米OS2
                    # 点击应用范围
                    self.shell('input tap 940 1990')
                    sleep(0.5)
                    # 点击整个屏幕
                    self.shell('input tap 820 2270')
                    sleep(0.5)
                    # 点击开始
                    self.shell('input tap 900 2480')
                    sleep(0.5)
                    self.shell('input tap 900 2480')
                elif self.os_version.startswith('V14'):
                    # miui系统 V14
                    self.shell('input tap 980 1720')
                    sleep(0.5)
                    self.shell('input tap 980 1720')
                else:
                    # OS1.0
                    self.shell('input tap 850 2500')
                    sleep(0.5)
                    self.shell('input tap 850 2500')

            elif (self.model == 'LYN-AN00'):
                # x60i
                self.shell('input tap 770 2290')
                sleep(0.5)
                self.shell('input tap 770 2290')
            elif (self.model == 'TFY-AN00'):
                # x30i
                self.shell('input tap 770 2260')
                sleep(0.5)
                self.shell('input tap 770 2260')
            elif (self.model == 'Pixel 5' or self.model == 'AOSP on redfin'):
                # pixel5
                self.shell('input tap 860 1520')
                sleep(1)
                self.shell('input tap 860 1520')
            elif (self.model == 'M2104K10AC'):
                # pixel5
                self.shell('input tap 850 1600')
                sleep(1)
                self.shell('input tap 850 1600')
            elif (self.model == 'FIN-AL60'):
                # nova 12
                self.shell('input tap 780 2250')
                sleep(0.5)
                self.shell('input tap 780 2250')
            elif (self.model == 'V2323A'):
                # vivo S18
                self.shell('input tap 630 2380')
                sleep(0.5)
                self.shell('input tap 630 2380')
            else:
                # 点击权限 安卓11
                self.shell('input tap 700 2200')
                sleep(0.5)
                self.shell('input tap 700 2200')

            # 安卓10  honor 9xp  2340*1080
            # self.shell('input tap 848 1357')

        # 回到桌面
        self.shell('input keyevent 3')
        self.log.info("开始录屏")
        # self._appium_driver.driver.start_activity()

    def is_screenrecord_on(self):
        return self._on_screenrecord is not None

    def screenrecord_finish(self, case_id,status,has_success):
        if record_type == 'scrcpy':
            if self._on_screenrecord is None:
                self.log.info("未启动录屏")
                return
            self._on_screenrecord = None
        
            if record_p_map[self.device_id]:
                kill_child_processes(record_p_map[self.device_id].pid)
                sleep(0.5)
                try:
                    local_path = 'log/video_cache/%s.mp4' % case_id

                    if os.path.exists(local_path):

                        if status is True:
                            status_str = 'success'
                        elif status is False:
                            status_str = 'fail'
                        else:
                            status_str = 'error'
                    
                        if (status_str in UPLOAD_STATUS_LIST) or (status_str =='success' and has_success==0):
                            self.log.info("开始上传视频")
                            # upload_video('video/%s.mp4' % case_id, local_path)
                            q.put(('video/%s.mp4' % case_id, local_path))
                            # t = threading.Thread(target=upload_video, args=('video/%s.mp4' % case_id, local_path))
                            # t.start()
                            self.log.info("上传完成")
                            # os.unlink(local_path)
                        else:
                            os.unlink(local_path)
                except Exception as e:
                    self.log.info(e)
        else:
            if self.record_level=='1.3.2':
                if self._on_screenrecord is None:
                    self.log.info("未启动录屏")
                    return
                self._on_screenrecord = None
            else:
                self.log.info("结束录屏")
                if self._on_screenrecord is None:
                    self.log.info("未启动录屏")
                    return
                self._on_screenrecord = None
                # self.shell('am start -n com.zfkj.ybc.screenrecord/com.zfkj.ybc.screenrecord.activity.MainActivity --es type finish --es uuid %s'% case_id)
                # sleep(1)
                
                self.shell('am start -n com.zfkj.ybc.screenrecord/com.zfkj.ybc.screenrecord.activity.MainActivity')
                sleep(0.5)

                # 点击结束录屏
                if (self.model == 'Pixel 5' or self.model == 'AOSP on redfin'):
                    # pixel5
                    self.shell('input tap 500 230')
                else:
                    self.shell('input tap 500 150')
                
                sleep(1)
                if int(self.level)>10:
                    # 点击权限 防止弹出层错误出现
                    self.shell('input tap 700 2200')
                    sleep(0.5)
                # kill app
                self.shell('am force-stop com.zfkj.ybc.screenrecord')

                try:
                    local_path = 'log/video_cache/%s.mp4' % case_id
                    re_path = '/sdcard/zfkj_screen_record/video/%s.mp4' % case_id
                    if not os.path.exists(os.path.dirname(local_path)):
                        os.makedirs(os.path.dirname(local_path))

                    if status is True:
                        status_str = 'success'
                    elif status is False:
                        status_str = 'fail'
                    else:
                        status_str = 'error'
                    
                    UPLOAD_FIRST_SUCCESS = caches.get('config.UPLOAD_FIRST_SUCCESS', 'false')

                    if (status_str in UPLOAD_STATUS_LIST) or (status_str =='success' and has_success==0 and UPLOAD_FIRST_SUCCESS == 'true'):
                        self.pull(re_path, local_path)

                        self.log.info("开始上传视频")
                        # upload_video('video/%s.mp4' % case_id, local_path)
                        q.put(('video/%s.mp4' % case_id, local_path))
                        # t = threading.Thread(target=upload_video, args=('video/%s.mp4' % case_id, local_path))
                        # t.start()
                        self.log.info("上传完成")
                    
                        self.shell('rm %s ' % re_path)
                        # os.unlink(local_path)
                    else:
                        self.shell('rm %s ' % re_path)
                except Exception as e:
                    self.log.info(e)


    def clear_logcat(self):
        print('清理logcat缓存')
        self.shell('logcat -c')

    def output_logcat(self,case_id):
        print('导出logcat日志')
        _t = time.strftime('%Y/%m/%d')
        dir_path = 'log/logcat/%s/%s' % (_t, self.device_id)
        if not os.path.exists(dir_path):
            os.makedirs(dir_path)
        # _sh = 'adb -s '+ self.device_id +' shell logcat -d -v time *:E >' + dir_path +'/'+ case_id + '.txt'
        # p = subprocess.Popen(_sh, shell=True, stdout=subprocess.PIPE)

        if logcat == 'true':

            _pid = self.pid

            if perf_log == 'true':
                # _sh = 'adb -s '+ self.device_id +' shell logcat --pid ' + _pid + ' -s moirai System -d  >' + dir_path +'/'+ case_id + '.txt'
                _sh = 'adb -s '+ self.device_id +' shell logcat *:E moirai:V -d  >' + dir_path +'/'+ case_id + '.txt'
            else:
                _sh = 'adb -s '+ self.device_id +' shell logcat -d -v time *:E >' + dir_path +'/'+ case_id + '.txt'

            p = subprocess.Popen(_sh, shell=True, stdout=subprocess.PIPE)

            local_path = dir_path +'/'+ case_id + '.txt'

            if upload_logcat == 'true':
                upload_logcat_file('logcat/%s.txt' % case_id, local_path)


    _appium_server = None
    _appium_subprocess = None
    _appium_driver = None
    _appium_port = None
    app = None

    @property
    def appium_ip(self):
        """
        一般情况下，*********/8 均可用于环回地址，但在FreeBSD 和 Mac 上 127.0.0.1 以外的地址不可用
        :return: str
        """
        return _appium_ip(self.device_id) if self._appium_port is None else self._appium_port

    @property
    def appium_port(self):
        return _appium_port(self.device_id)

    @appium_port.setter
    def appium_port(self, port):
        self._appium_port = int(port)

    def appium_server_start(self):
        if not self._appium_server:
            appium_server_start(self.device_id)
            self._appium_server = True

    @property
    def appium(self) -> DeviceDriver:
        """
        :return DeviceDriver
        """
        if self._appium_server is None:
            self.appium_server_start()
            sleep(3)
        if self._appium_driver is None:
            self._appium_driver = DeviceDriver(self)
        return self._appium_driver

    def is_appium_alive(self):
        return self._appium_driver is not None

    @hide_error
    def clean_appium(self):
        if self._appium_driver:
            self._appium_driver.close()

        # if self._appium_server:
        #     appium_server_finish(self.device_id)
        # self._appium_server = None
        self._appium_driver = None

    def check_pop(self):
        import cv2,numpy
        def _get_gray_score(binary):
            """
            calculate binary image mean value of normalized array
            :param binary: input binary image
            :return: score between (0-1.0)
            """
            if len(binary.shape) > 2:
                binary = cv2.cvtColor(binary, cv2.COLOR_BGR2GRAY)
            binary = numpy.asarray(binary, dtype=numpy.float32)
            binary_copy = numpy.array(binary, dtype=numpy.float32)
            cv2.normalize(binary, binary_copy, 0, 1, cv2.NORM_MINMAX)
            gray_score = numpy.mean(binary_copy)
            return True if gray_score > 0.7 else False


        def _get_binary_image(image):
            """
            convert 3d image to 1d image
            :param image: input image
            :return: 1d image of gray type and binary type
            """
            img = image
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
            ret, binary = cv2.threshold(gray, 160, 255, cv2.THRESH_BINARY_INV)
            img_gray = cv2.cvtColor(gray, cv2.COLOR_GRAY2BGR)
            img_binary = cv2.cvtColor(binary, cv2.COLOR_GRAY2BGR)
            return img_gray, img_binary

        _filename = self.device_id + str(int((time.time() * 10000))) + ".png"
        self.appium.driver.save_screenshot(_filename)

        img = cv2.imread(_filename)
        scale = 500 / img.shape[1]
        img = cv2.resize(img, (0, 0), fx=scale, fy=scale)
        gray, binary = _get_binary_image(img)
        popup_window = _get_gray_score(binary)

        if os.path.exists(_filename):
            os.remove(_filename)
        return popup_window


    def has_element(self, path, trytime=5,msg=None):
        if msg:
            try:
                el = self.appium.get_element(path, trytime)
                return el
            except ElementNotExist as e:
                if msg:
                    e = str(e) + '||' + msg

                # if 'cpshaitong' in API_URL:
                self.log.info('[error]未找到控件 <%s>' % path)
                
                raise TestCaseFail(e)
        else:
            try:
                el = self.appium.get_element(path, trytime)
                return el
            except:
                return None
    
    def wait_element(self, path, trytime=15):
        try:
            el = self.appium.get_wait_element(path, trytime)
            return el
        except Exception as e:
            print(e)
            return None

    def has_element_image(self, path):
        try:
            has_image,location = self.appium.get_element_image(path)
            return has_image,location
        except:
            return False,(0,0)

    def __getitem__(self, item):
        return self.appium.get_element(item)

    def __setitem__(self, key, value):
        self.appium.get_element(key).val(value)

    @property
    def traffic_monitor(self):
        """获取流量监控器"""
        if self._traffic_monitor is None:
            self._traffic_monitor = get_traffic_monitor(self.device_id)
        return self._traffic_monitor

    def start_traffic_monitoring(self, operation_name: str, control_path: str = "") -> str:
        """开始流量监控"""
        return self.traffic_monitor.start_monitoring(operation_name, control_path)

    def stop_traffic_monitoring(self, monitor_id: str):
        """停止流量监控"""
        return self.traffic_monitor.stop_monitoring(monitor_id)

    def __del__(self):
        if self._appium_subprocess and self._appium_subprocess is not True:
            self._appium_subprocess.kill()
