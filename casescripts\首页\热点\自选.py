from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('首页/热点/自选/自选')
def _f1952faa477357217508d8f995e4967b(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119', 股票代码='600123'):
    # 初始化
    登录前页面处理(device)
    点击(device, '/底部导航栏/首页界面跳转按钮')
    目录校验(device, '一', '首页', '/首页/选中状态')
    点击(device, '/首页/热点')
    目录校验(device, '二', '热点', '/首页/热点/选中状态')
    循环滑动(device, '/首页/热点/要闻/标题', '上')
    点击(device, '/首页/热点/自选')
    if 获取控件对象(device, '/首页/热点/自选/第一条标题') == '':
        点击(device, '/首页/热点/自选/添加自选股')
        输入文本(device, '/首页/热点/自选/添加自选股/填写股票代码', 股票代码)
        点击(device, '/首页/热点/自选/添加自选股/搜索')
        点击(device, '/首页/热点/自选/添加自选股/勾选')
        点击(device, '/首页/热点/自选/添加自选股/返回')
    预期结果 = 获取文本信息(device, '/首页/热点/自选/第一条标题')
    print(预期结果)
    点击(device, '/首页/热点/自选/第一条')
    sleep(2)
    实际结果 = 获取文本信息(device, '/首页/热点/自选/第一条详情/标题')
    print(实际结果)
    错误校验点 = 非空校验(device, '市场', 实际结果)
    结果 = 实际结果 == 预期结果
    return 结果, 错误校验点


