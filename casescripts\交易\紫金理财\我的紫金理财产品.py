from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('交易/紫金理财/我的紫金理财产品/我的紫金理财产品')
def _9f8dc720206878ed65d0a9ee9b4170fa(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # print(1)
    sleep(5)
    # 初始化
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/紫金理财')
    目录校验(device, '二', '紫金理财', '/页面标题')
    点击(device, '/交易/普通交易/紫金理财/紫金理财首页/我的紫金理财产品')
    实际结果 = 获取文本信息(device, '/交易/普通交易/紫金理财/紫金理财首页/我的紫金理财产品/无产品')
    错误校验点 = 为空校验(device)
    结果 = 实际结果 != ''
    return 结果, 错误校验点


