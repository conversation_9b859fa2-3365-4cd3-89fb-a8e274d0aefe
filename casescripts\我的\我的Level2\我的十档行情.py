from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('我的/我的Level2/我的十档行情/我的十档行情')
def _b797f85ce15572b3e7069ffecf8b3753(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # 初始化
    登录前页面处理(device)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    目录校验(device, '一','我的','/我的/选中状态')
    循环滑动(device, '/我的/我的Level2', '上')
    点击(device, '/我的/我的Level2')
    目录校验(device, '二', '我的十档行情', '/我的/我的Level2/页面标题')
    实际结果 = 获取文本信息(device, '/我的/我的Level2/我的十档行情')
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


