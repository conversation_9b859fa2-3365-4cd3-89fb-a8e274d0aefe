from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('行情/市场/更多/国内期货/贵金属现货/获取首只贵金属现货品种名称')
def _677b84d2ae2a1da86e63aeb3c4e6980e(device):
    错误校验点 = ''
    登录前页面处理(device)
    点击(device, '/底部导航栏/行情界面跳转按钮')
    目录校验(device, '一', '行情', '/行情/选中状态')
    点击(device, '/行情/市场/市场标题栏')
    控件左滑(device, '/行情/市场/沪深按钮标签')
    点击(device, '/行情/市场/更多/更多标签')
    目录校验(device, '二', '更多', '/行情/更多/选中状态')
    滑动控件至屏幕内(device, '/行情/市场/更多/国内期货/贵金属现货/贵金属现货标签')
    点击(device, '/行情/市场/更多/国内期货/贵金属现货/贵金属现货标签')
    if 获取控件对象(device, '/交易/普通交易/买入页面/系统提示') == '':
        实际结果 = 获取文本信息(device, '/行情/市场/更多/国内期货/贵金属现货/获取首只贵金属现货品种名称')
        结果 = 实际结果 != ''
        错误校验点 = 为空校验(device)
    else:
        实际结果 = 获取文本信息(device, '/交易/普通交易/买入页面/系统提示')
        预期结果 = '无相应的排名信息'
        结果 = 预期结果 in 实际结果
        错误校验点 = 非空校验(device, 预期结果, 实际结果)
        点击(device, '/系统提示/通知/确定')
    return 结果, 错误校验点


