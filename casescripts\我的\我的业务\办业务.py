from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('我的/我的业务/办业务/密码服务_修改交易密码')
def _a7767109d77cef9fd3775621bed3eaa6(device, 账号='666630297341', 旧交易密码='968892', 通信密码='715394', 新交易密码='142569', 第一次预期结果='成功', 第二次预期结果='成功'):
    # hxf 生产账户
    # 账号 = '666630296771'
    # 旧交易密码 = '124369'
    # 通信密码 = '159753'
    # 新交易密码 = '159753'
    # fsc
    登录前页面处理(device)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    上滑(device)
    点击(device, '/底部导航栏/理财界面跳转按钮')
    点击(device, '/底部导航栏/我的界面跳转按钮')
    目录校验(device, '一','我的','/我的/选中状态')
    点击(device, '/我的/我的业务')
    目录校验(device, '二', '办业务', '/我的/我的业务/页面标题')
    点击(device, '/我的/我的业务/修改交易密码')
    登录(device, 账号, 旧交易密码, 通信密码)
    # 点击( '/首页/热点/我的资产/密码修改/修改交易密码/输入当前密码')
    输入文本(device, '/首页/热点/我的资产/密码修改/修改交易密码/输入当前密码', 旧交易密码)
    输入文本(device, '/首页/热点/我的资产/密码修改/修改交易密码/输入新密码', 新交易密码)
    输入文本(device, '/首页/热点/我的资产/密码修改/修改交易密码/再次输入新密码', 新交易密码)
    点击(device, '/首页/热点/我的资产/密码修改/修改交易密码/显示密码字样')
    点击(device, '/首页/热点/我的资产/密码修改/修改交易密码/确认修改')
    点击(device, '/首页/热点/我的资产/密码修改/修改交易密码/修改密码/确定')
    第一次修改结果 = 获取文本信息(device, '/首页/热点/我的资产/密码修改/修改交易密码/系统提示/结果')
    print('第一次修改结果', 第一次修改结果)
    第一次结果 = 第一次预期结果 in 第一次修改结果
    # 全局变量.错误校验点 = 非空校验(第一次预期结果, 第一次修改结果)
    # print('结果', 结果)
    # 结果校验(第一次结果)
    点击(device, '/首页/热点/我的资产/密码修改/修改交易密码/系统提示/确定')
    # 将密码改回来
    输入文本(device, '/首页/热点/我的资产/密码修改/修改交易密码/输入当前密码', 新交易密码)
    输入文本(device, '/首页/热点/我的资产/密码修改/修改交易密码/输入新密码', 旧交易密码)
    输入文本(device, '/首页/热点/我的资产/密码修改/修改交易密码/再次输入新密码', 旧交易密码)
    点击(device, '/首页/热点/我的资产/密码修改/修改交易密码/显示密码字样')
    点击(device, '/首页/热点/我的资产/密码修改/修改交易密码/确认修改')
    点击(device, '/首页/热点/我的资产/密码修改/修改交易密码/修改密码/确定')
    第二次修改结果 = 获取文本信息(device, '/首页/热点/我的资产/密码修改/修改交易密码/系统提示/结果')
    print('第二次修改结果', 第二次修改结果)
    第二次结果 = 第一次预期结果 in 第一次修改结果 and 第二次预期结果 in 第二次修改结果
    # print('结果', 结果)
    结果 = 第一次结果 and 第二次结果
    错误校验点 = 两次校验(device, 第一次预期结果, 第一次修改结果, 第二次预期结果, 第二次修改结果)
    return 结果, 错误校验点
    点击(device, '/首页/热点/我的资产/密码修改/修改交易密码/系统提示/确定')


@casescript('我的/我的业务/办业务/密码服务_修改资金密码')
def _d729e17bf9b9bf049257d67aa14fa9b7(device, 账号='666630297341', 交易密码='968892', 通信密码='715394', 旧资金密码='684268', 新资金密码='137950', 第一次预期结果='成功', 第二次预期结果='成功'):
    # hxf 生产账户
    # 账号 = '666630296771'
    # 交易密码 = '124369'
    # 通信密码 = '159753'
    # 旧资金密码 = '124369'
    # 新资金密码 = '159753'
    # fsc
    登录前页面处理(device)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    上滑(device)
    点击(device, '/底部导航栏/理财界面跳转按钮')
    点击(device, '/底部导航栏/我的界面跳转按钮')
    目录校验(device, '一','我的','/我的/选中状态')
    点击(device, '/我的/我的业务')
    目录校验(device, '二', '办业务', '/我的/我的业务/页面标题')
    # 理财登陆(账号, 交易密码, 通信密码)
    点击(device, '/我的/我的业务/修改资金密码')
    登录(device, 账号, 交易密码, 通信密码)
    # 点击( '/首页/热点/我的资产/密码修改/修改交易密码/输入当前密码')
    输入文本(device, '/首页/热点/我的资产/密码修改/修改交易密码/输入当前密码', 旧资金密码)
    输入文本(device, '/首页/热点/我的资产/密码修改/修改交易密码/输入新密码', 新资金密码)
    输入文本(device, '/首页/热点/我的资产/密码修改/修改交易密码/再次输入新密码', 新资金密码)
    点击(device, '/首页/热点/我的资产/密码修改/修改交易密码/显示密码字样')
    点击(device, '/首页/热点/我的资产/密码修改/修改交易密码/确认修改')
    点击(device, '/首页/热点/我的资产/密码修改/修改交易密码/修改密码/确定')
    第一次实际结果 = 获取文本信息(device, '/首页/热点/我的资产/密码修改/修改交易密码/系统提示/结果')
    # print('实际结果', 第一次实际结果)
    第一次结果 = 第一次预期结果 in 第一次实际结果
    # print('结果', 结果)
    # 全局变量.错误校验点 = 非空校验(第一次预期结果,第一次实际结果)
    # 结果校验(第一次结果)
    点击(device, '/首页/热点/我的资产/密码修改/修改交易密码/系统提示/确定')
    输入文本(device, '/首页/热点/我的资产/密码修改/修改交易密码/输入当前密码', 新资金密码)
    输入文本(device, '/首页/热点/我的资产/密码修改/修改交易密码/输入新密码', 旧资金密码)
    输入文本(device, '/首页/热点/我的资产/密码修改/修改交易密码/再次输入新密码', 旧资金密码)
    点击(device, '/首页/热点/我的资产/密码修改/修改交易密码/显示密码字样')
    点击(device, '/首页/热点/我的资产/密码修改/修改交易密码/确认修改')
    点击(device, '/首页/热点/我的资产/密码修改/修改交易密码/修改密码/确定')
    第二次实际结果 = 获取文本信息(device, '/首页/热点/我的资产/密码修改/修改交易密码/系统提示/结果')
    第二次结果 = 第二次预期结果 in 第二次实际结果
    结果 = 第一次结果 and 第二次结果
    错误校验点 = 两次校验(device, 第一次预期结果, 第一次实际结果, 第二次预期结果, 第二次实际结果)
    return 结果, 错误校验点
    点击(device, '/首页/热点/我的资产/密码修改/修改交易密码/系统提示/确定')


@casescript('我的/我的业务/办业务/密码服务_修改通讯密码')
def _ad98df738afd55bb5f06a8167aa2e46b(device, 账号='666630297341', 交易密码='968892', 旧通信密码='715394', 新通信密码='298869', 第一次预期结果='成功', 第二次预期结果='成功'):
    # hxf 生产账户
    # 账号 = '666630296771'
    # 交易密码 = '124369'
    # 旧通讯密码 = '159753'
    # 新通讯密码 = '124369'
    # fsc
    登录前页面处理(device)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    上滑(device)
    点击(device, '/底部导航栏/理财界面跳转按钮')
    点击(device, '/底部导航栏/我的界面跳转按钮')
    目录校验(device, '一', '我的', '/我的/选中状态')
    点击(device, '/我的/我的业务')
    目录校验(device, '二', '办业务', '/我的/我的业务/页面标题')
    # 理财登陆(账号, 交易密码, 通信密码)
    点击(device, '/我的/我的业务/修改通讯密码')
    登录(device, 账号, 交易密码, 旧通信密码)
    输入文本(device, '/首页/热点/我的资产/密码修改/修改交易密码/输入当前密码', 旧通信密码)
    输入文本(device, '/首页/热点/我的资产/密码修改/修改交易密码/输入新密码', 新通信密码)
    输入文本(device, '/首页/热点/我的资产/密码修改/修改交易密码/再次输入新密码', 新通信密码)
    点击(device, '/首页/热点/我的资产/密码修改/修改交易密码/显示密码字样')
    点击(device, '/首页/热点/我的资产/密码修改/修改交易密码/确认修改')
    点击(device, '/首页/热点/我的资产/密码修改/修改交易密码/修改密码/确定')
    第一次实际结果 = 获取文本信息(device, '/首页/热点/我的资产/密码修改/修改交易密码/系统提示/结果')
    print('实际结果', 第一次实际结果)
    第一次结果 = 第一次预期结果 in 第一次实际结果
    # print('结果', 第一次结果)
    # 全局变量.错误校验点 = 非空校验(第一次预期结果, 第一次实际结果)
    # 结果校验(第一次结果)
    点击(device, '/首页/热点/我的资产/密码修改/修改交易密码/系统提示/确定')
    # 把密码改回来
    输入文本(device, '/首页/热点/我的资产/密码修改/修改交易密码/输入当前密码', 新通信密码)
    输入文本(device, '/首页/热点/我的资产/密码修改/修改交易密码/输入新密码', 旧通信密码)
    输入文本(device, '/首页/热点/我的资产/密码修改/修改交易密码/再次输入新密码', 旧通信密码)
    点击(device, '/首页/热点/我的资产/密码修改/修改交易密码/显示密码字样')
    点击(device, '/首页/热点/我的资产/密码修改/修改交易密码/确认修改')
    点击(device, '/首页/热点/我的资产/密码修改/修改交易密码/修改密码/确定')
    第二次实际结果 = 获取文本信息(device, '/首页/热点/我的资产/密码修改/修改交易密码/系统提示/结果')
    # print('实际结果', 第二次实际结果)
    第二次结果 = 第二次预期结果 in 第二次实际结果
    # print('结果', 第一次结果)
    结果 = 第一次结果 and 第二次结果
    错误校验点 = 两次校验(device, 第一次预期结果, 第一次实际结果, 第二次预期结果, 第二次实际结果)
    return 结果, 错误校验点
    点击(device, '/首页/热点/我的资产/密码修改/修改交易密码/系统提示/确定')


@casescript('我的/我的业务/办业务/密码服务_忘记交易密码')
def _25fc2c03507f958a33f5617f17f23cfe(device, 账号='666630296771', 交易密码='124369', 通信密码='159753', 姓名='贺晓飞', 身份证号码='320802199506012511'):
    # hxf 生产账户
    登录前页面处理(device)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    上滑(device)
    点击(device, '/底部导航栏/理财界面跳转按钮')
    点击(device, '/底部导航栏/我的界面跳转按钮')
    目录校验(device, '一', '我的', '/我的/选中状态')
    点击(device, '/我的/我的业务')
    目录校验(device, '二', '办业务', '/我的/我的业务/页面标题')
    点击(device, '/我的/我的业务/忘记交易密码')
    点击(device, '/首页/热点/我的资产/密码修改/重置资金密码/下一步')
    输入文本(device, '/我的/我的业务/重置密码/姓名', 姓名)
    输入文本(device, '/我的/我的业务/重置密码/身份证号码', 身份证号码)
    输入文本(device, '/我的/我的业务/重置密码/客户号', 账号)
    点击(device, '/首页/热点/我的资产/密码修改/重置资金密码/下一步')
    点击(device, '/业务办理须知/勾选框')
    点击(device, '/交易/普通交易/公用页面/不适当性确认书页面/确认按钮')
    sleep(10)
    # 点击('/首页/热点/我的资产/密码修改/重置资金密码/下一步')
    点击(device, '/首页/热点/我的资产/密码修改/重置资金密码/验证银证转账金额')
    点击(device, '/首页/热点/我的资产/密码修改/重置资金密码/输入金额')
    实际结果 = 获取文本信息(device, '/页面标题')
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点
    # 点击('/首页/热点/我的资产/密码修改/修改交易密码/系统提示/确定')


@casescript('我的/我的业务/办业务/密码服务_忘记资金密码')
def _f439a29f012b05f1c2943b77cf09ea69(device, 账号='666630296771', 交易密码='124369', 通信密码='159753'):
    # hxf 生产账户
    登录前页面处理(device)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    上滑(device)
    # time.sleep(3)
    点击(device, '/底部导航栏/理财界面跳转按钮')
    点击(device, '/底部导航栏/我的界面跳转按钮')
    目录校验(device, '一', '我的', '/我的/选中状态')
    点击(device, '/我的/我的业务')
    目录校验(device, '二', '办业务', '/我的/我的业务/页面标题')
    # 理财登陆(账号, 交易密码, 通信密码)
    点击(device, '/我的/我的业务/忘记资金密码')
    登录(device, 账号, 交易密码, 通信密码)
    点击(device, '/业务办理须知/勾选框')
    点击(device, '/交易/普通交易/公用页面/不适当性确认书页面/确认按钮')
    sleep(10)
    点击(device, '/首页/热点/我的资产/密码修改/重置资金密码/下一步')
    点击(device, '/首页/热点/我的资产/密码修改/重置资金密码/验证银证转账金额')
    点击(device, '/首页/热点/我的资产/密码修改/重置资金密码/输入金额')
    # 点击('/首页/热点/我的资产/密码修改/重置资金密码/确定')
    # 预期结果 = '无法在线重置'
    实际结果 = 获取文本信息(device, '/页面标题')
    结果 = 实际结果!=''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


@casescript('我的/我的业务/办业务/密码服务_忘记通讯密码')
def _1b536cf476f51a6bc84daa07d45e599a(device, 账号='666630296771', 交易密码='124369', 通信密码='159753', 身份证号码='320802199506012511'):
    # hxf 生产账户
    登录前页面处理(device)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    上滑(device)
    # time.sleep(3)
    点击(device, '/底部导航栏/理财界面跳转按钮')
    点击(device, '/底部导航栏/我的界面跳转按钮')
    目录校验(device, '一', '我的', '/我的/选中状态')
    点击(device, '/我的/我的业务')
    目录校验(device, '二', '办业务', '/我的/我的业务/页面标题')
    点击(device, '/我的/我的业务/忘记通讯密码')
    输入文本(device, '/首页/热点/我的资产/密码修改/重置通讯密码/客户号', 账号)
    输入文本(device, '/首页/热点/我的资产/密码修改/重置通讯密码/身份证号', 身份证号码)
    点击(device, '/首页/热点/我的资产/密码修改/重置通讯密码/点击确定')
    点击(device, '/首页/热点/我的资产/密码修改/重置通讯密码/下一步')
    点击(device, '/首页/热点/我的资产/密码修改/重置通讯密码/下一步')
    实际结果 = 获取文本信息(device, '/页面标题')
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点
    # 点击('/首页/热点/我的资产/密码修改/修改交易密码/系统提示/确定')


@casescript('我的/我的业务/办业务/投资服务_余额增值天天发_进入天天发_历史明细')
def _428198bb44a2b81f20bf4eea3b53d93d(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    登录前页面处理(device)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    上滑(device)
    # time.sleep(3)
    点击(device, '/底部导航栏/理财界面跳转按钮')
    点击(device, '/底部导航栏/我的界面跳转按钮')
    目录校验(device, '一', '我的', '/我的/选中状态')
    点击(device, '/我的/我的业务')
    目录校验(device, '二', '办业务', '/我的/我的业务/页面标题')
    sleep(3)
    点击(device, '我的/我的业务/余额增值天天发')
    # 登录前页面处理()
    if 获取控件对象(device, '/交易/交易登录页面/登陆按钮') != '':
        登录(device, 账号, 交易密码, 通信密码)
    else:
        print('已登录！')
    sleep(5)
    点击(device, '我的/我的业务/余额增值天天发/进入天天发')
    sleep(10)
    点击(device, '我的/我的业务/余额增值天天发/进入天天发/查询')
    sleep(10)
    点击(device, '我的/我的业务/余额增值天天发/进入天天发/查询/历史明细')
    实际结果 = 获取文本信息(device, '我的/我的业务/余额增值天天发/进入天天发/查询/历史明细/第一条')
    错误校验点 = 为空校验(device)
    结果 = 实际结果 != ''
    return 结果, 错误校验点


@casescript('我的/我的业务/办业务/投资服务_基金场外转场内')
def _ac768b2f427cb6442e48f46e069a06ba(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119', 交易数量='1'):
    # 登录前页面处理()
    点击(device, '/底部导航栏/我的界面跳转按钮')
    上滑(device)
    点击(device, '/底部导航栏/理财界面跳转按钮')
    点击(device, '/底部导航栏/我的界面跳转按钮')
    目录校验(device, '一', '我的', '/我的/选中状态')
    点击(device, '/我的/我的业务')
    目录校验(device, '二', '办业务', '/我的/我的业务/页面标题')
    sleep(2)
    点击(device, '/我的/我的业务/基金场外转场内')
    登录(device, 账号, 交易密码, 通信密码)
    点击(device, '/首页/热点/我的资产/密码修改/重置资金密码/下一步')
    点击(device, '/我的/我的业务/基金场外转场内/请选择持仓基金')
    点击(device, '/我的/我的业务/基金场外转场内/请选择持仓基金/第一个')
    点击(device, '/我的/我的业务/基金场外转场内/请选择持仓基金/输入数量')
    # 点击('/我的/我的业务/基金场外转场内/请选择持仓基金/交易数量')
    点击(device, '/我的/我的业务/基金场外转场内/请选择持仓基金/1')
    点击(device, '/我的/我的业务/基金场外转场内/请选择持仓基金/确定')
    点击(device, '/底部确定/确定按钮')
    点击(device, '/我的/我的业务/基金场外转场内/请选择持仓基金/确认转换')
    sleep(10)
    # 登录前页面处理()
    # 登录(账号, 交易密码, 通信密码)
    # time.sleep(5)
    实际结果 = 获取文本信息(device, '/我的/我的业务/基金场外转场内/请选择持仓基金/系统提示')
    # print(实际结果)
    结果 = 实际结果!=''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


@casescript('我的/我的业务/办业务/投资服务_天天财_天天财累计收益')
def _5faee3bfde93160a846fc1469dce5a29(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    登录前页面处理(device)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    # time.sleep(2)
    上滑(device)
    # time.sleep(2)
    点击(device, '/底部导航栏/理财界面跳转按钮')
    点击(device, '/底部导航栏/我的界面跳转按钮')
    目录校验(device, '一', '我的', '/我的/选中状态')
    点击(device, '/我的/我的业务')
    目录校验(device, '二', '办业务', '/我的/我的业务/页面标题')
    sleep(2)
    点击(device, '我的/我的业务/天天财')
    if 获取控件对象(device, '/交易/交易登录页面/登陆按钮') != '':
        登录(device, 账号, 交易密码, 通信密码)
    else:
        print('已登录！')
    点击(device, '/交易/普通交易/天天财/历史累计收益详情')
    实际结果 = 获取文本信息(device, '/交易/普通交易/华泰理财产品/天天财/历史累计/已实现收益')
    print(实际结果)
    错误校验点 = 为空校验(device)
    结果 = 实际结果 != ''
    return 结果, 错误校验点


@casescript('我的/我的业务/办业务/投资服务_天天财_我的委托_历史委托')
def _847a8b9f5bfafdc90ffab3822bf17aea(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    登录前页面处理(device)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    上滑(device)
    点击(device, '/底部导航栏/理财界面跳转按钮')
    点击(device, '/底部导航栏/我的界面跳转按钮')
    目录校验(device, '一', '我的', '/我的/选中状态')
    点击(device, '/我的/我的业务')
    目录校验(device, '二', '办业务', '/我的/我的业务/页面标题')
    sleep(2)
    点击(device, '我的/我的业务/天天财')
    if 获取控件对象(device, '/交易/交易登录页面/登陆按钮') != '':
        登录(device, 账号, 交易密码, 通信密码)
    else:
        print('已登录！')
    # 点击('我的/我的业务/天天财')
    点击(device, '我的/我的业务/天天财/查看委托')
    点击(device, '我的/我的业务/天天财/查看委托/历史委托')
    实际结果 = 获取文本信息(device, '我的/我的业务/天天财/无记录')
    print(实际结果)
    错误校验点 = 为空校验(device)
    结果 = 实际结果 != ''
    return 结果, 错误校验点


@casescript('我的/我的业务/办业务/投资服务_天天财_我的委托_当日委托')
def _9a92dbedbbca3d756c2c611fd28d7f9b(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    登录前页面处理(device)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    上滑(device)
    点击(device, '/底部导航栏/理财界面跳转按钮')
    点击(device, '/底部导航栏/我的界面跳转按钮')
    目录校验(device, '一', '我的', '/我的/选中状态')
    点击(device, '/我的/我的业务')
    目录校验(device, '二', '办业务', '/我的/我的业务/页面标题')
    sleep(2)
    点击(device, '我的/我的业务/天天财')
    if 获取控件对象(device, '/交易/交易登录页面/登陆按钮') != '':
        登录(device, 账号, 交易密码, 通信密码)
    else:
        print('已登录！')
    # 点击('我的/我的业务/天天财')
    点击(device, '我的/我的业务/天天财/查看委托')
    实际结果 = 获取文本信息(device, '我的/我的业务/天天财/无记录')
    print(实际结果)
    错误校验点 = 为空校验(device)
    结果 = 实际结果 != ''
    return 结果, 错误校验点


@casescript('我的/我的业务/办业务/投资服务_天天财_我要抢购今日份额_短期理财服务')
def _60b455aed5fd5c5b98568892ebcdbf12(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    登录前页面处理(device)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    上滑(device)
    点击(device, '/底部导航栏/理财界面跳转按钮')
    点击(device, '/底部导航栏/我的界面跳转按钮')
    目录校验(device, '一', '我的', '/我的/选中状态')
    点击(device, '/我的/我的业务')
    目录校验(device, '二', '办业务', '/我的/我的业务/页面标题')
    sleep(2)
    点击(device, '我的/我的业务/天天财')
    if 获取控件对象(device, '/交易/交易登录页面/登陆按钮') != '':
        登录(device, 账号, 交易密码, 通信密码)
    else:
        print('已登录！')
    点击(device, '/交易/普通交易/天天财/我要抢购今日份额')
    点击(device, '/交易/普通交易/天天财/我要抢购今日份额/短期理财服务')
    实际结果 = 获取文本信息(device, '/交易/普通交易/天天财/我要抢购今日份额/短期理财服务/什么是天天财')
    print(实际结果)
    错误校验点 = 为空校验(device)
    结果 = 实际结果 != ''
    return 结果, 错误校验点


@casescript('我的/我的业务/办业务/投资服务_融资融券(页面跳转至交易-融资融券）')
def _abce3aeeed9d21af96e7d2d1d40d615a(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119', 预期结果='融资融券'):
    # 登录前页面处理()
    点击(device, '/底部导航栏/我的界面跳转按钮')
    上滑(device)
    点击(device, '/底部导航栏/理财界面跳转按钮')
    点击(device, '/底部导航栏/我的界面跳转按钮')
    目录校验(device, '一', '我的', '/我的/选中状态')
    点击(device, '/我的/我的业务')
    目录校验(device, '二', '办业务', '/我的/我的业务/页面标题')
    sleep(2)
    点击(device, '我的/我的业务/融资融券')
    # 登录前页面处理()
    # 登录(账号, 交易密码, 通信密码)
    # time.sleep(5)
    实际结果 = 获取文本信息(device, '我的/我的业务/融资融券/判断')
    print(实际结果)
    错误校验点 = 非空校验(device, 预期结果, 实际结果)
    结果 = 预期结果 in 实际结果
    return 结果, 错误校验点


@casescript('我的/我的业务/办业务/权限开通_开通交易权限_上交所退市整理权限')
def _d827f6c5a133668484a7a684b9ecbac8(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119', 预期结果='已签署'):
    错误校验点 = ''
    登录前页面处理(device)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    上滑(device)
    点击(device, '/底部导航栏/理财界面跳转按钮')
    点击(device, '/底部导航栏/我的界面跳转按钮')
    目录校验(device, '一', '我的', '/我的/选中状态')
    点击(device, '/我的/我的业务')
    目录校验(device, '二', '办业务', '/我的/我的业务/页面标题')
    # time.sleep(2)
    滑动控件至屏幕内(device, '/我的/我的业务/开通相关交易权限')
    点击(device, '/我的/我的业务/问一问')
    返回(device)
    点击(device, '/我的/我的业务/开通相关交易权限')
    登录(device, 账号, 交易密码, 通信密码)
    上交所拟终止 = 获取文本信息(device, '/我的/我的业务/开通相关交易权限/上交所拟终止')
    结果 = 预期结果 in 上交所拟终止
    if 结果:
        print('已签署')
    else:
        预期结果 = '未签署'
        结果 = 预期结果 in 上交所拟终止
        if 结果:
            print('未签署')
            点击(device, '/我的/我的业务/开通相关交易权限/上交所拟终止')
            结果 = 获取控件对象(device, '/首页/热点/我的资产/业务办理/开通相关交易权限/上交所风险警示股票交易/我要开通') != ''
            错误校验点 = 为空校验(device)
        else:
            错误校验点 = 非空校验(device, 预期结果, 上交所拟终止)
    return 结果, 错误校验点


@casescript('我的/我的业务/办业务/权限开通_开通交易权限_开通风险警示板')
def _f859658f7503edfd4ecd61c5fbb0c313(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119', 预期结果='已签署'):
    错误校验点 = ''
    登录前页面处理(device)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    上滑(device)
    点击(device, '/底部导航栏/理财界面跳转按钮')
    点击(device, '/底部导航栏/我的界面跳转按钮')
    目录校验(device, '一', '我的', '/我的/选中状态')
    点击(device, '/我的/我的业务')
    目录校验(device, '二', '办业务', '/我的/我的业务/页面标题')
    # 上滑()
    # 上滑()
    # time.sleep(2)
    滑动控件至屏幕内(device, '/我的/我的业务/开通相关交易权限')
    点击(device, '/我的/我的业务/问一问')
    返回(device)
    # time.sleep(3)
    点击(device, '/我的/我的业务/开通相关交易权限')
    登录(device, 账号, 交易密码, 通信密码)
    上交所风险警示股票交易 = 获取文本信息(device, '/首页/热点/我的资产/业务办理/开通相关交易权限/上交所风险警示股票交易')
    结果 = 预期结果 in 上交所风险警示股票交易
    if 结果:
        print('已签署')
    else:
        预期结果 = '未签署'
        结果 = 预期结果 in 上交所风险警示股票交易
        if 结果:
            print('未签署')
            点击(device, '/首页/热点/我的资产/业务办理/开通相关交易权限/上交所风险警示股票交易')
            结果 = 获取控件对象(device, '/首页/热点/我的资产/业务办理/开通相关交易权限/上交所风险警示股票交易/我要开通') != ''
            错误校验点 = 为空校验(device)
        else:
            错误校验点 = 非空校验(device, 预期结果, 上交所风险警示股票交易)
    return 结果, 错误校验点


@casescript('我的/我的业务/办业务/权限开通_开通交易权限_深交所退市整理权限')
def _3635d366a26375ab19dbac5f0cb03ffe(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119', 预期结果='已签署'):
    错误校验点 = ''
    登录前页面处理(device)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    上滑(device)
    点击(device, '/底部导航栏/理财界面跳转按钮')
    点击(device, '/底部导航栏/我的界面跳转按钮')
    目录校验(device, '一', '我的', '/我的/选中状态')
    点击(device, '/我的/我的业务')
    目录校验(device, '二', '办业务', '/我的/我的业务/页面标题')
    滑动控件至屏幕内(device, '/我的/我的业务/开通相关交易权限')
    点击(device, '/我的/我的业务/问一问')
    返回(device)
    点击(device, '/我的/我的业务/开通相关交易权限')
    登录(device, 账号, 交易密码, 通信密码)
    深交所拟终止 = 获取文本信息(device, '/我的/我的业务/开通相关交易权限/深交所拟终止')
    结果 = 预期结果 in 深交所拟终止
    if 结果:
        print('已签署')
    else:
        预期结果 = '未签署'
        结果 = 预期结果 in 深交所拟终止
        if 结果:
            print('未签署')
            点击(device, '/我的/我的业务/开通相关交易权限/深交所拟终止')
            结果 = 获取控件对象(device, '/首页/热点/我的资产/业务办理/开通相关交易权限/上交所风险警示股票交易/我要开通') != ''
            错误校验点 = 为空校验(device)
        else:
            错误校验点 = 非空校验(device, 预期结果, 深交所拟终止)
    return 结果, 错误校验点


@casescript('我的/我的业务/办业务/权限开通_开通沪深股东账户')
def _ec4a6d1422ddf315fa49c8c24f358a18(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    登录前页面处理(device)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    上滑(device)
    点击(device, '/底部导航栏/理财界面跳转按钮')
    点击(device, '/底部导航栏/我的界面跳转按钮')
    目录校验(device, '一', '我的', '/我的/选中状态')
    点击(device, '/我的/我的业务')
    目录校验(device, '二', '办业务', '/我的/我的业务/页面标题')
    # time.sleep(2)
    滑动控件至屏幕内(device, '/我的/我的业务/权限开通/沪深股东账户')
    点击(device, '/我的/我的业务/问一问')
    返回(device)
    点击(device, '/我的/我的业务/权限开通/沪深股东账户')
    登录(device, 账号, 交易密码, 通信密码)
    结果 = 获取控件对象(device, '/我的/我的业务/权限开通/沪深股东账户/加载检查') != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


@casescript('我的/我的业务/办业务/权限开通_开通港股通')
def _4ea5c4b213dd9619d36454b0a5bad69c(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    登录前页面处理(device)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    上滑(device)
    点击(device, '/底部导航栏/理财界面跳转按钮')
    点击(device, '/底部导航栏/我的界面跳转按钮')
    目录校验(device, '一', '我的', '/我的/选中状态')
    点击(device, '/我的/我的业务')
    目录校验(device, '二', '办业务', '/我的/我的业务/页面标题')
    # time.sleep(2)
    滑动控件至屏幕内(device, '/我的/我的业务/权限开通/开通港股通')
    点击(device, '/我的/我的业务/问一问')
    返回(device)
    点击(device, '/我的/我的业务/权限开通/开通港股通')
    登录(device, 账号, 交易密码, 通信密码)
    结果 = 获取控件对象(device, '/我的/我的业务/权限开通/开通港股通/立即开通') != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


@casescript('我的/我的业务/办业务/权限开通_开通质押借钱功能')
def _cf5cef4e2553827c97122fb37067a340(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119', 预期结果='关于暂停'):
    登录前页面处理(device)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    上滑(device)
    点击(device, '/底部导航栏/理财界面跳转按钮')
    点击(device, '/底部导航栏/我的界面跳转按钮')
    目录校验(device, '一', '我的', '/我的/选中状态')
    点击(device, '/我的/我的业务')
    目录校验(device, '二', '办业务', '/我的/我的业务/页面标题')
    滑动控件至屏幕内(device, '/我的/我的业务/开通质押借钱功能')
    点击(device, '/我的/我的业务/问一问')
    返回(device)
    点击(device, '/我的/我的业务/开通质押借钱功能')
    登录(device, 账号, 交易密码, 通信密码)
    实际结果 = 获取文本信息(device, '/我的/我的业务/开通质押借钱功能/暂停通知')
    错误校验点 = 非空校验(device, 预期结果, 实际结果)
    结果 = 预期结果 in 实际结果
    点击(device, '/交易/普通交易/撤单/撤单页面/确定按钮')
    return 结果, 错误校验点


@casescript('我的/我的业务/办业务/权限开通_柜台市场产品')
def _a6fb2284c5149ff7954d5ea0fce19fe1(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    登录前页面处理(device)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    上滑(device)
    点击(device, '/底部导航栏/理财界面跳转按钮')
    点击(device, '/底部导航栏/我的界面跳转按钮')
    目录校验(device, '一', '我的', '/我的/选中状态')
    点击(device, '/我的/我的业务')
    目录校验(device, '二', '办业务', '/我的/我的业务/页面标题')
    # time.sleep(2)
    滑动控件至屏幕内(device, '/我的/我的业务/权限开通/柜台市场产品')
    点击(device, '/我的/我的业务/问一问')
    返回(device)
    点击(device, '/我的/我的业务/权限开通/柜台市场产品')
    登录(device, 账号, 交易密码, 通信密码)
    结果 = 获取控件对象(device, '/我的/我的业务/权限开通/柜台市场产品/加载检查') != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


@casescript('我的/我的业务/办业务/测评认定等_专业投资者认定与取消')
def _025f548b779c2f196387d396eb52d784(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119', 预期结果='我要成为专业投资者'):
    # hxf 生产账户
    # 初始化
    登录前页面处理(device)
    # 理财登陆(账号, 交易密码, 通信密码)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    上滑(device)
    点击(device, '/底部导航栏/理财界面跳转按钮')
    点击(device, '/底部导航栏/我的界面跳转按钮')
    目录校验(device, '一', '我的', '/我的/选中状态')
    点击(device, '/我的/我的业务')
    目录校验(device, '二', '办业务', '/我的/我的业务/页面标题')
    上滑(device)
    上滑(device)
    点击(device, '/我的/我的业务/问一问')
    返回(device)
    点击(device, '/我的/我的业务/专业投资者认定与取消')
    登录(device, 账号, 交易密码, 通信密码)
    实际结果 = 获取文本信息(device, "/我的/我的业务/专业投资者认定与取消/我要成为专业投资者")
    错误校验点 = 非空校验(device, 预期结果, 实际结果)
    结果 = (预期结果 == 实际结果)
    return 结果, 错误校验点


@casescript('我的/我的业务/办业务/测评认定等_两融征信')
def _e043e5c63537c1f9af7fd8399166b391(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119', 预期结果='知道了'):
    # hxf 生产账户
    # 初始化
    登录前页面处理(device)
    # 理财登陆(账号, 交易密码, 通信密码)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    上滑(device)
    点击(device, '/底部导航栏/理财界面跳转按钮')
    点击(device, '/底部导航栏/我的界面跳转按钮')
    目录校验(device, '一', '我的', '/我的/选中状态')
    点击(device, '/我的/我的业务')
    目录校验(device, '二', '办业务', '/我的/我的业务/页面标题')
    上滑(device)
    上滑(device)
    # 滑动控件至屏幕内('/我的/我的业务/两融征信')
    点击(device, '/我的/我的业务/问一问')
    返回(device)
    点击(device, '/我的/我的业务/两融征信')
    登录(device, 账号, 交易密码, 通信密码)
    返回(device)
    点击(device, '/我的/我的业务/两融征信')
    实际结果 = 获取文本信息(device, '/我的/我的业务/两融征信/融资证券/融资融券/知道了')
    错误校验点 = 非空校验(device, 预期结果, 实际结果)
    结果 = 实际结果 == 预期结果
    return 结果, 错误校验点


@casescript('我的/我的业务/办业务/测评认定等_合同签署')
def _a2e940618c7ada6e60e55deb67c9ad65(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # 初始化
    登录前页面处理(device)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    上滑(device)
    点击(device, '/底部导航栏/理财界面跳转按钮')
    点击(device, '/底部导航栏/我的界面跳转按钮')
    目录校验(device, '一', '我的', '/我的/选中状态')
    点击(device, '/我的/我的业务')
    目录校验(device, '二', '办业务', '/我的/我的业务/页面标题')
    上滑(device)
    上滑(device)
    点击(device, '/我的/我的业务/问一问')
    返回(device)
    sleep(2)
    点击(device, '/我的/我的业务/测评认定等/合同签署')
    if 获取控件对象(device, '/交易/交易登录页面/登陆按钮') != '':
        登录(device, 账号, 交易密码, 通信密码)
    else:
        print('已登录！')
    实际结果 = 获取文本信息(device, '/我的/我的业务/测评认定等/合同签署/第一条合同名称')
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


@casescript('我的/我的业务/办业务/测评认定等_风险评估')
def _1e53bbd6ea12dc439fb2dea19f456650(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # 初始化
    登录前页面处理(device)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    # time.sleep(3)
    上滑(device)
    点击(device, '/底部导航栏/理财界面跳转按钮')
    点击(device, '/底部导航栏/我的界面跳转按钮')
    目录校验(device, '一', '我的', '/我的/选中状态')
    点击(device, '/我的/我的业务')
    目录校验(device, '二', '办业务', '/我的/我的业务/页面标题')
    上滑(device)
    上滑(device)
    # time.sleep(2)
    点击(device, '/我的/我的业务/问一问')
    返回(device)
    # 滑动控件至屏幕内('/我的/我的业务/测评认定等/风险评估')
    点击(device, '/我的/我的业务/测评认定等/风险评估')
    if 获取控件对象(device, '/交易/交易登录页面/登陆按钮') != '':
        登录(device, 账号, 交易密码, 通信密码)
    else:
        print('已登录！')
    实际结果 = 获取文本信息(device, '/我的/我的业务/测评认定等/风险评估/类型')
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


@casescript('我的/我的业务/办业务/热门业务_三方存管变更')
def _4593656a3d6109c5316d467f32795406(device, 账号='666630296771', 交易密码='124369', 通信密码='159753', 金额='1'):
    # hxf 生产账户
    # 初始化
    登录前页面处理(device)
    # 理财登陆(账号, 交易密码, 通信密码)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    上滑(device)
    # time.sleep(3)
    点击(device, '/底部导航栏/理财界面跳转按钮')
    点击(device, '/底部导航栏/我的界面跳转按钮')
    目录校验(device, '一', '我的', '/我的/选中状态')
    点击(device, '/我的/我的业务')
    目录校验(device, '二', '办业务', '/我的/我的业务/页面标题')
    # 点击('/底部导航栏/我的界面跳转按钮')
    # 上滑()
    # 点击('/我的/我的业务')
    点击(device, '/我的/我的业务/三方存管变更')
    登录(device, 账号, 交易密码, 通信密码)
    try:
        点击(device, '/我的/我的业务/三方存管变更/下一步')
        点击(device, '/我的/我的业务/三方存管变更/立即转账')
        输入文本(device, '/我的/我的业务/三方存管变更/输入金额', 金额)
        点击(device, '/我的/我的业务/三方存管变更/确定')
        输入文本(device, '/我的/我的业务/三方存管变更/输入密码', 交易密码)
        点击(device, '/我的/我的业务/三方存管变更/再次确定')
        实际结果 = 获取文本信息(device, '/页面标题')
    except:
        实际结果 = 获取文本信息(device, '/页面标题')
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


@casescript('我的/我的业务/办业务/热门业务_创业板转签')
def _65e009b5c772dd35673f61c71afbffe8(device, 账号='666630296771', 交易密码='124369', 通信密码='159753'):
    # hxf 生产账户
    # 初始化
    登录前页面处理(device)
    # 理财登陆(账号, 交易密码, 通信密码)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    上滑(device)
    # time.sleep(3)
    点击(device, '/底部导航栏/理财界面跳转按钮')
    点击(device, '/底部导航栏/我的界面跳转按钮')
    目录校验(device, '一', '我的', '/我的/选中状态')
    点击(device, '/我的/我的业务')
    目录校验(device, '二', '办业务', '/我的/我的业务/页面标题')
    # 点击('/底部导航栏/我的界面跳转按钮')
    # 上滑()
    # 点击('/我的/我的业务')
    点击(device, '/我的/我的业务/创业板转签')
    登录(device, 账号, 交易密码, 通信密码)
    点击(device, '/我的/我的业务/创业板转签/我要办理')
    实际结果 = 获取文本信息(device, '/页面标题')
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


@casescript('我的/我的业务/办业务/热门业务_外籍开户预约')
def _195d7057a08c719f527eb3eebb27b2df(device, 账号='666630296771', 交易密码='124369', 通信密码='159753'):
    # hxf 生产账户
    # 初始化
    登录前页面处理(device)
    # 理财登陆(账号, 交易密码, 通信密码)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    上滑(device)
    # time.sleep(3)
    点击(device, '/底部导航栏/理财界面跳转按钮')
    点击(device, '/底部导航栏/我的界面跳转按钮')
    目录校验(device, '一', '我的', '/我的/选中状态')
    点击(device, '/我的/我的业务')
    目录校验(device, '二', '办业务', '/我的/我的业务/页面标题')
    # 点击('/我的/我的业务')
    点击(device, '/我的/我的业务/外籍开户预约')
    # 登录(账号, 交易密码, 通信密码)
    实际结果 = 获取控件对象(device, '/我的/我的业务/外籍开户预约/提交按钮')
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


@casescript('我的/我的业务/办业务/热门业务_开通科创板')
def _b510c9979887ca9257c0aeb44ea785f6(device, 账号='666630296771', 交易密码='124369', 通信密码='159753'):
    # hxf 生产账户
    # 初始化
    登录前页面处理(device)
    # 理财登陆(账号, 交易密码, 通信密码)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    上滑(device)
    # time.sleep(3)
    点击(device, '/底部导航栏/理财界面跳转按钮')
    点击(device, '/底部导航栏/我的界面跳转按钮')
    目录校验(device, '一', '我的', '/我的/选中状态')
    点击(device, '/我的/我的业务')
    目录校验(device, '二', '办业务', '/我的/我的业务/页面标题')
    # 点击('/底部导航栏/我的界面跳转按钮')
    # 上滑()
    # 点击('/我的/我的业务')
    点击(device, '/我的/我的业务/开通科创板')
    登录(device, 账号, 交易密码, 通信密码)
    实际结果 = 获取文本信息(device, '/页面标题')
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


@casescript('我的/我的业务/办业务/热门业务_更新个人信息')
def _40879ee43996959c524e2131ceba65a5(device, 账号='666630296771', 交易密码='124369', 通信密码='159753', 居住地址='贾西新苑15楼', 手机号码='17749532309', 预期结果='个人信息'):
    # hxf 生产账户
    # 初始化
    登录前页面处理(device)
    # 理财登陆(账号, 交易密码, 通信密码)
    # 交易登录(账号, 交易密码, 通信密码)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    上滑(device)
    点击(device, '/底部导航栏/理财界面跳转按钮')
    点击(device, '/底部导航栏/我的界面跳转按钮')
    目录校验(device, '一', '我的', '/我的/选中状态')
    点击(device, '/我的/我的业务')
    目录校验(device, '二', '办业务', '/我的/我的业务/页面标题')
    # 点击('/我的/我的业务')
    点击(device, '/我的/我的业务/更新个人信息')
    登录(device, 账号, 交易密码, 通信密码)
    实际结果 = 获取文本信息(device, '/页面标题')
    结果 = 预期结果 in 实际结果
    错误校验点 = 非空校验(device, 预期结果, 实际结果)
    # 输入文本('/首页/热点/我的资产/个人信息/个人信息更新/居住地址', 居住地址)
    # 点击('/首页/热点/我的资产/个人信息/个人信息更新/下一步')
    #
    # try:
    #     获取文本信息('/首页/热点/我的资产/个人信息/个人信息更新/系统提示/加载检查')
    # except:
    #     print('无系统提示')
    #     pass
    # else:
    #     全局变量.错误校验点 = 获取文本信息('/首页/热点/我的资产/个人信息/个人信息更新/系统提示/提示信息')
    #     结果校验(False)
    #     raise NotFindError
    #
    # try:
    #     获取文本信息('/公用/温馨提示/加载检查')
    # except:
    #     print('无温馨提示！')
    #     pass
    # else:
    #     点击('/公用/温馨提示/我已确认')
    #     点击('/首页/热点/我的资产/个人信息/个人信息更新/下一步')
    #
    # 输入文本('/首页/热点/我的资产/个人信息/个人信息更新/验证/手机号码', 手机号码)
    # 点击('/首页/热点/我的资产/个人信息/个人信息更新/验证/获取验证码')
    #
    # time.sleep(5)
    # 验证码 = 获取短信验证码()
    # print('验证码：；', 验证码)
    # 输入文本('/首页/热点/我的资产/个人信息/个人信息更新/验证/输入验证码', 验证码)
    # 点击('/首页/热点/我的资产/个人信息/个人信息更新/验证/确认修改')
    # 预期结果 = '修改成功'
    # 实际结果 = 获取文本信息('/首页/热点/我的资产/个人信息/个人信息更新/结果/修改结果')
    # 点击('/首页/热点/我的资产/个人信息/个人信息更新/结果/确定')
    # 结果 = 预期结果 in 实际结果
    # 全局变量.错误校验点 = 非空校验(预期结果, 实际结果)
    return 结果, 错误校验点


@casescript('我的/我的业务/办业务/热门业务_涉税信息申报')
def _546507dabb677dfb5ec3c7d8c30db648(device, 账号='666630296771', 交易密码='124369', 通信密码='159753'):
    # hxf 生产账户
    # 初始化
    登录前页面处理(device)
    # 理财登陆(账号, 交易密码, 通信密码)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    上滑(device)
    # time.sleep(3)
    点击(device, '/底部导航栏/理财界面跳转按钮')
    点击(device, '/底部导航栏/我的界面跳转按钮')
    目录校验(device, '一', '我的', '/我的/选中状态')
    点击(device, '/我的/我的业务')
    目录校验(device, '二', '办业务', '/我的/我的业务/页面标题')
    # 点击('/底部导航栏/我的界面跳转按钮')
    # 上滑()
    # 点击('/我的/我的业务')
    点击(device, '/我的/我的业务/涉税信息申报')
    登录(device, 账号, 交易密码, 通信密码)
    实际结果 = 获取文本信息(device, '/页面标题')
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


@casescript('我的/我的业务/办业务/热门业务_网上开户')
def _3610a69e998589dfadf76dffdaa3dc5a(device, 账号='666630296771', 交易密码='124369', 通信密码='159753'):
    # hxf 生产账户
    # 初始化
    登录前页面处理(device)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    上滑(device)
    # time.sleep(3)
    点击(device, '/底部导航栏/理财界面跳转按钮')
    点击(device, '/底部导航栏/我的界面跳转按钮')
    目录校验(device, '一', '我的', '/我的/选中状态')
    点击(device, '/我的/我的业务')
    目录校验(device, '二', '办业务', '/我的/我的业务/页面标题')
    # 点击('/底部导航栏/我的界面跳转按钮')
    # 上滑()
    # 点击('/我的/我的业务')
    点击(device, '/我的/我的业务/网上开户')
    实际结果 = 获取文本信息(device, '/页面标题')
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


@casescript('我的/我的业务/办业务/热门业务_网点取号')
def _a359523dfdf6f113bba695418274bbee(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # 登录前页面处理()
    点击(device, '/底部导航栏/我的界面跳转按钮')
    上滑(device)
    点击(device, '/底部导航栏/理财界面跳转按钮')
    点击(device, '/底部导航栏/我的界面跳转按钮')
    目录校验(device, '一', '我的', '/我的/选中状态')
    点击(device, '/我的/我的业务')
    目录校验(device, '二', '办业务', '/我的/我的业务/页面标题')
    # 登录(账号, 交易密码, 通信密码)
    # time.sleep(5)
    点击(device, '/我的/我的业务/网点取号')
    实际结果 = 获取文本信息(device, '/我的/我的业务/网点取号/网点信息')
    错误校验点 = 为空校验(device)
    结果 = 实际结果 != ''
    return 结果, 错误校验点


@casescript('我的/我的业务/办业务/热门业务_身份证信息更新')
def _8bec70554e21fb163f473eb2e7667087(device, 账号='666630296771', 交易密码='124369', 通信密码='159753'):
    # hxf 生产账户
    # 初始化
    登录前页面处理(device)
    # 理财登陆(账号, 交易密码, 通信密码)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    上滑(device)
    # time.sleep(3)
    点击(device, '/底部导航栏/理财界面跳转按钮')
    点击(device, '/底部导航栏/我的界面跳转按钮')
    目录校验(device, '一', '我的', '/我的/选中状态')
    点击(device, '/我的/我的业务')
    目录校验(device, '二', '办业务', '/我的/我的业务/页面标题')
    # 点击('/底部导航栏/我的界面跳转按钮')
    # 上滑()
    # 点击('/我的/我的业务')
    点击(device, '/我的/我的业务/身份证信息更新')
    登录(device, 账号, 交易密码, 通信密码)
    实际结果 = 获取文本信息(device, '/页面标题')
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


@casescript('我的/我的业务/办业务/账户管理_三方存管新建')
def _e988dd3c024628e3578cd05ac31eef43(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # 初始化
    登录前页面处理(device)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    # time.sleep(3)
    上滑(device)
    点击(device, '/底部导航栏/理财界面跳转按钮')
    点击(device, '/底部导航栏/我的界面跳转按钮')
    目录校验(device, '一', '我的', '/我的/选中状态')
    点击(device, '/我的/我的业务')
    目录校验(device, '二', '办业务', '/我的/我的业务/页面标题')
    # 滑动控件至屏幕内('/我的/我的业务/账户管理/三方存管新建')
    上滑(device)
    上滑(device)
    点击(device, '我的/我的业务/我的客服')
    返回(device)
    点击(device, '/我的/我的业务/账户管理/三方存管新建')
    if 获取控件对象(device, '/交易/交易登录页面/登陆按钮') != '':
        登录(device, 账号, 交易密码, 通信密码)
    else:
        print('已登录！')
    实际结果 = 获取文本信息(device, '/我的/我的业务/账户管理/三方存管新建/标题')
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


@casescript('我的/我的业务/办业务/账户管理_休眠账号激活')
def _5a6d872e5991519ebafb1d646699aec8(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # 初始化
    登录前页面处理(device)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    # time.sleep(3)
    上滑(device)
    点击(device, '/底部导航栏/理财界面跳转按钮')
    点击(device, '/底部导航栏/我的界面跳转按钮')
    目录校验(device, '一', '我的', '/我的/选中状态')
    点击(device, '/我的/我的业务')
    目录校验(device, '二', '办业务', '/我的/我的业务/页面标题')
    # 滑动控件至屏幕内('/我的/我的业务/账户管理/休眠账号激活')
    上滑(device)
    上滑(device)
    点击(device, '我的/我的业务/我的客服')
    返回(device)
    点击(device, '/我的/我的业务/账户管理/休眠账号激活')
    if 获取控件对象(device, '/交易/交易登录页面/登陆按钮') != '':
        登录(device, 账号, 交易密码, 通信密码)
    else:
        print('已登录！')
    实际结果 = 获取文本信息(device, '/我的/我的业务/账户管理/休眠账号激活/标题')
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


@casescript('我的/我的业务/办业务/账户管理_修改手机号')
def _e689a9e28f73ffc5375812966eb0f50b(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # 初始化
    登录前页面处理(device)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    # time.sleep(3)
    上滑(device)
    点击(device, '/底部导航栏/理财界面跳转按钮')
    点击(device, '/底部导航栏/我的界面跳转按钮')
    目录校验(device, '一', '我的', '/我的/选中状态')
    点击(device, '/我的/我的业务')
    目录校验(device, '二', '办业务', '/我的/我的业务/页面标题')
    上滑(device)
    上滑(device)
    点击(device, '我的/我的业务/我的客服')
    返回(device)
    # 滑动控件至屏幕内('/我的/我的业务/账户管理/修改手机号')
    点击(device, '/我的/我的业务/账户管理/修改手机号')
    if 获取控件对象(device, '/交易/交易登录页面/登陆按钮') != '':
        登录(device, 账号, 交易密码, 通信密码)
    else:
        print('已登录！')
    实际结果 = 获取文本信息(device, '/我的/我的业务/账户管理/修改手机号/标题')
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


@casescript('我的/我的业务/办业务/账户管理_找回客户号')
def _7ef1b8b1bf6f1d65652abf948e592e4f(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # 初始化
    登录前页面处理(device)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    # time.sleep(3)
    上滑(device)
    点击(device, '/底部导航栏/理财界面跳转按钮')
    点击(device, '/底部导航栏/我的界面跳转按钮')
    目录校验(device, '一', '我的', '/我的/选中状态')
    点击(device, '/我的/我的业务')
    目录校验(device, '二', '办业务', '/我的/我的业务/页面标题')
    上滑(device)
    上滑(device)
    点击(device, '我的/我的业务/我的客服')
    返回(device)
    # 滑动控件至屏幕内('/我的/我的业务/账户管理/找回客户号')
    点击(device, '/我的/我的业务/账户管理/找回客户号')
    if 获取控件对象(device, '/交易/交易登录页面/登陆按钮') != '':
        登录(device, 账号, 交易密码, 通信密码)
    else:
        print('已登录！')
    实际结果 = 获取文本信息(device, '/我的/我的业务/账户管理/找回客户号/标题')
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


@casescript('我的/我的业务/办业务/账户管理_证券账户下挂')
def _6b1722d54d8f8d3e6e4c0829c6214f49(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # 初始化
    登录前页面处理(device)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    # time.sleep(3)
    上滑(device)
    点击(device, '/底部导航栏/理财界面跳转按钮')
    点击(device, '/底部导航栏/我的界面跳转按钮')
    目录校验(device, '一', '我的', '/我的/选中状态')
    点击(device, '/我的/我的业务')
    目录校验(device, '二', '办业务', '/我的/我的业务/页面标题')
    上滑(device)
    上滑(device)
    点击(device, '我的/我的业务/我的客服')
    返回(device)
    # 滑动控件至屏幕内('/我的/我的业务/账户管理/证券账户下挂')
    点击(device, '/我的/我的业务/账户管理/证券账户下挂')
    if 获取控件对象(device, '/交易/交易登录页面/登陆按钮') != '':
        登录(device, 账号, 交易密码, 通信密码)
    else:
        print('已登录！')
    实际结果 = 获取文本信息(device, '/我的/我的业务/账户管理/证券账户下挂/标题')
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


@casescript('我的/我的业务/办业务/账户管理_辅助资金账号新增')
def _05b28cbb5df0043ee3d0894643c7e55e(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # 初始化
    登录前页面处理(device)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    # time.sleep(3)
    上滑(device)
    点击(device, '/底部导航栏/理财界面跳转按钮')
    点击(device, '/底部导航栏/我的界面跳转按钮')
    目录校验(device, '一', '我的', '/我的/选中状态')
    点击(device, '/我的/我的业务')
    目录校验(device, '二', '办业务', '/我的/我的业务/页面标题')
    上滑(device)
    上滑(device)
    点击(device, '我的/我的业务/我的客服')
    返回(device)
    # 滑动控件至屏幕内('/我的/我的业务/账户管理/辅助资金账号新增')
    点击(device, '/我的/我的业务/账户管理/辅助资金账号新增')
    if 获取控件对象(device, '/交易/交易登录页面/登陆按钮') != '':
        登录(device, 账号, 交易密码, 通信密码)
    else:
        print('已登录！')
    实际结果 = 获取文本信息(device, '/我的/我的业务/账户管理/辅助资金账号新增/标题')
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


@casescript('我的/我的业务/办业务/账户管理_辅助资金账号销户')
def _2ee85d041945cf81858a4acf0b789a8a(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # 初始化
    登录前页面处理(device)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    # time.sleep(3)
    上滑(device)
    点击(device, '/底部导航栏/理财界面跳转按钮')
    点击(device, '/底部导航栏/我的界面跳转按钮')
    目录校验(device, '一', '我的', '/我的/选中状态')
    点击(device, '/我的/我的业务')
    目录校验(device, '二', '办业务', '/我的/我的业务/页面标题')
    上滑(device)
    上滑(device)
    点击(device, '我的/我的业务/我的客服')
    返回(device)
    # 滑动控件至屏幕内('/我的/我的业务/账户管理/辅助资金账号销户')
    点击(device, '/我的/我的业务/账户管理/辅助资金账号销户')
    if 获取控件对象(device, '/交易/交易登录页面/登陆按钮') != '':
        登录(device, 账号, 交易密码, 通信密码)
    else:
        print('已登录！')
    try:
        实际结果 = 获取文本信息(device, '/我的/我的业务/账户管理/辅助资金账号销户/提示')
    except:
        实际结果 =获取文本信息(device, '/弹框')
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


@casescript('我的/我的业务/办业务/账户管理_销户办理')
def _baad7505a2366b21cfe116eec4cf2275(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # 初始化
    登录前页面处理(device)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    # time.sleep(3)
    上滑(device)
    点击(device, '/底部导航栏/理财界面跳转按钮')
    点击(device, '/底部导航栏/我的界面跳转按钮')
    目录校验(device, '一', '我的', '/我的/选中状态')
    点击(device, '/我的/我的业务')
    目录校验(device, '二', '办业务', '/我的/我的业务/页面标题')
    上滑(device)
    上滑(device)
    点击(device, '我的/我的业务/我的客服')
    返回(device)
    # 滑动控件至屏幕内('/我的/我的业务/账户管理/销户办理')
    点击(device, '/我的/我的业务/账户管理/销户办理')
    if 获取控件对象(device, '/交易/交易登录页面/登陆按钮') != '':
        登录(device, 账号, 交易密码, 通信密码)
    else:
        print('已登录！')
    实际结果 = 获取文本信息(device, '/我的/我的业务/账户管理/销户办理/标题')
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


