from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('行情/市场/更多/港股/AH股/获取首只AH股股票名称')
def _4eb5d1ed07d3dbd0833fcea71cd5ab82(device):
    登录前页面处理(device)
    print(device.appium.driver)
    点击(device, '/底部导航栏/行情界面跳转按钮')
    点击(device, '/行情/市场/市场标题栏')
    控件左滑(device, '/行情/市场/沪深按钮标签')
    点击(device, '/行情/市场/更多/更多标签')
    滑动控件至屏幕内(device, '/行情/市场/更多/港股/AH股/AH股标签')
    点击(device, '/行情/市场/更多/港股/AH股/AH股标签')
    首只AH股股票名称 = 获取文本信息(device, '/行情/市场/更多/港股/AH股/首只AH股股票名称')
    结果 = 首只AH股股票名称 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


