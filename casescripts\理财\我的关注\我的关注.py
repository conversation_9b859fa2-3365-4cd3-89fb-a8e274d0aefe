from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('理财/我的关注/我的关注/我的关注')
def _3e29931d9fb1d225afb64fce399f2dc2(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # 初始化
    登录前页面处理(device)
    理财登陆(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '理财', '/理财/选中状态')
    点击(device, '/理财/理财首页/我的关注')
    目录校验(device, '二', '我的关注', '/理财/我的关注/页面标题')
    sleep(2)
    实际结果 = 获取文本信息(device, '/理财/理财首页/我的关注/无记录')
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


