from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('我的/我的服务记录/投顾服务/投顾服务')
def _8c42a30372b53f999b18cf01a579e07c(device, 账号='666630296771', 交易密码='124369', 通信密码='159753'):
    登录前页面处理(device)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    上滑(device)
    点击(device, '/底部导航栏/理财界面跳转按钮')
    点击(device, '/底部导航栏/我的界面跳转按钮')
    目录校验(device, '一', '我的', '/我的/选中状态')
    点击(device, '/我的/我的服务记录')
    登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '二', '服务记录', '/页面标题')
    点击(device, '/我的/我的服务记录/投顾服务')
    if 获取控件对象(device, '/我的/我的服务记录/投顾服务/第一条记录'):
        实际结果 = 获取控件对象(device, '/我的/我的服务记录/投顾服务/第一条记录')
    else:
        实际结果 = 获取控件对象(device, '/我的/我的服务记录/投顾服务/无记录')
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


