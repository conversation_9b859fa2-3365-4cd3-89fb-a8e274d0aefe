from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('行情/市场/股指/股指期货/上证50主力')
def _f8675070589cdabd0867a921abde4f55(device):
    登录前页面处理(device)
    点击(device, '/底部导航栏/行情界面跳转按钮')
    目录校验(device, '一', '行情', '/行情/选中状态')
    点击(device, '/行情/市场/市场标题栏')
    点击(device, '/行情/市场/股指/股指标签')
    目录校验(device, '二', '股指', '/行情/更多/选中状态')
    滑动控件至屏幕内(device, '/行情/市场/股指/股指期货/成分品种校验/IH主力品种')
    点击(device, '/行情/市场/股指/股指期货/成分品种校验/IH主力品种')
    价格 = 获取文本信息(device, '/行情/市场/股指/股指期货/成分品种校验/价格')
    涨跌价 = 获取文本信息(device, '/行情/市场/股指/股指期货/成分品种校验/增加价格')
    涨跌百分比 = 获取文本信息(device, '/行情/市场/股指/股指期货/成分品种校验/增加百分比')
    今开 = 获取文本信息(device, '/行情/市场/股指/股指期货/成分品种校验/今开')
    昨结 = 获取文本信息(device, '/行情/市场/股指/股指期货/成分品种校验/昨结')
    最高 = 获取文本信息(device, '/行情/市场/股指/股指期货/成分品种校验/最高')
    最低 = 获取文本信息(device, '/行情/市场/股指/股指期货/成分品种校验/最低')
    成交量 = 获取文本信息(device, '/行情/市场/股指/股指期货/成分品种校验/成交量')
    持仓量 = 获取文本信息(device, '/行情/市场/股指/股指期货/成分品种校验/持仓量')
    结果 = 价格 != '--' and 涨跌价 != '--' and 涨跌百分比 != '--' and 今开 != '--' and 昨结 != '--' and 最高 != '--' and 最低 != '--' and 成交量 != '--' and 持仓量 != '--'
    # 结果 = 价格 != '' and 涨跌价 != '' and 涨跌百分比 != '' and 今开 != '' and 昨结 != '' and 最高 != '' and 最低 != '' and 成交量 != '' and 持仓量 != ''
    错误校验点 = '价格:%s,涨跌价:%s,涨跌百分比:%s,今开:%s,昨结:%s,最高:%s,最低:%s,成交量:%s,持仓量:%s' % (
        价格, 涨跌价, 涨跌百分比, 今开, 昨结, 最高, 最低, 成交量, 持仓量)
    return 结果, 错误校验点


@casescript('行情/市场/股指/股指期货/中证500主力')
def _92d36fa17e1de2f3351013c5172d0c46(device):
    登录前页面处理(device)
    点击(device, '/底部导航栏/行情界面跳转按钮')
    目录校验(device, '一', '行情', '/行情/选中状态')
    点击(device, '/行情/市场/市场标题栏')
    点击(device, '/行情/市场/股指/股指标签')
    目录校验(device, '二', '股指', '/行情/更多/选中状态')
    滑动控件至屏幕内(device, '/行情/市场/股指/股指期货/成分品种校验/IC主力品种')
    点击(device, '/行情/市场/股指/股指期货/成分品种校验/IC主力品种')
    价格 = 获取文本信息(device, '/行情/市场/股指/股指期货/成分品种校验/价格')
    涨跌价 = 获取文本信息(device, '/行情/市场/股指/股指期货/成分品种校验/增加价格')
    涨跌百分比 = 获取文本信息(device, '/行情/市场/股指/股指期货/成分品种校验/增加百分比')
    今开 = 获取文本信息(device, '/行情/市场/股指/股指期货/成分品种校验/今开')
    昨结 = 获取文本信息(device, '/行情/市场/股指/股指期货/成分品种校验/昨结')
    最高 = 获取文本信息(device, '/行情/市场/股指/股指期货/成分品种校验/最高')
    最低 = 获取文本信息(device, '/行情/市场/股指/股指期货/成分品种校验/最低')
    成交量 = 获取文本信息(device, '/行情/市场/股指/股指期货/成分品种校验/成交量')
    持仓量 = 获取文本信息(device, '/行情/市场/股指/股指期货/成分品种校验/持仓量')
    结果 = 价格 != '--' and 涨跌价 != '--' and 涨跌百分比 != '--' and 今开 != '--' and 昨结 != '--' and 最高 != '--' and 最低 != '--' and 成交量 != '--' and 持仓量 != '--'
    # 结果 = 价格 != '' and 涨跌价 != '' and 涨跌百分比 != '' and 今开 != '' and 昨结 != '' and 最高 != '' and 最低 != '' and 成交量 != '' and 持仓量 != ''
    错误校验点 = '价格:%s,涨跌价:%s,涨跌百分比:%s,今开:%s,昨结:%s,最高:%s,最低:%s,成交量:%s,持仓量:%s' % (
        价格, 涨跌价, 涨跌百分比, 今开, 昨结, 最高, 最低, 成交量, 持仓量)
    return 结果, 错误校验点


@casescript('行情/市场/股指/股指期货/成分品种校验_指数成分校验')
def _d034cf361dfa98b29d3af8e39015d896(device):
    登录前页面处理(device)
    点击(device, '/底部导航栏/行情界面跳转按钮')
    目录校验(device, '一', '行情', '/行情/选中状态')
    点击(device, '/行情/市场/市场标题栏')
    点击(device, '/行情/市场/股指/股指标签')
    目录校验(device, '二', '股指', '/行情/更多/选中状态')
    滑动控件至屏幕内(device, '/行情/市场/股指/股指期货/成分品种校验/IC主力品种')
    IC品种 = 获取控件对象(device, '/行情/市场/股指/股指期货/成分品种校验/IC主力品种')
    滑动控件至屏幕内(device, '/行情/市场/股指/股指期货/成分品种校验/IF主力品种')
    IF品种 = 获取控件对象(device, '/行情/市场/股指/股指期货/成分品种校验/IF主力品种')
    滑动控件至屏幕内(device, '/行情/市场/股指/股指期货/成分品种校验/IH主力品种')
    IH品种 = 获取控件对象(device, '/行情/市场/股指/股指期货/成分品种校验/IH主力品种')
    结果 = IC品种 != '' and IF品种 != '' and IH品种 != ''
    错误校验点 = '列表中丢失部分指数'
    return 结果, 错误校验点


@casescript('行情/市场/股指/股指期货/沪深主力')
def _08f1dd90e74547fe4af85d5373eff754(device):
    登录前页面处理(device)
    点击(device, '/底部导航栏/行情界面跳转按钮')
    目录校验(device, '一', '行情', '/行情/选中状态')
    点击(device, '/行情/市场/市场标题栏')
    点击(device, '/行情/市场/股指/股指标签')
    目录校验(device, '二', '股指', '/行情/更多/选中状态')
    滑动控件至屏幕内(device, '/行情/市场/股指/股指期货/成分品种校验/IF主力品种')
    点击(device, '/行情/市场/股指/股指期货/成分品种校验/IF主力品种')
    价格 = 获取文本信息(device, '/行情/市场/股指/股指期货/成分品种校验/价格')
    涨跌价 = 获取文本信息(device, '/行情/市场/股指/股指期货/成分品种校验/增加价格')
    涨跌百分比 = 获取文本信息(device, '/行情/市场/股指/股指期货/成分品种校验/增加百分比')
    今开 = 获取文本信息(device, '/行情/市场/股指/股指期货/成分品种校验/今开')
    昨结 = 获取文本信息(device, '/行情/市场/股指/股指期货/成分品种校验/昨结')
    最高 = 获取文本信息(device, '/行情/市场/股指/股指期货/成分品种校验/最高')
    最低 = 获取文本信息(device, '/行情/市场/股指/股指期货/成分品种校验/最低')
    成交量 = 获取文本信息(device, '/行情/市场/股指/股指期货/成分品种校验/成交量')
    持仓量 = 获取文本信息(device, '/行情/市场/股指/股指期货/成分品种校验/持仓量')
    结果 = 价格 != '--' and 涨跌价 != '--' and 涨跌百分比 != '--' and 今开 != '--' and 昨结 != '--' and 最高 != '--' and 最低 != '--' and 成交量 != '--' and 持仓量 != '--'
    错误校验点 = '价格:%s,涨跌价:%s,涨跌百分比:%s,今开:%s,昨结:%s,最高:%s,最低:%s,成交量:%s,持仓量:%s' % (
        价格, 涨跌价, 涨跌百分比, 今开, 昨结, 最高, 最低, 成交量, 持仓量)
    return 结果, 错误校验点


