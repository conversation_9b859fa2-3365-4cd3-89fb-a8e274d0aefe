from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('我的/我的Level2/立即购买/立即购买')
def _bff4f6c80a6c39416daa2ea745e972d8(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    错误校验点 = ''
    # 初始化
    登录前页面处理(device)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    目录校验(device, '一','我的','/我的/选中状态')
    循环滑动(device, '/我的/我的Level2', '上')
    点击(device, '/我的/我的Level2')
    目录校验(device, '二', '我的十档行情', '/我的/我的Level2/页面标题')
    结果 = ''
    if 获取控件对象(device, '/我的/我的Level2/立即购买') == '':
        预期结果 = '已开通'
        实际结果 = 获取文本信息(device, '/我的/我的Level2/已开通')
        错误校验点 = 非空校验(device, 预期结果, 实际结果)
        结果 = 预期结果 in 实际结果
    else:
        点击(device, '/我的/我的Level2/立即购买')
        点击(device, '/我的/我的Level2/立即购买/立即付款')
        登录(device, 账号, 交易密码, 通信密码)
        点击(device, '/我的/我的交易账户/我的特权/柜台市场/免费一键开通/我已知悉并同意')
        点击(device, '/底部确认/确认按钮')
        点击(device, '/我的/我的Level2/立即购买/确认付款')
        实际结果 = 获取文本信息(device, '/我的/我的Level2/立即购买/购买结果')
        预期结果 = '成功'
        结果 = 预期结果 in 实际结果
        错误校验点 = 非空校验(device, 预期结果, 实际结果)
    return 结果, 错误校验点


