import subprocess

def get_adb_devices():
    p = subprocess.Popen('adb devices', shell=True, stdout=subprocess.PIPE)
    p.stdout.readline()
    devices_list = []
    for line in p.stdout:
        line = line.decode().strip()
        if len(line) > 0:
            d = line.split('\t')
            if len(d) >= 2 and d[1] == 'device':
                devices_list.append(d[0])
    p.wait()
    return devices_list

def open_gnirehtet():
    close_p = subprocess.Popen('kill -9 $(pidof gnirehtet)', shell=True, bufsize=-1, stderr=subprocess.PIPE)
    close_p.wait()
    fp = subprocess.Popen('nohup ~/gnirehtet-rust-linux64/gnirehtet relay 1>log.txt 2>&1 &', shell=True, bufsize=-1, stderr=subprocess.PIPE)
    fp.wait()


open_gnirehtet()

device_list = get_adb_devices()

for device in device_list:
    start_cmd = "~/gnirehtet-rust-linux64/gnirehtet start " + device
    stop_cmd = '~/gnirehtet-rust-linux64/gnirehtet stop ' + device
    stop_output = subprocess.Popen(stop_cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    stop_output.wait()
    start_output = subprocess.Popen(start_cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    start_output.wait()