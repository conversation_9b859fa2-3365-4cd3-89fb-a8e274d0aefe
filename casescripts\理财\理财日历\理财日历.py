from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('理财/理财日历/理财日历/理财日历')
def _54f40a51b2180f725a729d9c4c32e6c3(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # 初始化
    登录前页面处理(device)
    理财登陆(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '理财', '/理财/选中状态')
    滑动控件至屏幕内_理财(device, '/理财/首发基金')
    控件左滑_理财(device, '/理财/首发基金')
    点击(device, '/理财/自定义添加')
    # 循环滑动('/理财/理财日历','上')
    sleep(2)
    上滑(device)
    sleep(2)
    device.appium.driver.background_app(3)
    点击(device, '/理财/理财日历')
    目录校验(device, '二', '理财日历', '/理财/理财日历/页面标题')
    实际结果 = 获取文本信息(device, '/理财/理财日历/第一条记录名称')
    print(实际结果)
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


