from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('行情/市场/沪深/盘中异动/首条异动类型')
def _eedc6f4370d288b87289907e41c8a454(device):
    登录前页面处理(device)
    点击(device, '/底部导航栏/行情界面跳转按钮')
    目录校验(device, '一', '行情', '/行情/选中状态')
    点击(device, '/行情/市场/市场标题栏')
    目录校验(device, '二', '沪深', '/行情/更多/选中状态')
    sleep(3)
    首条异动类型 = 获取文本信息(device, '/行情/市场/沪深/盘中异动（如快速拉升）/首条异动类型')
    print("首条异动类型:::", 首条异动类型)
    结果 = 首条异动类型 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


