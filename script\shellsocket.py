import configparser
import websockets
import asyncio
import datetime
import os
from hashlib import md5
import subprocess
import time

if os.getcwd()[-6:] == 'script':
    os.chdir('../')

test_cfg = "./config.ini"

config_raw = configparser.ConfigParser()
config_raw.read(test_cfg)
API_TOKEN = config_raw.get('cloud', 'API_TOKEN', fallback='')
EU_ID = config_raw.get('cloud', 'EU_ID', fallback='').replace('-', '')
SOCKET_HOST = config_raw.get('cloud', 'SOCKET_HOST', fallback='*************:3000')


def command(cmd, timeout=20):
    """执行命令cmd，返回命令输出的内容。
    如果超时将会抛出TimeoutError异常。
    cmd - 要执行的命令
    timeout - 最长等待时间，单位：秒
    """
    p = subprocess.Popen(cmd, stderr=subprocess.STDOUT, stdout=subprocess.PIPE, shell=True)
    t_beginning = time.time()
    seconds_passed = 0
    while True:
        if p.poll() is not None:
            break
        seconds_passed = time.time() - t_beginning
        if timeout and seconds_passed > timeout:
            p.terminate()
            raise TimeoutError(cmd, timeout)
        time.sleep(0.1)
    return p.stdout.read().decode('GBK' if os.name == 'nt' else 'UTF-8')


async def connect():
    uri = 'ws://%s/%s' % (SOCKET_HOST,md5(('%s.%s.xoquuxh' % (API_TOKEN, EU_ID)).encode('UTF-8')).hexdigest())
    print(uri)
    async with websockets.connect(uri, timeout=10) as ws:
        # sub = subprocess.Popen('cmd.exe' if os.name == 'nt' else '/bin/sh', stdin=subprocess.PIPE, stdout=subprocess.PIPE, stderr=subprocess.PIPE, shell=True)
        while True:
            message = await ws.recv()
            print(message)
            try:
                res = command(message)
                print(res)
                await ws.send(res)
            except TimeoutError:
                await ws.send('timeout')


def run():
    while True:
        try:
            asyncio.get_event_loop().run_until_complete(connect())
        except Exception as e:
            print(e)
            print(datetime.datetime.now(), 'reconnect')
            pass


if __name__ == '__main__':
    if API_TOKEN and EU_ID:
        run()
