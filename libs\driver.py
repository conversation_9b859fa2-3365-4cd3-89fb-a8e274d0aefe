import time

import urllib3
from appium import webdriver
from selenium.common.exceptions import NoSuchElementException
from selenium.webdriver.remote import webelement

from selenium.webdriver.support import expected_conditions
from selenium.webdriver.support.wait import WebDriverWait
from selenium.common.exceptions import TimeoutException

import requests

from libs.log import log_control
from libs.sdk import *


class ElementNotExist(RuntimeWarning):
    def __init__(self, elename):
        self.elename = elename

    def __str__(self):
        return str('未找到 <%s>' % self.elename)


class Element:
    def __init__(self, web_element, path, device):
        self.ele = web_element  # type: webelement.WebElement
        self.path = path
        self.device = device  # type: LocalDevice

    def click(self):
        if 'cpshtzq' in API_URL:
            self.device.log.info('执行点击')
        self.ele.click()

        # x = int(self.location[0] + self.size[0] / 2) if self.size[0] else int(self.location[0])
        # y = int(self.location[1] + self.size[1] / 2) if self.size[1] else int(self.location[1])

        # self.device.appium.driver.tap([(x, y)])

        return self

    @property
    def tag_name(self):
        return self.ele.tag_name

    def text(self):
        _v = self.ele.get_attribute('text')
        if not _v or _v == ' ':
            _v = self.ele.get_attribute('contentDescription')
        return _v

    @property
    def location(self):
        s = self.ele.location
        return s['x'], s['y']

    @property
    def size(self):
        s = self.ele.size
        return s['width'], s['height']

    @property
    def location_middle(self):
        return int(self.ele.location[0] + self.size[0] / 2), int(self.ele.location[1] + self.size[1] / 2)

    def swipe_left(self, duration=500):
        time.sleep(3)
        l = log_control
        self.driver.swipe(self.location[0] + self.size[0], l[1], self.location[0], l[1], duration)
        time.sleep(duration / 1000 + 1)

    def swipe_right(self, duration=500, pix=100):
        time.sleep(3)
        l = log_control
        self.driver.swipe(self.location[0], l[1], self.location[0] + self.size[0], l[1], duration)
        time.sleep(duration / 1000 + 1)

    def submit(self):
        self.ele.submit()
        return self

    def attr(self, name):
        return self.ele.get_attribute(name)

    def is_selected(self):
        return self.ele.is_selected()

    def val(self, value=None):
        if value is None:
            return self.text()
        else:
            # self.ele.click()
            self.ele.clear()
            self.ele.send_keys(value)
            return self

    def __str__(self):
        s = self.text()
        if s:
            return s
        return ''

    def __eq__(self, other):
        if type(other) is str:
            return self.text() == other
        if type(other) is Element:
            return self.ele.id == self.ele.id
        return False

    def __ne__(self, other):
        if type(other) is str:
            return self.text() != other
        if type(other) is Element:
            return self.ele.id != self.ele.id
        return True

    def __int__(self):
        return int(self.text())

    def __call__(self):
        self.click()

    def __lt__(self, other):
        return int(self.text()) < int(other)

    def __le__(self, other):
        return int(self.text()) <= int(other)

    def __gt__(self, other):
        return int(self.text()) > int(other)

    def __ge__(self, other):
        return int(self.text()) < int(other)

    def find(self, xpath):
        return Element(self.ele.find_element_by_xpath(xpath), self.path + ' -> ' + xpath, self.device)


class DeviceDriver(object):
    def __init__(self, device):
        self.device = device

    _driver = None

    @property
    def driver(self):
        if self._driver is None:
            for i in range(10):
                try:
                    if monkey_open == 'true':
                        self.device.shell('am force-stop %s' % self.device.app['package_name'])

                        self._driver = webdriver.Remote('http://%s:%s/wd/hub' % (self.device.appium_ip, self.device.appium_port),self.desired_capabilities_only_server)
                        self.device.shell('monkey -p %s -c android.intent.category.LAUNCHER 1' % (self.device.app['package_name']))
                    else:
                        if time_cost=='true':
                            # 冷启动方式
                            self._driver = webdriver.Remote(
                            'http://%s:%s/wd/hub' % (self.device.appium_ip, self.device.appium_port),
                            self.desired_capabilities_only_server)
                            
                            if video_compu == 'true' and self.device.app['name'] in ['招商银行','邮储银行','中国建设银行','民生企业银行','邮储企业银行','华夏企业银行','中行企业银行','交行企业银行']:
                                self.device.shell('am force-stop %s' % self.device.app['package_name'])
                            else:
                                self.device.shell('am force-stop %s' % self.device.app['package_name'])
                                self.device.log.info('打开应用 %s' % (self.device.app['name']))

                                if isWin:
                                    output = self.device.shell('"am start -W "%s/%s" | findStr TotalTime"' % (self.device.app['package_name'],self.device.app['home_activity']))
                                else:
                                    output = self.device.shell('"am start -W "%s/%s" | grep TotalTime"' % (self.device.app['package_name'],self.device.app['home_activity']))

                                self.device.log.info('冷启动时间 %sms' % (output.split(':')[1].strip()))
                                if output.split(':')[1].strip() and int(output.split(':')[1].strip()) > 0:
                                    self.device.cold_start_time = int(output.split(':')[1].strip())
                                
                        else:
                            # self.device.log.info('--------------------开始启动driver第 %s 次' % (str(i)))
                            self._driver = webdriver.Remote(
                            'http://%s:%s/wd/hub' % (self.device.appium_ip, self.device.appium_port),
                            self.desired_capabilities)
                            # self._driver.start_activity(self.device.app['package_name'],self.device.app['home_activity'])
                            # pass
                        
                    break
                except urllib3.exceptions.MaxRetryError:
                    log_control.info(
                        'webdriver 连接失败: http://%s:%s/wd/hub' % (self.device.appium_ip, self.device.appium_port))
                    time.sleep(1)
                except Exception as e:
                    self.device.log.info('应用启动异常')
                    log_control.error(e)
                    log_control.error('webdriver 连接错误: http://%s:%s/wd/hub' % (self.device.appium_ip, self.device.appium_port))
            # self.device.log.info('开始启动driver启动完成')
            time.sleep(3)

        # 配置项为true时，忽略appium界面解析超时
        if ignore_timeout == 'true':
            self._driver.update_settings({
                'waitForIdleTimeout': 50,
                'waitForSelectorTimeout':50
                })

        return self._driver

    @property
    def is_driver_alive(self):
        return self._driver is not None

    @property
    def desired_capabilities(self):
        udid = self.device.device_id
        deviceName = self.device.device_id
        if WIRED=='true':
            # 无线adb模式下 devicesName为ip:port
            udid = self.device.adb_connect
            deviceName = self.device.adb_connect

        return {
            'platformName': 'Android',
            'platformVersion': self.device.level,  # '7.1.2'
            'deviceName': deviceName,  # 35074d1b
            'udid': udid,  # 35074d1b
            'appPackage': self.device.app['package_name'],  # 'cn.com.essence.stock',
            'appActivity': self.device.app['home_activity'],
            # 'com.zztzt.android.simple.PhoneVGModule.activitys.MainActivity',
            'noReset': True,
            'defaultCommandTimeout': 600,
            'newCommandTimeout': 600,
            'automationName': 'UiAutomator2',
            'unicodeKeyboard': True,
            'resetKeyboard': False,
            'noSign': True,
            'systemPort': self.device.appium_port + 2,
        }
    
    @property
    def desired_capabilities_only_server(self):
        udid = self.device.device_id
        deviceName = self.device.device_id
        if WIRED=='true':
            # 无线adb模式下 devicesName为ip:port
            udid = self.device.adb_connect
            deviceName = self.device.adb_connect

        return {
            'platformName': 'Android',
            'platformVersion': self.device.level,  # '7.1.2'
            'deviceName': deviceName,  # 35074d1b
            'udid': udid,  # 35074d1b
            # 'appPackage': self.device.app['package_name'],  # 'cn.com.essence.stock',
            # 'appActivity': self.device.app['home_activity'],
            'noReset': True,
            'defaultCommandTimeout': 600,
            'newCommandTimeout': 600,
            'automationName': 'UiAutomator2',
            'unicodeKeyboard': True,
            'resetKeyboard': False,
            'noSign': True,
            'systemPort': self.device.appium_port + 2,
        }

    @staticmethod
    def get_element_xpath(object_path):
        from libs.cache import caches
        _cache_name = 'element_path'
        result = caches.get(_cache_name)
        if not result:
            all = None
            for i in range(5):
                try:
                    view_element_url = caches.get('config.VIEW_ELEMENT_URL',
                                                  'http://case.plmt-soft.com/essence-view-element/data.json')
                    print(view_element_url)
                    all = requests.get(view_element_url).json()
                    break
                except:
                    continue
            if not all or 'result' not in all:
                raise
            result = all['result']
            caches.set(_cache_name, result, 86400)
        if object_path in result:
            return '\n'.join(result[object_path])
        res = "xp|//android.view.View[@text='%s']" % object_path.split('/')[-1]
        log_control.info('线上不存在此对象，使用文本查找：【%s】' % object_path.split('/')[-1])
        return res
    
    @staticmethod
    def get_element_image_path(object_path):
        from libs.cache import caches
        _cache_name = 'element_urls_'
        result = caches.get(_cache_name)
        if not result:
            all = None
            for i in range(5):
                try:
                    view_element_image_path = caches.get('config.VIEW_ELEMENT_IMAGES_URL',
                                                  'http://cpspoc.njzfit.cn/api/get_all_element_path')
                    print(view_element_image_path)
                    all = requests.get(view_element_image_path).json()
                    break
                except:
                    continue
            if not all or 'result' not in all:
                raise
            result = all['result']
            caches.set(_cache_name, result, 86400)
        if object_path in result:
            return result[object_path]
        else:
            return ''


    def _find_element_by_xpath_or_id(self, element_path, xpath, type='xp'):
        if element_path.strip('/').split('/')[0] in ['交易', '理财', '行情', '首页', '我的']:
            self.device.find_element_search_last = element_path
        if not self.device.find_element_search_map:
            self.device.find_element_search_map = dict()
        if element_path not in self.device.find_element_search_map:
            self.device.find_element_search_map[element_path] = 0
        try:
            if type == 'id':
                ele = self.driver.find_element_by_id(xpath)
            else:
                ele = self.driver.find_element_by_xpath(xpath)

            self.device.find_element_search_map[element_path] += 1
        except Exception as e:
            self.device.find_element_search_map[element_path] += 1
            raise
        return ele

    def get_element_identifier(self, element_path, trytime=12,ai_time=0):
        if not len(element_path):
            return ElementNotExist('控件为空')
        _element_path = element_path
        if '|' not in element_path:
            xpaths = DeviceDriver.get_element_xpath(element_path)
            if len(xpaths) < 1:
                return ElementNotExist(element_path)
            element_path_list = xpaths.split('\n')
        else:
            element_path_list = [element_path]
        i = 0
        f = 0
        trytime = max(len(element_path_list), trytime)
        while i < trytime:
            f += 1
            _path = element_path_list[f % len(element_path_list)].split('|')
            try:
                i += 1
                if 'cpshtzq' in API_URL:
                    self.device.log.info('开始查找控件 %s' % (element_path))
                _ele = self._find_element_by_xpath_or_id(element_path, _path[1], _path[0])
                if 'cpshtzq' in API_URL:
                    self.device.log.info('已定位控件 %s' % (element_path))
                if _ele:
                    return _ele
            except NoSuchElementException:
                if 'cpshtzq' in API_URL:
                    pass
                else:
                    if _path[1].find('[@text=') > 0:
                        try:
                            if trytime > 3:
                                i += 1
                            _ele = self._find_element_by_xpath_or_id(element_path,
                                                                    _path[1].replace('[@text=', '[@content-desc='))
                            if _ele:
                                return _ele
                        except NoSuchElementException:
                            pass
                    if _path[0] == 'id' and _path[1].find('com.lphtsccft:id') < 0:
                        _li = ['android.widget.EditText', 'android.view.View', 'android.widget.Button']
                        for _l in _li:
                            try:
                                if trytime > 3:
                                    i += 1
                                _ele = self._find_element_by_xpath_or_id(element_path,
                                                                        "//%s[@resource-id='%s']" % (_l, _path[1]))
                                if _ele:
                                    return _ele
                            except NoSuchElementException:
                                pass
                time.sleep(0.3)

            except Exception as e:
                if self.device.find_element_search_last:
                    self.device.log.error(e)
                else:
                    self.device.clean_appium()

        if AI_POLICY=='true' and ai_time < 3:
            try:
                filename = self.device.device_id + str(int((time.time() * 10000))) + ".png"
                self.driver.save_screenshot(filename)
                popup_window = self.device.check_pop()
                if popup_window:
                    self.device.log.info('检测到异常弹窗')
                    self.device.screencap()
                    is_deal = False
                    online_ai=False
                    try:
                        result = paddle_server(filename)
                        online_ai = True
                    except:
                        result = paddle_ocr.ocr(filename, cls=True)

                    for res in result:
                        if online_ai:
                            if res['text'].strip(punctuation) in TIP_KEYWORDS or '知道了' in res['text'].strip(punctuation):
                                x, y = get_location_online_pd(res['text_region'])
                                print(x, y )
                                self.device.log.info('自动识别元素【%s】,坐标(%s,%s)' % (res['text'].strip(punctuation), x, y))
                                is_deal = True
                                self.device.appium.driver.tap([(x, y)])
                                break
                        else:
                            if res[1][0] in TIP_KEYWORDS:
                                x, y = get_location(res)
                                self.device.log.info('自动识别元素【%s】,坐标(%s,%s)' % (res[1][0], x, y))
                                is_deal = True
                                self.device.appium.driver.tap([(x, y)])
                                break
                    
                    if not is_deal:
                        img = cv2.imread(filename)
                        scale = 500 / img.shape[1]
                        img = cv2.resize(img, (0, 0), fx=scale, fy=scale)
                        gray, binary = get_binary_image(img)
                        mg_lbl, regions = selectivesearch.selective_search(binary, SCALE, SIGMA, MIN_SIZE)
                        regions = get_proposal(regions, img.shape)
                        try:
                            rectangles, score_list = get_prediction(binary, regions)
                            if len(score_list) > 0:
                                score = round(max(score_list), 2)
                                rect = rectangles[score_list.index(max(score_list))]
                                position = get_pos(rect, scale)
                                x = position[0]
                                y = position[1]
                                self.device.log.info('自动识别关闭按钮,坐标(%s,%s)' % (x, y))
                                is_deal = True
                                self.device.appium.driver.tap([(x, y)])
                        except Exception as e:
                            self.device.log.info(e)
                    
                    if os.path.exists(filename):
                        os.remove(filename)

                    if is_deal and self.device.check_pop():
                        time.sleep(1)
                        self.get_element_identifier(element_path, 1,trytime=3,ai_time=ai_time+1)
                        # return self.device.appium.get_element_identifier(element_path, 1)
                    else:
                        raise ElementNotExist(_element_path)
                else:
                    if os.path.exists(filename):
                        os.remove(filename)
                    raise ElementNotExist(_element_path)
            except:
                raise ElementNotExist(_element_path)

        raise ElementNotExist(_element_path)


    def _wait_find_element_by_xpath_or_id(self, element_path, xpath, type='xpath',time =10):
        if element_path.strip('/').split('/')[0] in ['交易', '理财', '行情', '首页', '我的']:
            self.device.find_element_search_last = element_path
        if not self.device.find_element_search_map:
            self.device.find_element_search_map = dict()
        if element_path not in self.device.find_element_search_map:
            self.device.find_element_search_map[element_path] = 0
        try:
            if type != 'id':
                type = 'xpath'
            ele = WebDriverWait(self.driver, time).until(expected_conditions.presence_of_element_located((type, xpath)), "未找到%s" % element_path)

            self.device.find_element_search_map[element_path] += 1
            return ele
        except TimeoutException as e:
            try:
                _list = ['android.view.View', 'android.widget.TextView', 'android.widget.Button','android.widget.EditText']
                for _l in _list:
                    try:
                        _ele = WebDriverWait(self.driver, time).until(expected_conditions.presence_of_element_located((type, "//%s[@resource-id='%s']" % (_l, xpath))), "未找到%s" % element_path)
                        self.device.find_element_search_map[element_path] += 1
                        return _ele
                    except TimeoutException as _e:
                        raise ElementNotExist(element_path)
            except TimeoutException as _e:
                raise ElementNotExist(element_path)
            raise ElementNotExist(element_path)
        except NoSuchElementException as e:
            raise ElementNotExist(element_path)
        except Exception as e:
            self.device.find_element_search_map[element_path] += 1
            if self.device.find_element_search_last:
                self.device.log.error(e)
            else:
                self.device.clean_appium()
            


    

    def get_wait_element_identifier(self, element_path, trytime=33,ai_time=0):
        if not len(element_path):
            return ElementNotExist('控件为空')
        _element_path = element_path
        if '|' not in element_path:
            xpaths = DeviceDriver.get_element_xpath(element_path)
            if len(xpaths) < 1:
                return ElementNotExist(element_path)
            element_path_list = xpaths.split('\n')
        else:
            element_path_list = [element_path]
        i = 0
        f = 0

        f += 1
        # _path = element_path_list[f % len(element_path_list)].split('|')
        try:
            for _element_path in element_path_list:
                _path = _element_path.split('|')

                i += 1
                _ele = self._wait_find_element_by_xpath_or_id(element_path, _path[1], _path[0])
                if _ele:
                    return _ele

        except ElementNotExist as e:
            raise ElementNotExist(element_path)


    _on_page = None


    def get_element(self, path: str, trytime=12):
        if not len(path):
            return None
        web_element = self.get_element_identifier(path, trytime)
        return Element(web_element, path, self.device)

    
    def get_wait_element(self, path: str, trytime=12):
        if not len(path):
            return None
        print(path)
        web_element = self.get_wait_element_identifier(path, trytime)
        return Element(web_element, path, self.device)


    def get_element_image(self, path: str):
        if not len(path):
            return None
        element_image_path = DeviceDriver.get_element_image_path(path)

        local_element_image_path= os.path.join('log',cloud_host,element_image_path)

        _cache_name = 'element_images'
        result = caches.get(_cache_name)

        element_image_cv = ''

        if result is not None and path in result:
            element_image_cv = result[path]
            # return result[path]
        else:
            if not os.path.exists(local_element_image_path):
                if not os.path.exists(os.path.dirname(local_element_image_path)):
                    os.makedirs(os.path.dirname(local_element_image_path))

                params = {
                    'path': path
                }
                _view_element_image = caches.get('config.VIEW_ELEMENT_IMAGE',
                                                'http://cpspoc.njzfit.cn/api/get_view_element_image')
                image_json = requests.get(_view_element_image,params=params).json()
                if image_json['code'] == 200:
                    imgdata = base64.b64decode(image_json['base64_data'])
                    file = open(local_element_image_path,'wb')
                    file.write(imgdata)
                    file.close()

                    nparr = np.fromstring(imgdata,np.uint8)  
                    image_cv = cv2.imdecode(nparr,cv2.IMREAD_COLOR)
                
                    if result is not None:
                        result[path] = image_cv
                    else:
                        _res={}
                        _res[path] = image_cv
                        result=_res
                    caches.set(_cache_name, result, 86400)
            else:
                image_data = cv2.imread(local_element_image_path) 
                # base64_data = base64.b64encode(image_data)  # base64编码
                if result is not None:
                    result[path] = image_data
                else:
                    _res={}
                    _res[path] = image_data
                    result=_res
                caches.set(_cache_name, result, 86400)
            
            element_image_cv = result[path]
        
        if element_image_cv !='':
            parent_image = str(int((time.time() * 10000))) + ".png"
            self.driver.save_screenshot(parent_image)
            parent_image_cv = cv2.imread(parent_image) 

            has_image,location = find_element_image(element_image_cv,parent_image_cv)
            if os.path.exists(parent_image):
                os.remove(parent_image)
            
            return has_image,location
        else:
            return False,(0,0)

    def get_element_images(self,path):
        params = {
            'path': path
        }
        _view_element_image = caches.get('config.VIEW_ELEMENT_IMAGE',
                                         'http://cpspoc.njzfit.cn/api/get_view_element_image')
        image_json = requests.get(_view_element_image, params=params).json()
        image_cv = ''
        if image_json['code'] == 200:
            imgdata = base64.b64decode(image_json['base64_data'])
            file = open(local_element_image_path, 'wb')
            file.write(imgdata)
            file.close()
            nparr = np.fromstring(imgdata, np.uint8)
            image_cv = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
        if image_cv:
            parent_image = str(int((time.time() * 10000))) + ".png"
            self.driver.save_screenshot(parent_image)
            parent_image_cv = cv2.imread(filename)  # 要找的大图
            has_image, location = find_element_image(image_cv, parent_image_cv)
            return [has_image,location]
        return False


    def __del__(self):
        self.close()

    def slide_left(self, duration=2000, percentage=0.6):
        if 'cpshtzq' in API_URL:
            time.sleep(1)
        else:
            time.sleep(3)
        l = self.device.screen_width, self.device.screen_height
        x1 = int(l[0] * 0.8)
        x2 = int(l[0] * (0.8 - percentage))
        y1 = int(l[1] * 0.5)
        y2 = int(l[1] * 0.5)
        self.driver.swipe(x1, y1, x2, y2, duration)
        time.sleep(duration / 1000 + 1)

    def slide_right(self, duration=2000, percentage=0.6):
        if 'cpshtzq' in API_URL:
            time.sleep(1)
        else:
            time.sleep(3)
        l = self.device.screen_width, self.device.screen_height
        x1 = int(l[0] * (0.8 - percentage))
        x2 = int(l[0] * 0.8)
        y1 = int(l[1] * 0.5)
        y2 = int(l[1] * 0.5)
        self.driver.swipe(x1, y1, x2, y2, duration)
        time.sleep(duration / 1000 + 1)

    def slide_up(self, duration=2000, percentage=0.6):
        if 'cpshtzq' in API_URL:
            time.sleep(1)
        else:
            time.sleep(3)
        l = self.device.screen_width, self.device.screen_height
        x1 = int(l[0] * 0.5)
        x2 = int(l[0] * 0.5)
        y1 = int(l[1] * 0.8)
        y2 = int(l[1] * (0.8 - percentage))
        self.driver.swipe(x1, y1, x2, y2, duration)

    def slide_down(self, duration=2000, percentage=0.6):
        if 'cpshtzq' in API_URL:
            time.sleep(1)
        else:
            time.sleep(3)
        l = self.device.screen_width, self.device.screen_height
        x1 = int(l[0] * 0.5)
        x2 = int(l[0] * 0.5)
        y1 = int(l[1] * (0.8 - percentage))
        y2 = int(l[1] * 0.8)
        self.driver.swipe(x1, y1, x2, y2, duration)
        time.sleep(duration / 1000)

    def back(self):
        time.sleep(2)
        self.driver.back()

    @hide_error
    def close(self):
        if self._driver:
            if monkey_open == 'true':
                self.device.shell('am force-stop %s' % self.device.app['package_name'])
            else:
                self._driver.close_app()
            # self._driver.close()
        self._driver = None
