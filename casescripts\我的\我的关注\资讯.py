from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('我的/我的关注/资讯/资讯')
def _fadae25c3c87804ab921d487d0b41b6d(device, 账号='666630296771', 交易密码='124369', 通信密码='159753'):
    登录前页面处理(device)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    上滑(device)
    点击(device, '/底部导航栏/理财界面跳转按钮')
    点击(device, '/底部导航栏/我的界面跳转按钮')
    目录校验(device, '一', '我的', '/我的/选中状态')
    点击(device, '/我的/我的关注')
    目录校验(device, '二', '我的关注', '/我的/我的关注/页面标题')
    # 登录(账号, 交易密码, 通信密码)
    点击(device, '/我的/我的关注/资讯')
    if 获取控件对象(device, '/我的/我的关注/第一条记录'):
        实际结果 = 获取控件对象(device, '/我的/我的关注/第一条记录')
    else:
        实际结果 = 获取控件对象(device, '/我的/我的关注/无记录')
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


