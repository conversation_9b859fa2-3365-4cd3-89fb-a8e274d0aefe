import _thread
import os
import queue
import socket
import subprocess
import sys
import zlib
import datetime
from time import sleep
from libs.sdk import WIRED,get_host_ip,API_URL

from libs.log import log_control

server_queue = queue.Queue()


def _appium_ip(device_id):
    """
    一般情况下，*********/8 均可用于环回地址，但在FreeBSD 和 Mac 上 127.0.0.1 以外的地址不可用
    :return: str
    """
    if WIRED=='true':
        return get_host_ip()

    return "127.0.0.1"


def _appium_port(device_id):
    crc32 = zlib.crc32(device_id.encode())
    return (crc32 % 15000) * 4 + 2768


_stdout_path = os.getcwd() + '/log/appium/'
if not os.path.exists(_stdout_path):
    os.makedirs(_stdout_path)


def port_is_open(_ip, _port):
    sk = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    sk.settimeout(1)
    try:
        sk.connect((_ip, _port))
        flag = True
    except Exception:
        flag = False
    sk.close()
    return flag


def get_appium_version():
    p = subprocess.Popen('appium -v', shell=True, stdout=subprocess.PIPE)
    out_line=p.stdout.readlines()[0].decode().strip()
    p.wait()
    if '.' in out_line:
        return out_line
    else:
        return None


APPIUM_SERVER_MAP = {}


def _start(device_id):
    """
    启动 appium server
    :param device_id:
    :return:
    """
    if device_id in APPIUM_SERVER_MAP:
        return

    _ip = _appium_ip(device_id)
    _port = _appium_port(device_id)
    if port_is_open(_ip, _port):
        sleep(1)
        APPIUM_SERVER_MAP[device_id] = True
    else:
        log_file = _stdout_path + device_id + '_server.log'
        shell = "appium -a %s -p %s -bp %s  --selendroid-port %s --session-override --log-level warn:error --log-timestamp --local-timezone --allow-cors --log-no-colors > %s " % (
            _ip, _port, _port + 1, _port + 3, log_file)  # --chromedriver-port %s
        
        
        appium_version = get_appium_version()
        version = appium_version.split('.')[1]
        
        if int(version)!=13:
            shell = "appium -a %s -p %s -bp %s  --session-override --log-level warn:error --log-timestamp --local-timezone --allow-cors --log-no-colors > %s " % (
            _ip, _port, _port + 1, log_file)

        # 华泰2025 性能调试特殊处理
        if 'cpshtzq' in API_URL:
            shell = "appium -a %s -p %s -bp %s  --session-override --log-level debug --log-timestamp --local-timezone --allow-cors --log-no-colors > %s " % (
            _ip, _port, _port + 1, log_file)

        if 'poc' in API_URL:
            shell = "appium -a %s -p %s -bp %s  --session-override --log-level warn:error --log-timestamp --local-timezone --allow-cors --log-no-colors --relaxed-security > %s " % (
            _ip, _port, _port + 1, log_file)

        p = subprocess.Popen(shell, stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL, stdin=subprocess.DEVNULL, shell=True)
        p._shell_cache = shell
        p._shell_start_time = datetime.datetime.now()
        APPIUM_SERVER_MAP[device_id] = p
        log_control.debug('为设备 %s 启动 appium 服务：%s %s' % (device_id, p.pid, shell))
        print('为设备 %s 启动 appium 服务：%s %s' % (device_id, p.pid, shell))


def _finish(device_id):
    """
    结束appium server
    :param device_id:
    :return:
    """
    if device_id not in APPIUM_SERVER_MAP:
        return
    _ip = _appium_ip(device_id)
    _port = _appium_port(device_id)
    p = APPIUM_SERVER_MAP[device_id]
    if type(p) is subprocess.Popen:
        p.kill()
    del APPIUM_SERVER_MAP[device_id]


def appium_server_start(device_id):
    start_listen()
    server_queue.put(['start_server', device_id])


def appium_server_finish(device_id):
    start_listen()
    server_queue.put(['finish_server', device_id])


def _restart_all(_callback):
    restartd = []
    if not callable(_callback):
        return False
    for device_id in APPIUM_SERVER_MAP:
        child = APPIUM_SERVER_MAP[device_id]
        _ip = _appium_ip(device_id)
        _port = _appium_port(device_id)
        if child is True:
            continue
        _shell_start_time = child._shell_start_time
        _time = datetime.datetime.now() - _shell_start_time
        if _callback(child, device_id):
            if _time > datetime.timedelta(seconds=10):
                child.kill()
                poll = child.poll()
                shell = child._shell_cache
                log_control.debug('进程 %s 异常结束，返回码：%s  %s' % (child.pid, poll, shell))
                print('进程 %s 异常结束，返回码：%s  %s' % (child.pid, poll, shell))
                p = subprocess.Popen(shell, stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL, stdin=subprocess.DEVNULL, shell=True)
                p._shell_cache = shell
                p._shell_start_time = datetime.datetime.now()
                APPIUM_SERVER_MAP[device_id] = p
                restartd.append(device_id)
                sleep(1)
            else:
                log_control.debug('进程 %s 异常，等待 %s 秒' % (child.pid, 10 - _time.seconds))
                continue
    return restartd


def _start_appium_server_listen():
    while True:

        try:
            job = server_queue.get_nowait()
            func = job[0]
            args = job[1:]
            if func == 'start_server':
                _start(*args)
            elif func == 'finish_server':
                _finish(*args)
                pass
        except queue.Empty:
            job = None
        sleep(1)


def _start_appium_keep_on():
    while True:
        _restart_all(lambda child, device_id: child.poll() is not None)
        sleep(1)
        _restart_all(lambda child, device_id: not port_is_open(_appium_ip(device_id), _appium_port(device_id)))
        sleep(1)


_start_appium_server_listen.thread = False


def start_listen():
    if not _start_appium_server_listen.thread:
        _thread.start_new_thread(_start_appium_server_listen, ())
        _start_appium_server_listen.thread = True
        _thread.start_new_thread(_start_appium_keep_on, ())
