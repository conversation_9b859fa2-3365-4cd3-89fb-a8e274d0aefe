from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('我的/我的交易账户/密码修改（已覆盖，跳转至密码设置）/密码修改（已覆盖，跳转至密码设置）')
def _e00d362af856bfa277926ecd956c74e3(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119', 预期结果='密码设置'):
    # 初始化
    登录前页面处理(device)
    # 理财登陆(账号, 交易密码, 通信密码)
    交易登录(device, 账号, 交易密码, 通信密码)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    目录校验(device, '一', '我的', '/我的/选中状态')
    # time.sleep(3)
    # 上滑()
    循环滑动(device, '/我的/我的交易账户/密码修改', "上")
    点击(device, '/我的/我的交易账户/密码修改')
    目录校验(device, '二', '密码设置', '/页面标题')
    sleep(2)
    实际结果 = 获取文本信息(device, '/我的/我的交易账户/密码修改/标题')
    print(实际结果)
    错误校验点 = 非空校验(device, 预期结果, 实际结果)
    结果 = 实际结果 == 预期结果
    return 结果, 错误校验点


