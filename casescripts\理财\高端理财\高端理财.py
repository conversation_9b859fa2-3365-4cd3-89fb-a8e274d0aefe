from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('理财/高端理财/高端理财/高端理财')
def _9fa96208ceda1ee7d995b02f6df4aeaa(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # 初始化
    登录前页面处理(device)
    理财登陆(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '理财', '/理财/选中状态')
    点击(device, '/理财/理财首页/高端理财')
    目录校验(device, '二', '高端理财', '/理财/高端理财/页面标题')
    sleep(2)
    实际结果 = ''
    try:
        实际结果 = 获取文本信息(device, '/理财/理财首页/高端理财/第一条产品名称')
    except:
        try:
            实际结果 = 获取文本信息(device, '/理财/高端理财/全部产品/无产品信息')
        except:
            返回(device)
            sleep(3)
            点击(device, '/理财/理财首页/高端理财')
            try:
                实际结果 = 获取文本信息(device, '/理财/理财首页/高端理财/第一条产品名称')
            except:
                实际结果 = 获取文本信息(device, '/理财/高端理财/全部产品/无产品信息')
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


