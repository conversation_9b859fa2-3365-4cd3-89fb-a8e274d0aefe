from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('行情/市场/沪深/策略/精选策略/短线利器/短线利器策略标题')
def _ffd6be1e18c54c0d487235b15045c9e2(device):
    登录前页面处理(device)
    点击(device, '/底部导航栏/行情界面跳转按钮')
    目录校验(device, '一', '行情', '/行情/选中状态')
    点击(device, '/行情/市场/市场标题栏')
    目录校验(device, '二', '沪深', '/行情/更多/选中状态')
    点击(device, '/行情/市场/泰牛智投')
    # 上滑(5000)
    # 上滑(5000)
    滑动控件至屏幕内(device, '/行情/市场/策略/短线利器/盯盘T策略')
    盯盘T策略 = 获取文本信息(device, '/行情/市场/策略/短线利器/盯盘T策略')
    print("盯盘T策略:::", 盯盘T策略)
    结果 = 盯盘T策略 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


