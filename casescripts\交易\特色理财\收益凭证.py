from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('交易/特色理财/收益凭证/收益凭证')
def _1425411e71907b90877c6310cc7626bf(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    错误校验点 = ''
    # 初始化
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    滑动控件至屏幕内(device, '/交易/普通交易/普通交易首页/特色理财')
    点击(device, '/交易/普通交易/普通交易首页/特色理财')
    目录校验(device, '二', '特色理财', '/页面标题')
    sleep(2)
    if 获取控件对象(device, '/交易/普通交易/特色理财/特色理财首页/收益凭证/第一个产品') != '':
        点击(device, '/交易/普通交易/特色理财/特色理财首页/收益凭证/第一个产品')
        sleep(5)
        年化收益率 = 获取文本信息(device, '/交易/普通交易/特色理财/特色理财首页/收益凭证/第一个产品/年化收益率')
        # print('年化收益率：', 年化收益率)
        错误校验点 = 为空校验(device)
        结果 = 年化收益率 != '' and '--' not in 年化收益率
    else:
        print('一大波产品正在路上')
        错误校验点 = 为空校验(device)
        结果 = 获取控件对象(device, '/交易/普通交易/特色理财/特色理财首页/其他产品/无记录')
    return 结果, 错误校验点


