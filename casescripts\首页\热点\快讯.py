from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('首页/热点/快讯/快讯')
def _3daa1ab0de247367ffcc0d9b0b78335e(device):
    # 初始化
    登录前页面处理(device)
    # time.sleep(15)
    点击(device, '/底部导航栏/首页界面跳转按钮')
    目录校验(device, '一', '首页', '/首页/选中状态')
    点击(device, '/首页/热点')
    目录校验(device, '二', '热点', '/首页/热点/选中状态')
    上滑(device)
    # 循环滑动('/首页/热点/快讯','上')
    点击(device, '/首页/热点/快讯')
    实际结果 = 获取文本信息(device, '/首页/热点/快讯/第一条内容')
    print(实际结果)
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


