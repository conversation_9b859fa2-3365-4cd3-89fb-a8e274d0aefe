from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('行情/市场/沪深/龙虎榜/搜索/进入龙虎榜搜索页面')
def _8bdc9cddbc624f9863af7d43ce992659(device):
    登录前页面处理(device)
    点击(device, '/底部导航栏/行情界面跳转按钮')
    目录校验(device, '一', '行情', '/行情/选中状态')
    点击(device, '/行情/市场/市场标题栏')
    目录校验(device, '二', '沪深', '/行情/更多/选中状态')
    控件左滑(device, '/行情/市场/沪深/智能投顾栏目')
    点击(device, '/行情/市场/沪深/龙虎榜/龙虎榜按钮')
    点击(device, '/行情/市场/沪深/主力风云榜/搜索按钮')
    sleep(3)
    搜索页面主题 = 获取文本信息(device, '/行情/市场/沪深/主力风云榜/搜索/搜索页面主题')
    print("搜索页面主题:::", 搜索页面主题)
    结果 = 搜索页面主题 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


