#!/bin/bash
# ubuntu20.04 update nodejs and npm and appium
echo -e "\033[32m---------------------------卸载appium nodejs npm---------------------------------\033[0m"
sudo npm uninstall appium -g
sudo apt remove nodejs -y
sudo apt remove npm -y
sudo apt autoremove -y
sudo rm -rf /usr/local/lib/node_modules/
sudo rm /usr/local/bin/appium
sudo rm /usr/local/bin/authorize-ios
echo -e "\033[32m---------------------------卸载nodejs npm 完成---------------------------------\033[0m"

echo -e "\033[32m---------------------------安装升级版本nodejs---------------------------------\033[0m"
sudo apt update
sudo apt install git curl -y
# sudo apt install nodejs npm -y
curl -fsSL https://deb.nodesource.com/setup_14.x | sudo -E bash -
sudo apt-get install -y nodejs
sudo apt install vim -y

echo -e "\033[32m---------------------------配置环境变量---------------------------------\033[0m"
cd ~
echo "export JAVA_HOME=/usr/lib/jvm/java-8-openjdk-amd64" >> src/ControlSystem/env/bin/activate
sudo sh -c 'echo "export JAVA_HOME=/usr/lib/jvm/java-8-openjdk-amd64" >> /etc/profile'
source /etc/profile

echo -e "\033[32m-----------------------安装新版appium，下载时间较长请耐心等待---------------------------\033[0m"
sudo npm --registry https://registry.npm.taobao.org install -g appium@1.21.0 --unsafe-perm=true --allow-root -y --chromedriver-skip-install
sudo chmod 777 /usr/lib/node_modules/appium/node_modules/appium-uiautomator2-server/apks/appium-uiautomator2-server-debug-androidTest.apk
sudo chmod 777 /usr/lib/node_modules/appium/node_modules/appium-uiautomator2-server/apks/appium-uiautomator2-server-v4.21.1.apk
sudo chmod -R 777 /usr/lib/node_modules/appium/node_modules/appium-uiautomator2-server

echo -e "\033[32m----------------------升级完成--------------------\033[0m"