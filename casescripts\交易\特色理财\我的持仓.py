from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('交易/特色理财/我的持仓/我的持仓')
def _39afae817186f48b93e04e580bc8b667(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # 初始化
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    滑动控件至屏幕内(device, '/交易/普通交易/普通交易首页/特色理财')
    点击(device, '/交易/普通交易/普通交易首页/特色理财')
    目录校验(device, '二', '特色理财', '/页面标题')
    sleep(4)
    点击(device, '/交易/普通交易/特色理财/特色理财首页/持仓按钮')
    sleep(2)
    实际结果 = 获取文本信息(device, '/交易/普通交易/特色理财/我的持仓/特色理财持仓首页/持有金额')
    print(实际结果)
    错误校验点 = 为空校验(device)
    结果 = 实际结果 != ''
    return 结果, 错误校验点


