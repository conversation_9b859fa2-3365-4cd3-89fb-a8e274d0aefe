from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('交易/华泰理财产品/天天发/取现_快速取现')
def _5950af571d53dd51f8e9bb4ec1754cea(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    错误校验点 = ''
    取现金额 = 1000
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    上滑(device)
    点击(device, '/交易/普通交易/普通交易首页/天天发')
    返回(device)
    点击(device, '/交易/普通交易/普通交易首页/天天发')
    目录校验(device, '二', '我的天天发', '/页面标题')
    sleep(5)
    点击(device, '/交易/普通交易/天天发/取现按钮')
    sleep(5)
    点击(device, '/交易/普通交易/天天发/快速取现')
    最多可取金额一 = 获取控件对象(device, '/交易/普通交易/天天发/快速取现/最多可取')
    # 点击('/交易/普通交易/天天发/快速取现/最多可取/刷新')
    输入文本(device, '/交易/普通交易/天天发/取现/输入金额', 取现金额)
    点击(device, '/确定/确定按钮')
    点击(device, '/底部确定/确定按钮')
    try:
        实际结果 = 获取文本信息(device, '/交易/普通交易/天天发/取现/提示信息')
        预期结果 = "已提交"
        结果 = 预期结果 in 实际结果
        错误校验点 = 非空校验(device, 预期结果, 实际结果)
        if 结果:
            返回(device)
            sleep(5)
            点击(device, '/交易/普通交易/天天发/取现按钮')
            sleep(5)
            点击(device, '/交易/普通交易/天天发/快速取现')
            点击(device, '/交易/普通交易/天天发/快速取现/最多可取/刷新')
            sleep(3)
            最多可取金额二 = 获取控件对象(device, '/交易/普通交易/天天发/快速取现/最多可取')
            结果 = 最多可取金额二 < 最多可取金额一
            错误校验点 = '之前最多可取金额为%s,买卖后最多可取金额为%s' % (最多可取金额一, 最多可取金额二)
    except:
        实际结果 = 获取文本信息(device, '/系统提示')
        预期结果 = "失败"
        结果 = 预期结果 in 实际结果
        错误校验点 = 非空校验(device, 预期结果, 实际结果)
    return 结果, 错误校验点


@casescript('交易/华泰理财产品/天天发/取现_预约取现')
def _089d1216553103d35154edc4e4bc1d75(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119', 预期结果='已提交'):
    错误校验点 = ''
    取现金额 = 1
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    上滑(device)
    点击(device, '/交易/普通交易/普通交易首页/天天发')
    返回(device)
    点击(device, '/交易/普通交易/普通交易首页/天天发')
    目录校验(device, '二', '我的天天发', '/页面标题')
    sleep(5)
    点击(device, '/交易/普通交易/天天发/取现按钮')
    sleep(5)
    点击(device, '/交易/普通交易/天天发/预约取现')
    if 获取控件对象(device, '/交易/普通交易/天天发/预约取现/开通按钮'):
        点击(device, '/交易/普通交易/天天发/预约取现/开通按钮')
        点击(device, '/底部确定/确定按钮')
    # 持仓金额1 = 获取文本信息('/交易/普通交易/天天发/预约取现/持仓金额')
    输入文本(device, '/交易/普通交易/天天发/取现/预约取现/输入金额', 取现金额)
    点击(device, '/确定/确定按钮')
    点击(device, '/底部确定/确定按钮')
    try:
        实际结果 = 获取文本信息(device, '/交易/普通交易/天天发/取现/提示信息')
    except:
        实际结果 = 获取文本信息(device, '/系统提示')
    结果 = 预期结果 in 实际结果
    错误校验点 = 为空校验(device)
    if 结果:
        返回(device)
        点击(device, '/交易/普通交易/天天发/撤单')
        点击(device, '/交易/普通交易/天天发/预约取现')
        点击(device, '/交易/普通交易/天天发/预约取现/撤单')
        点击(device, '/底部确定/确定按钮')
        try:
            实际结果 = 获取文本信息(device, '/交易/普通交易/天天发/取现/提示信息')
        except:
            实际结果 = 获取文本信息(device, '/交易/普通交易/天天发/取现/提示信息')
        预期结果 = '成功'
        结果 = 预期结果 in 实际结果
        错误校验点 = 非空校验(device, 预期结果, 实际结果)
        # 结果 = 预期结果 != ''
    return 结果, 错误校验点


@casescript('交易/华泰理财产品/天天发/我的天天发_查询历史明细')
def _692977bcbe3abcba67be3c6266269505(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    错误校验点 = ''
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    上滑(device)
    点击(device, '/交易/普通交易/普通交易首页/天天发')
    返回(device)
    点击(device, '/交易/普通交易/普通交易首页/天天发')
    目录校验(device, '二', '我的天天发', '/页面标题')
    try:
        系统提示信息=获取文本信息(device, '/提示信息/消息内容')
        if '升级' in 系统提示信息 or '维护' in 系统提示信息 or '清算' in 系统提示信息:
            点击(device, '/提示信息/确定按钮')
        # 返回()
        # 点击('/交易/普通交易/普通交易首页/天天发')
        # 返回()
    except:
        pass
    sleep(3)
    # 点击('/交易/普通交易/普通交易首页/进入天天发')
    try:
        点击(device, '/交易/普通交易/普通交易首页/进入天天发')
    except:
        返回(device)
        点击(device, '/交易/普通交易/普通交易首页/天天发')
        sleep(5)
        点击(device, '/交易/普通交易/普通交易首页/进入天天发')
    结果 = False
    if 获取控件对象(device, '/登陆/系统提示'):
        实际结果 = 获取文本信息(device, '/登陆/系统提示内容')
        预期结果 = '系统未初始化'
        错误校验点 = 非空校验(device, 预期结果,实际结果)
        为空校验(device)
        点击(device, '/提示确认/确认按钮')
        结果 = 预期结果 in 实际结果
    else:
        点击(device, '/交易/普通交易/普通交易首页/天天发/历史明细')
        sleep(3)
        if 获取控件对象(device, '/交易/普通交易/普通交易首页/天天发/历史明细/第一条记录'):
            实际结果 = 获取文本信息(device, '/交易/普通交易/普通交易首页/天天发/历史明细/第一条记录')
        else:
            实际结果 = 获取控件对象(device, '/交易/普通交易/普通交易首页/天天发/历史明细')
        错误校验点 = 为空校验(device)
        结果 = 实际结果 != ''
    return 结果, 错误校验点


@casescript('交易/华泰理财产品/天天发/我的天天发_查询当前委托')
def _24fedc249fa0791c63e016d6025c0cac(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    错误校验点 = ''
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    上滑(device)
    点击(device, '/交易/普通交易/普通交易首页/天天发')
    返回(device)
    点击(device, '/交易/普通交易/普通交易首页/天天发')
    目录校验(device, '二', '我的天天发', '/页面标题')
    sleep(3)
    try:
        系统提示信息=获取文本信息(device, '/提示信息/消息内容')
        print("获取文本信息::",获取文本信息)
        if '升级' in 系统提示信息 or '维护' in 系统提示信息 or '清算' in 系统提示信息:
            点击(device, '/提示信息/确定按钮')
        # 返回()
        # 点击('/交易/普通交易/普通交易首页/天天发')
        # 返回()
    except:
        pass
    sleep(3)
    try:
        点击(device, '/交易/普通交易/普通交易首页/进入天天发')
    except:
        返回(device)
        点击(device, '/交易/普通交易/普通交易首页/天天发')
        sleep(5)
        点击(device, '/交易/普通交易/普通交易首页/进入天天发')
    # 点击('/交易/普通交易/普通交易首页/天天发/当前委托')
    结果 = False
    if 获取控件对象(device, '/登陆/系统提示'):
        实际结果 = 获取文本信息(device, '/登陆/系统提示内容')
        预期结果 = '系统未初始化'
        错误校验点 = 非空校验(device, 预期结果, 实际结果)
        为空校验(device)
        点击(device, '/提示确认/确认按钮')
        结果 = 预期结果 in 实际结果
    else:
        if 获取控件对象(device, '/交易/普通交易/普通交易首页/天天发/当前委托/第一条委托'):
            实际结果 = 获取文本信息(device, '/交易/普通交易/普通交易首页/天天发/当前委托/第一条委托')
        else:
            实际结果 = 获取控件对象(device, '/交易/普通交易/普通交易首页/天天发/当前委托')
        错误校验点 = 为空校验(device)
        结果 = 实际结果 != ''
    return 结果, 错误校验点


@casescript('交易/华泰理财产品/天天发/撤单_转入资金')
def _194928f7167649e8d5a5c9aa816d1ca7(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    错误校验点 = ''
    取现金额 = 1000
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    上滑(device)
    点击(device, '/交易/普通交易/普通交易首页/天天发')
    返回(device)
    点击(device, '/交易/普通交易/普通交易首页/天天发')
    目录校验(device, '二', '我的天天发', '/页面标题')
    sleep(5)
    我的天天发余额1 = 获取文本信息(device, '/交易/普通交易/天天发/余额')
    点击(device, '/交易/普通交易/天天发/撤单')
    sleep(5)
    点击(device, '/交易/普通交易/天天发/转入资金')
    if 获取控件对象(device, '/交易/普通交易/天天发/预约取现/撤单'):
        sleep(3)
        点击(device, '/交易/普通交易/天天发/预约取现/撤单')
        点击(device, '/底部确定/确定按钮')
        try:
            实际结果 = 获取文本信息(device, '/交易/普通交易/天天发/取现/提示信息')
        except:
            实际结果 = 获取文本信息(device, '/系统提示')
        预期结果 = '成功'
        结果 = 预期结果 in 实际结果
        错误校验点 = 非空校验(device, 预期结果, 实际结果)
        if 结果:
            返回(device)
            返回(device)
            我的天天发余额2 =获取文本信息(device, '/交易/普通交易/天天发/余额')
            结果 = 我的天天发余额2<我的天天发余额1
            print('之前金额为%s,之后金额为%s' % (我的天天发余额1, 我的天天发余额2))
            错误校验点 = '之前金额为%s,之后金额为%s' % (我的天天发余额1, 我的天天发余额2)
    else:
        实际结果 = 获取文本信息(device, '/交易/普通交易/天天发/取现/无记录')
        结果 = 实际结果 != ''
        错误校验点 = 为空校验(device)
    return 结果, 错误校验点


@casescript('交易/华泰理财产品/天天发/撤单_预约取现')
def _542da2934ccc7ffbbaeddd9ba6c8c211(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    错误校验点 = ''
    取现金额 = 1000
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    上滑(device)
    点击(device, '/交易/普通交易/普通交易首页/天天发')
    返回(device)
    点击(device, '/交易/普通交易/普通交易首页/天天发')
    目录校验(device, '二', '我的天天发', '/页面标题')
    sleep(5)
    点击(device, '/交易/普通交易/天天发/撤单')
    sleep(5)
    点击(device, '/交易/普通交易/天天发/预约取现')
    if 获取控件对象(device, '/交易/普通交易/天天发/预约取现/撤单'):
        点击(device, '/交易/普通交易/天天发/预约取现/撤单')
        点击(device, '/底部确定/确定按钮')
        实际结果 = 获取文本信息(device, '/交易/普通交易/天天发/取现/提示信息')
        预期结果 = '成功'
        结果 = 预期结果 in 实际结果
        错误校验点 = 非空校验(device, 预期结果, 实际结果)
    else:
        实际结果 = 获取文本信息(device, '/交易/普通交易/天天发/取现/无记录')
        结果 = 实际结果 != ''
        错误校验点 = 为空校验(device)
    return 结果, 错误校验点


