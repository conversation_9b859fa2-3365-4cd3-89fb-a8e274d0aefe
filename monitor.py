import csv
import os
import re
import time
import platform
import subprocess
import socket
import json
import psutil
import platform
import datetime
import requests
import configparser


class MobileInfo(object):
    def __init__(self, serialNumber):
        self.serialNumber = serialNumber
        self.grep = 'findStr' if platform.system().lower().startswith('win') else 'grep'

    def get_devices_list(self):
        print(datetime.datetime.now(), '更新设备列表')
        p = subprocess.Popen('adb devices', shell=True, stdout=subprocess.PIPE)
        p.stdout.readline()
        devices_list = []
        for line in p.stdout:
            line = line.decode().strip()
            if len(line) > 0:
                d = line.split('\t')
                if len(d) >= 2 and d[1] == 'device':
                    devices_list.append(d[0])
        p.wait()
        return devices_list

    def get_cpu(self, device):
        cmd1 = "adb -s " + device + " shell dumpsys cpuinfo | " + self.grep + " TOTAL"
        cmd2 = 'adb -s ' + device + ' shell cat /proc/cpuinfo | ' + self.grep + ' processor'
        output = subprocess.Popen(
            cmd1, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE).stdout.readlines()
        cpuInfo = {}
        for line in output:
            line_str = line.decode()
            total = line_str.split(": ")[0]
            cpuInfo['used'] = float(total.split("%")[0])
            cpuInfo['free'] = float(100-float(total.split("%")[0]))

        output2 = subprocess.Popen(
            cmd2, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE).stdout.readlines()

        cpuInfo['number'] = len(output2)
        cpuInfo['deviceId'] = device
        cpuInfo['recordDate'] = datetime.datetime.now().strftime(
            '%Y-%m-%d %H:%M:%S')
        cpuInfo['serialNumber'] = self.serialNumber

        return cpuInfo

    def get_men(self, device):
        # cmd = "adb -s " + device + " shell dumpsys meminfo |" + self.grep + " Used"
        cmd = "adb -s " + device + " shell dumpsys meminfo"
        output = subprocess.Popen(
            cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE).stdout.readlines()
        memoryInfo = {}
        for line in output:
            line_str = line.decode()
            if 'Total RAM' in line_str:
                total = line_str.split("(")[0]
                memoryInfo['total'] = int(total.split(
                    ": ")[1].replace(',', '').replace('K', ''))

            if 'Free RAM' in line_str:
                free = line_str.split("(")[0]
                memoryInfo['free'] = int(free.split(
                    ": ")[1].replace(',', '').replace('K', ''))
            if 'Used RAM' in line_str:
                used = line_str.split("(")[0]
                memoryInfo['used'] = int(used.split(
                    ": ")[1].replace(',', '').replace('K', ''))
            if 'Lost RAM' in line_str:
                lost = line_str.split("(")[0]
                memoryInfo['lose'] = int(lost.split(": ")[1].replace(
                    ',', '').replace('K', '').strip())

        memoryInfo['deviceId'] = device
        memoryInfo['recordDate'] = datetime.datetime.now().strftime(
            '%Y-%m-%d %H:%M:%S')
        memoryInfo['serialNumber'] = self.serialNumber
        return memoryInfo

    def get_battery(self, device):
        battery_cmd = "adb -s %s shell dumpsys battery" % device
        battery = os.popen(battery_cmd)
        battery_dict = {}
        for line in battery.readlines():
            key = line.split(":")[0].strip()
            battery_dict[key] = line.split(":")[1].strip()
        batteryInfo = {}
        batteryInfo['batteryHealthStatus'] = battery_dict['health']
        batteryInfo['batteryTemperature'] = float(battery_dict['temperature'])
        batteryInfo['batteryType'] = battery_dict['technology']
        batteryInfo['chargingState'] = battery_dict['status']
        batteryInfo['deviceId'] = device
        batteryInfo['electricQuantity'] = int(battery_dict['level'])
        if battery_dict['AC powered'] == 'true':
            powerSupplyMode = 'AC powered'
        if battery_dict['USB powered'] == 'true':
            powerSupplyMode = 'USB powered'
        if battery_dict['Wireless powered'] == 'true':
            powerSupplyMode = 'Wireless powered'
        batteryInfo['powerSupplyMode'] = powerSupplyMode

        batteryInfo['recordDate'] = datetime.datetime.now().strftime(
            '%Y-%m-%d %H:%M:%S')
        batteryInfo['serialNumber'] = self.serialNumber

        return batteryInfo

    def get_net(self, device):
        net_cmd = "adb -s %s shell dumpsys netstats | %s iface" % (
            device, self.grep)
        net = os.popen(net_cmd)
        netInfo = {}
        for line in net.readlines():
            if len(re.findall("{type=(.*?),", line)):
                netInfo['netstat'] = re.findall("{type=(.*?),", line)[0]
        netInfo['deviceId'] = device
        netInfo['recordDate'] = datetime.datetime.now().strftime(
            '%Y-%m-%d %H:%M:%S')
        netInfo['serialNumber'] = self.serialNumber

        return netInfo

    def get_disk(self, device):
        diskInfo = {}

        diskInfo['avail'] = 0
        diskInfo['deviceId'] = device
        diskInfo['free'] = 0
        diskInfo['total'] = 0
        diskInfo['recordDate'] = datetime.datetime.now().strftime(
            '%Y-%m-%d %H:%M:%S')
        diskInfo['serialNumber'] = self.serialNumber

        return diskInfo

    def get_name(self, device):
        name_cmd = "adb -s %s shell dumpsys bluetooth_manager | %s name" % (
            device, self.grep)
        name_p = os.popen(name_cmd)
        name = 'device'
        for line in name_p.readlines():
            name = line.split(":")[1].strip()
        return name

    # 单次执行监控过程
    def monitoring(self):
        for device in self.get_devices_list():
            device_dict = {}
            # device_dict['batteryInfo'] = self.get_battery(device)
            batteryInfo = self.get_battery(device)
            device_dict['batteryHealthStatus'] = batteryInfo['batteryHealthStatus']
            device_dict['batteryTemperature'] = batteryInfo['batteryTemperature']
            device_dict['chargingState'] = batteryInfo['chargingState']
            device_dict['electricQuantity'] = batteryInfo['electricQuantity']
            device_dict['powerSupplyMode'] = batteryInfo['electricQuantity']
            device_dict['batteryType'] = batteryInfo['batteryType']

            # device_dict['memoryInfo'] = self.get_men(device)
            memoryInfo = self.get_men(device)
            device_dict['memoryFree'] = memoryInfo['free']
            device_dict['memoryLose'] = memoryInfo['total']
            device_dict['memoryTotal'] = memoryInfo['total']
            device_dict['memoryUsed'] = memoryInfo['used']

            # device_dict['netInfo'] = self.get_net(device)
            netInfo = self.get_net(device)
            device_dict['netstat'] = netInfo['netstat']

            cpuInfo = self.get_cpu(device)
            device_dict['cpuNumber'] = cpuInfo['number']
            device_dict['cpuUsed'] = cpuInfo['used']
            device_dict['cpuFree'] = cpuInfo['free']

            # device_dict['diskInfo'] = self.get_disk(device)
            diskInfo = self.get_disk(device)
            device_dict['diskAvail'] = diskInfo['avail']
            device_dict['diskFree'] = diskInfo['free']
            device_dict['diskTotal'] = diskInfo['total']

            device_dict['deviceId'] = device
            # device_dict['deviceName'] = self.get_name(device)
            device_dict['recordDate'] = datetime.datetime.now().strftime(
                '%Y-%m-%d %H:%M:%S')
            device_dict['serialNumber'] = self.serialNumber

            print(json.dumps(device_dict))
            headers = {"Content-Type": "application/json"}
            r = requests.post(
                'http://112.124.5.206:8088/collectMobileData/addPhoneInfo', data=json.dumps(device_dict), headers=headers)
            print(r.text)


class ComputerInfo(object):
    def __init__(self):
        self.serial_number = ''
        self.os = ''

        test_cfg = "./config.ini"
        config_raw = configparser.ConfigParser()
        config_raw.read(test_cfg)

        self.project_name = config_raw.get(
            'monitor', 'project_name', fallback='')
        self.eu_name = config_raw.get('monitor', 'eu_name', fallback='')
        self.password = config_raw.get('monitor', 'eu_password', fallback='')

    def get_cpu(self):
        """
        cpu信息
        """
        cpuInfoList = []
        cpu_res = psutil.cpu_percent()

        use_rate_all = psutil.cpu_percent(percpu=True)
        recordDate = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        # for index, used in enumerate(use_rate_all):
        #     dicts = {}
        #     dicts['combined'] = cpu_res
        #     dicts['free'] = 100-used
        #     dicts['used'] = used
        #     dicts['number'] = index
        #     dicts['serialNumber'] = self.serial_number
        #     dicts['sys'] = 0
        #     dicts['user'] = 0
        #     dicts['recordDate'] = recordDate

        #     cpuInfoList.append(dicts)
        dicts = {}
        dicts['combined'] = cpu_res
        dicts['free'] = 100-cpu_res
        dicts['used'] = cpu_res
        dicts['number'] = len(use_rate_all)
        dicts['sys'] = 0
        dicts['user'] = 0
        cpuInfoList.append(dicts)

        return cpuInfoList

    def get_memory(self):
        """
        内存 信息
        """
        memory = psutil.virtual_memory()
        dicts = {}
        recordDate = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        dicts['serialNumber'] = self.serial_number
        dicts['total'] = memory.total/1024
        dicts['used'] = memory.used/1024
        dicts['free'] = memory.free/1024
        dicts['recordDate'] = recordDate

        return dicts

    def get_disk(self):
        """
        磁盘
        """
        partitions = psutil.disk_partitions()
        recordDate = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        diskInfoList = []
        disk_dict = {
            'devName': '',
            'dirName': '',
            'sysTypeName': '',
            'typeName': '',
            'total': 0,
            'free': 0,
            'avail': 0,
            'used': 0,
            'usePercent': 0,
            'diskReads': 0,
            'diskWrites': 0,
        }
        for i in partitions:
            try:
                foo = psutil.disk_usage(i.mountpoint)
                # disk_dict = {}
                # disk_dict['devName'] = i.device
                # disk_dict['dirName'] = i.mountpoint
                # disk_dict['sysTypeName'] = i.fstype
                # disk_dict['typeName'] = i.opts
                # disk_dict['total'] = foo.total/1024
                # disk_dict['free'] = foo.free/1024
                # disk_dict['avail'] = foo.free/1024
                # disk_dict['used'] = foo.used/1024
                # disk_dict['serialNumber'] = self.serial_number
                # disk_dict['usePercent'] = foo.percent
                # disk_dict['diskReads'] = 0
                # disk_dict['diskWrites'] = 0
                # disk_dict['recordDate'] = recordDate

                # diskInfoList.append(disk_dict)

                disk_dict['sysTypeName'] = i.fstype
                disk_dict['typeName'] = i.opts
                disk_dict['total'] = foo.total+disk_dict['total']
                disk_dict['free'] = foo.free+disk_dict['free']
                disk_dict['avail'] = foo.free+disk_dict['avail']
                disk_dict['used'] = foo.used+disk_dict['used']

                # disk_dict['serialNumber'] = self.serial_number
                # disk_dict['usePercent'] = foo.percent
                disk_dict['diskReads'] = 0
                disk_dict['diskWrites'] = 0

            except Exception as e:
                print(e)

        disk_dict['usePercent'] = disk_dict['used']/disk_dict['total']
        disk_dict['total'] = disk_dict['total']/1024
        disk_dict['free'] = disk_dict['free']/1024
        disk_dict['avail'] = disk_dict['avail']/1024
        disk_dict['used'] = disk_dict['used']/1024

        diskInfoList.append(disk_dict)

        return diskInfoList

    def get_net(self):
        """
        网路信息
        """
        recordDate = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        net_if = psutil.net_if_addrs()
        net_io = psutil.net_io_counters(pernic=True)
        netInfoList = []
        netInfo = {
            'rxBytes': 0,
            'rxDropped': 0,
            'rxErrors': 0,
            'rxPackets': 0,
            'txBytes': 0,
            'txDropped': 0,
            'txErrors': 0,
            'txPackets': 0,
            'mac': '',
            'ip': ''
        }
        for net in net_if:
            dict_net = {}
            dict_net['netName'] = net

            for sni in net_if[net]:
                if str(sni.family) == 'AddressFamily.AF_LINK':
                    dict_net['mac'] = sni.address
                if str(sni.family) == 'AddressFamily.AF_INET':
                    dict_net['ip'] = sni.address

            for io in net_io:
                if net == io:
                    dict_net['rxBytes'] = net_io[io].bytes_recv
                    dict_net['rxDropped'] = net_io[io].dropin
                    dict_net['rxErrors'] = net_io[io].errin
                    dict_net['rxPackets'] = net_io[io].packets_recv

                    dict_net['txBytes'] = net_io[io].bytes_sent
                    dict_net['txDropped'] = net_io[io].dropout
                    dict_net['txErrors'] = net_io[io].errout
                    dict_net['txPackets'] = net_io[io].packets_sent

            dict_net['recordDate'] = recordDate
            dict_net['serialNumber'] = self.serial_number
            # netInfoList.append(dict_net)

            netInfo['rxBytes'] = netInfo['rxBytes'] + dict_net['rxBytes']
            netInfo['rxDropped'] = netInfo['rxDropped'] + dict_net['rxDropped']
            netInfo['rxErrors'] = netInfo['rxErrors'] + dict_net['rxErrors']
            netInfo['rxPackets'] = netInfo['rxPackets'] + dict_net['rxPackets']
            netInfo['txBytes'] = netInfo['txBytes'] + dict_net['txBytes']
            netInfo['txDropped'] = netInfo['txDropped'] + dict_net['txDropped']
            netInfo['txErrors'] = netInfo['txErrors'] + dict_net['txErrors']
            netInfo['txPackets'] = netInfo['txPackets'] + dict_net['txPackets']
            if 'mac' in dict_net.keys():
                netInfo['mac'] = dict_net['mac'] if netInfo['mac'] == '' else netInfo['mac'] + \
                    ',' + dict_net['mac']
            if 'ip' in dict_net.keys():
                netInfo['ip'] = dict_net['ip']if netInfo['ip'] == '' else netInfo['ip'] + \
                    ',' + dict_net['ip']

        netInfo['netName'] = 'all'
        netInfo['recordDate'] = recordDate
        netInfo['serialNumber'] = self.serial_number
        netInfoList.append(netInfo)

        print(netInfoList)
        return netInfoList

    def get_base(self):
        """
        基础信息
        computer_name 计算机名
        ip 本机IP地址
        user_name 用户名
        system 系统
        cpu_name cpu型号
        cpu_number cpu序列号
        disk_number 硬盘序列号
        mac mac地址
        :return:
        """
        dicts = {}

        # dicts['serverName'] = socket.gethostname()
        dicts['serverName'] = self.project_name + '-'+self.eu_name
        dicts['serverIp'] = socket.gethostbyname(socket.gethostname())
        # dicts['user_name'] = getpass.getuser()
        dicts['osName'] = platform.system()
        dicts['osArch'] = ' '.join(platform.architecture())
        dicts['osVersion'] = platform.version()
        dicts['osDesc'] = platform.platform()

        OS = platform.system()
        if OS == "Windows":
            cmd = 'wmic bios get serialnumber'
            output = subprocess.Popen(
                cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE).stdout.readlines()
            if len(output) > 2:
                self.serial_number = output[1].decode().strip()
            else:
                self.serial_number = socket.gethostname()

        elif OS == "Darwin":
            cmd = 'ioreg -l | grep IOPlatformSerialNumber'
            output = subprocess.Popen(
                cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE).stdout.readlines()
            self.serial_number = output[0].decode().split(
                '=')[1].replace('"', '').strip()

        else:
            cmd = "echo %s | sudo -S dmidecode -t system | grep Serial" % self.password
            output = subprocess.Popen(
                cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE).stdout.readlines()
            self.serial_number = output[0].decode().split(':')[1].strip()

        dicts['serialNumber'] = self.serial_number

        return dicts

    def monitoring(self):
        emu_dict = {}
        # emu_dict['propertyInfo'] = self.get_base()
        propertyInfo = self.get_base()
        emu_dict['serverName'] = propertyInfo['serverName']
        emu_dict['serverIp'] = propertyInfo['serverIp']
        emu_dict['osName'] = propertyInfo['osName']
        emu_dict['osArch'] = propertyInfo['osArch']
        emu_dict['osDesc'] = propertyInfo['osDesc']
        emu_dict['osVersion'] = propertyInfo['osVersion']
        emu_dict['serialNumber'] = propertyInfo['serialNumber']

        # emu_dict['netInfoList'] = self.get_net()
        netInfo = self.get_net()[0]
        emu_dict['netRxBytes'] = netInfo['rxBytes']
        emu_dict['netRxDropped'] = netInfo['rxDropped']
        emu_dict['netRxErrors'] = netInfo['rxErrors']
        emu_dict['netRxPackets'] = netInfo['rxPackets']
        emu_dict['netTxBytes'] = netInfo['txBytes']
        emu_dict['netTxDropped'] = netInfo['txDropped']
        emu_dict['netTxErrors'] = netInfo['txErrors']
        emu_dict['netTxPackets'] = netInfo['txPackets']
        emu_dict['netMac'] = netInfo['mac']
        emu_dict['netIp'] = netInfo['ip']
        emu_dict['netDesc'] = netInfo['netName']
        emu_dict['netName'] = netInfo['netName']

        # emu_dict['memoryInfo'] = self.get_memory()
        memoryInfo = self.get_memory()
        emu_dict['memoryFree'] = memoryInfo['free']
        emu_dict['memoryTotal'] = memoryInfo['total']
        emu_dict['memoryUsed'] = memoryInfo['used']

        # emu_dict['diskInfoList'] = self.get_disk()
        diskInfo = self.get_disk()[0]
        emu_dict['diskUsePercent'] = diskInfo['usePercent']
        emu_dict['diskTotal'] = diskInfo['total']
        emu_dict['diskFree'] = diskInfo['free']
        emu_dict['diskAvail'] = diskInfo['avail']
        emu_dict['diskUsed'] = diskInfo['used']
        emu_dict['diskReads'] = diskInfo['diskReads']
        emu_dict['diskWrites'] = diskInfo['diskWrites']

        # emu_dict['cpuInfoList'] = self.get_cpu()
        cpuInfo = self.get_cpu()[0]
        emu_dict['cpuCombined'] = cpuInfo['combined']
        emu_dict['cpuFree'] = cpuInfo['free']
        emu_dict['cpuUsed'] = cpuInfo['used']
        emu_dict['cpuNumber'] = cpuInfo['number']
        emu_dict['cpuSys'] = cpuInfo['sys']
        emu_dict['cpuUser'] = cpuInfo['user']

        emu_dict['recordDate'] = datetime.datetime.now().strftime(
            '%Y-%m-%d %H:%M:%S')
        emu_dict['serialNumber'] = self.serial_number

        print(json.dumps(emu_dict))
        headers = {"Content-Type": "application/json"}
        r = requests.post(
            'http://112.124.5.206:8088/collectEmuData/addEmuInfo', data=json.dumps(emu_dict), headers=headers)
        print(r.text)


if __name__ == "__main__":
    cio = ComputerInfo()
    cio.get_base()
    serialNumber = cio.serial_number
    mio = MobileInfo(serialNumber)

    while True:
        try:
            cio.monitoring()
            mio.monitoring()
        except Exception as e:
            print(e)
        time.sleep(10)