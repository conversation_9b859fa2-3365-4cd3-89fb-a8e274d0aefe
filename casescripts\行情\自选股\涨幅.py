from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('行情/自选股/涨幅/检查首支个股涨幅')
def _6fd8fd9f6f35194db007a1b71c3e0459(device, 股票代码='600123'):
    错误校验点 = ''
    登录前页面处理(device)
    点击(device, '/底部导航栏/行情界面跳转按钮')
    目录校验(device, '一', '行情', '/行情/选中状态')
    点击(device, '/行情/自选股/自选股标签')
    目录校验(device, '二', '自选股', '/行情/自选股')
    try:
        第一条个股涨幅 = 获取文本信息(device, '/行情/自选股/涨幅/无记录')
        点击(device, '/行情/自选股/添加自选股')
        输入文本(device, '/行情/自选股/添加自选股/输入框', 股票代码)
        点击(device, '/行情/自选股/添加自选股/搜索')
        点击(device, '/行情/自选股/添加自选股/添加')
        点击(device, '/行情/自选股/添加自选股/返回')
        第一条个股涨幅 = 获取文本信息(device, '/行情/自选股/涨幅/第一条个股涨幅')
    except:
        第一条个股涨幅 = 获取文本信息(device, '/行情/自选股/涨幅/第一条个股涨幅')
    print('第一条个股涨幅::', 第一条个股涨幅)
    结果 = 第一条个股涨幅 != ''
    if 第一条个股涨幅 == '':
        错误校验点 = '无涨幅字段数据'
    return 结果, 错误校验点


