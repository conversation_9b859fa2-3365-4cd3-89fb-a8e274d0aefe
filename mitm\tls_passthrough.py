"""
This addon allows conditional TLS Interception based on a user-defined strategy.

Example:

    > mitmdump -s tls_passthrough.py

    1. curl --proxy http://localhost:8080 https://example.com --insecure
    // works - we'll also see the contents in mitmproxy

    2. curl --proxy http://localhost:8080 https://example.com
    // fails with a certificate error, which we will also see in mitmproxy

    3. curl --proxy http://localhost:8080 https://example.com
    // works again, but mitmproxy does not intercept and we do *not* see the contents
"""

import collections
import logging
import random
from abc import ABC
from abc import abstractmethod
from enum import Enum
import asyncio
import aiohttp
import datetime
import time

import mitmproxy
from mitmproxy import connection
from mitmproxy import ctx,http,proxy
from mitmproxy import tls
from mitmproxy.addonmanager import Loader
from mitmproxy.utils import human


# 异步保存数据的函数
async def save_data(data):
    url = "http://*************:18091/MoiraiService/addCpsRequestInfo"

    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(url, json=data) as response:
                if response.content_type == 'application/json':
                    result = await response.json()
                else:
                    result = await response.text()
        ctx.log.info(f"Data saved successfully: {result}")
    except Exception as e:
        ctx.log.error(f"Error saving data: {e}")


class InterceptionResult(Enum):
    SUCCESS = 1
    FAILURE = 2
    SKIPPED = 3


class TlsStrategy(ABC):
    def __init__(self):
        # A server_address -> interception results mapping
        self.history = collections.defaultdict(lambda: collections.deque(maxlen=200))

    @abstractmethod
    def should_intercept(self, server_address: connection.Address) -> bool:
        raise NotImplementedError()

    def record_success(self, server_address):
        self.history[server_address].append(InterceptionResult.SUCCESS)

    def record_failure(self, server_address):
        self.history[server_address].append(InterceptionResult.FAILURE)

    def record_skipped(self, server_address):
        self.history[server_address].append(InterceptionResult.SKIPPED)


class ConservativeStrategy(TlsStrategy):
    """
    Conservative Interception Strategy - only intercept if there haven't been any failed attempts
    in the history.
    """

    def should_intercept(self, server_address: connection.Address) -> bool:
        return InterceptionResult.FAILURE not in self.history[server_address]


class ProbabilisticStrategy(TlsStrategy):
    """
    Fixed probability that we intercept a given connection.
    """

    def __init__(self, p: float):
        self.p = p
        super().__init__()

    def should_intercept(self, server_address: connection.Address) -> bool:
        return random.uniform(0, 1) < self.p


class MaybeTls:
    strategy: TlsStrategy

    def load(self, loader: Loader):
        loader.add_option(
            "tls_strategy",
            int,
            0,
            "TLS passthrough strategy. If set to 0, connections will be passed through after the first unsuccessful "
            "handshake. If set to 0 < p <= 100, connections with be passed through with probability p.",
        )

    def configure(self, updated):
        if "tls_strategy" not in updated:
            return
        if ctx.options.tls_strategy > 0:
            self.strategy = ProbabilisticStrategy(ctx.options.tls_strategy / 100)
        else:
            self.strategy = ConservativeStrategy()

    @staticmethod
    def get_addr(server: connection.Server):
        # .peername may be unset in upstream proxy mode, so we need a fallback.
        return server.peername or server.address


    def tls_clienthello(self, data: tls.ClientHelloData):
        server_address = self.get_addr(data.context.server)
        if not self.strategy.should_intercept(server_address):
            logging.info(f"TLS passthrough: {human.format_address(server_address)}.")
            print(f"TLS tls_clienthello: {human.format_address(server_address)}.")
            print(f"TLS passthrough: {human.format_address(server_address)}.")
            data.ignore_connection = True
            self.strategy.record_skipped(server_address)

    def tls_established_client(self, data: tls.TlsData):
        server_address = self.get_addr(data.context.server)
        logging.info(
            f"TLS handshake successful: {human.format_address(server_address)}"
        )
        print(f"TLS handshake successful: {human.format_address(server_address)}")
        self.strategy.record_success(server_address)

    def tls_failed_client(self, data: tls.TlsData):
        server_address = self.get_addr(data.context.server)
        logging.info(f"TLS handshake failed: {human.format_address(server_address)}")
        print(f"TLS handshake failed: {human.format_address(server_address)}")
        self.strategy.record_failure(server_address)





    # def tls_clienthello(data: mitmproxy.tls.ClientHelloData):
    #     pass

    def tls_start_client(self, data: mitmproxy.tls.TlsData):
        # TLS negotation between mitmproxy and a client is about to start.
        # An addon is expected to initialize data.ssl_conn. (by default, this is done by mitmproxy.addons.tlsconfig)
        pass

    

    def tls_start_server(self, data: mitmproxy.tls.TlsData):
        # TLS negotation between mitmproxy and a server is about to start.
        # An addon is expected to initialize data.ssl_conn. (by default, this is done by mitmproxy.addons.tlsconfig)
        pass

    # def tls_established_client(self, data: mitmproxy.tls.TlsData):
    #     # The TLS handshake with the client has been completed successfully.
    #     pass

    def tls_established_server(self, data: mitmproxy.tls.TlsData):
        # The TLS handshake with the server has been completed successfully.
        pass

    # def tls_failed_client(self, data: mitmproxy.tls.TlsData):
    #     # The TLS handshake with the client has failed.
    #     pass

    def tls_failed_server(self, data: mitmproxy.tls.TlsData):
        pass

    def requestheaders(self, flow: mitmproxy.http.HTTPFlow):
        pass

    def request(self, flow: http.HTTPFlow) -> None:
        client_conn = flow.client_conn
        server_conn = flow.server_conn

        data = {
            "dt":datetime.datetime.now().strftime("%Y-%m-%d"),
            "method": flow.request.method,
            "url": flow.request.url,
            # "headers": dict(flow.request.headers),
            # "content": flow.request.content.decode('utf-8', errors='ignore'),
            # "content_type" :dict(flow.request.headers)['Content-Type'] if 'Content-Type' in dict(flow.request.headers).keys() else dict(flow.request.headers),
            # "content_length" :dict(flow.request.headers)['Content-Length']if 'Content-Length' in dict(flow.request.headers).keys() else dict(flow.request.headers),
            "cce":client_conn.timestamp_start,
            "cctls":client_conn.timestamp_tls_setup,
            "frb":flow.request.timestamp_start,
            "rc":flow.request.timestamp_end,
            "sci":server_conn.timestamp_start,
            "sctcp":server_conn.timestamp_tcp_setup,
            "sctls":server_conn.timestamp_tls_setup,
            # "client_conn.timestamp_start": client_conn.timestamp_start,
            # "client_conn.timestamp_tcp_setup": client_conn.timestamp_tcp_setup,
            # "client_conn.timestamp_tls_setup": client_conn.timestamp_tls_setup,
            # "client_conn.timestamp_end": client_conn.timestamp_end,
            # "request.timestamp_start": flow.request.timestamp_start,
            # "request.timestamp_end": flow.request.timestamp_end,
            # "response.timestamp_start": flow.response.timestamp_start if flow.response else '',
            # "response.timestamp_end": flow.response.timestamp_end if flow.response else '',
            "flow_id":flow.id,
            "client_ip":flow.client_conn.address[0],
            "server_ip":flow.server_conn.ip_address[0] if flow.server_conn.ip_address else '',
            "connection_id":flow.server_conn.id,
            "stage":"request",
            "timestamp":time.time()*1000
        }

        asyncio.create_task(save_data(data))
    

    def responseheaders(self, flow: mitmproxy.http.HTTPFlow):
        pass

    def response(self, flow: http.HTTPFlow) -> None:
        client_conn = flow.client_conn
        server_conn = flow.server_conn
        data = {
            "dt":datetime.datetime.now().strftime("%Y-%m-%d"),
            "method": flow.request.method,
            "url": flow.request.url,
            # "headers": dict(flow.request.headers),
            # "content": flow.request.content.decode('utf-8', errors='ignore'),
            "content_type" :flow.request.headers.get('Content-Type', ''),
            "content_length" :len(flow.response.content),
            "cce":client_conn.timestamp_start,
            "cctls":client_conn.timestamp_tls_setup,
            "frb":flow.request.timestamp_start,
            "rc":flow.request.timestamp_end,
            "sci":server_conn.timestamp_start,
            "sctcp":server_conn.timestamp_tcp_setup,
            "sctls":server_conn.timestamp_tls_setup,
            "res_code":str(flow.response.status_code),
            "flow_id":flow.id,
            "client_ip":flow.client_conn.address[0],
            "server_ip":flow.server_conn.ip_address[0] if flow.server_conn.ip_address else '',
            "connection_id":flow.server_conn.id,
            "stage":"response",
            "timestamp":time.time()*1000
        }

        asyncio.create_task(save_data(data))

    def error(self, flow: mitmproxy.http.HTTPFlow):
        pass

    def http_connect(self, flow: http.HTTPFlow):
        # print('http_connect:::: ')
        pass

    def http_connected(self, flow: http.HTTPFlow):
        pass

    def http_connect_error(self, flow: mitmproxy.http.HTTPFlow):
        pass

    def client_connected(self, client: mitmproxy.connection.Client):
        pass

    def client_disconnected(self, client: mitmproxy.connection.Client):
        pass

    def server_connect(self, data: proxy.server_hooks.ServerConnectionHookData):
        # print('server_connect:::: ' + data.server.address[0])
        pass

    def server_disconnected(self, data: proxy.server_hooks.ServerConnectionHookData):
        # print('server_disconnected:::: ' + data.server.address[0])
        pass

    def server_connect(self, data: proxy.server_hooks.ServerConnectionHookData):
        # print('server_connect:::: ' + data.server.address[0])
        # print(data)
        pass

    def server_connect_error(self, data: proxy.server_hooks.ServerConnectionHookData):
        # print('server_connect_error:::: ' + data.server.address[0])
        pass

addons = [MaybeTls()]
