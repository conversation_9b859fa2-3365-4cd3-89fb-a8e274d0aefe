import os
import subprocess
import time
import datetime
import minio
import oss2
from libs.adb import get_adb_devices,LocalDevice,register_device

upload_type='s3'

def upload_file(device_id, local):
    if not os.path.exists(local):
        return None
    
    upload_status = False

    if upload_type == 's3':
        s3_bucket_name = 'huatai'
        endpoint = '192.168.1.222:9000'
        minioClient = minio.Minio(endpoint,
                                    access_key='AX_USER',
                                    secret_key='B54WcsjnbfDy96E426Cvjwe',
                                    secure=False)

        day_str = os.path.basename(local).split('_')[1].replace('log','')
        hour_str = os.path.basename(local).split('_')[2][0:2]
        remote = 'track/' + device_id + '/' + day_str + '/' + hour_str + '/' + os.path.basename(local)
        print(remote)
        for _ in range(3):
            try:
                with open(local, 'rb') as file_data:
                    file_stat = os.stat(local)
                    res=minioClient.put_object(s3_bucket_name, remote, file_data, file_stat.st_size,"text/plain")
                    upload_status = True
                    print(res)
                break
            except Exception as e:
                print(e)
                pass
    else:
        oss_image_access_key_id = 'LTAI5t7MSZ6uKgzq1Hny5mXM'
        oss_image_access_key_secret = '******************************'
        oss_image_endpoint = 'http://oss-cn-hangzhou.aliyuncs.com'
        oss_image_auth = oss2.Auth(oss_image_access_key_id, oss_image_access_key_secret)
        oss_image_bucket = 'zfkj-cps'
        
        if oss_image_access_key_id=='LTAI5t7MSZ6uKgzq1Hny5mXM':
            # 新oss id
            remote = 'track/' + device_id + '/' + day_str + '/'
            oss_bucket = oss2.Bucket(oss_image_auth, oss_image_endpoint, oss_image_bucket)

        for _ in range(3):
            try:
                oss_bucket.put_object_from_file(remote, local)
                upload_status = True
                break
            except:
                pass
    
    if upload_status:
        os.unlink(local)

# device_list = get_adb_devices()
# 'C7YVB20429011401',
device_list = ['SNHVB20B20013704']
local_path = 'log/track/'

while True:
    for device in device_list:
        dir_path = '/storage/emulated/0/Android/data/com.lphtsccft/files/Download/request_log/'
        shell_line='adb -s %s shell ls %s -lrt' % (device,dir_path)
        p = subprocess.Popen(shell_line, shell=True, stdout=subprocess.PIPE)
        # p.stdout.readline()
        # for line in p.stdout:
        lines = p.stdout.readlines()
        for line in lines:
            line = line.decode().strip()
            print(line)
            file_name = line.split(' ')[-1]
            print(line.split(' '))
            if '.txt' in file_name:
                device_path = dir_path + file_name
                p_pull = subprocess.Popen("adb -s %s pull %s %s" % (device, device_path, local_path), shell=True,stdout=subprocess.DEVNULL)
                while p_pull.poll() is None:
                    time.sleep(0.1)
                time.sleep(0.1)
                subprocess.call('adb -s %s shell rm %s ' % (device, device_path), timeout=10, shell=True)

                upload_file(device,local_path + file_name)
    
    time.sleep(600)





