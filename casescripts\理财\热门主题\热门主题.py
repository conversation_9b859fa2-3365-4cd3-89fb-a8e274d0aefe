from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('理财/热门主题/热门主题/热门主题')
def _129212d7b5aa32e73cd4b6cbc31af5c2(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # 初始化
    登录前页面处理(device)
    理财登陆(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '理财', '/理财/选中状态')
    # 滑动控件至屏幕内_理财('/理财/快捷功能')
    滑动控件至屏幕内_理财(device, '/理财/首发基金')
    控件左滑_理财(device, '/理财/首发基金')
    sleep(2)
    点击(device, '/理财/热门主题')
    目录校验(device, '二', '主题基金', '/页面标题')
    实际结果 = float(获取文本信息(device, '/理财/热门主题/第一个收益率')[0:-1])
    print(实际结果)
    if 实际结果 >= 0:
        结果 = True
    else:
        结果 = False
    错误校验点 = 数字校验(device, 实际结果)
    return 结果, 错误校验点


