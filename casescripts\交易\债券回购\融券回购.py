from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('交易/债券回购/融券回购/融券回购')
def _104a9d0cc10c87a01c8ad12e069ebd87(device, 账号='01028889', 交易密码='123123', 通信密码='123123', 证券代码='010107', 交易数量='1', 预期结果='成功'):
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    上滑(device)
    sleep(1)
    点击(device, '/交易/普通交易/普通交易首页/其他交易')
    目录校验(device, '二', '其他交易', '/页面标题')
    点击(device, '/交易/普通交易/其他交易/其他交易首页/债券回购')
    点击(device, '/交易/普通交易/其他交易/其他交易首页/债券回购_融券回购')
    输入文本(device, '/交易/普通交易/其他交易/其他交易首页/债券回购_融资回购/输入证券代码', 证券代码)
    输入文本(device, '/交易/普通交易/其他交易/其他交易首页/债券回购_融资回购/输入交易数量', 交易数量)
    点击(device, '/确定/确定按钮')
    点击(device, '/底部确定/确定按钮')
    实际结果 = 获取文本信息(device, '/提示')
    结果 = 预期结果 in 实际结果
    错误校验点 = 非空校验(device, 预期结果, 实际结果)
    return 结果, 错误校验点


