# MinerU 升级指南：从 1.3.12 到 2.1.11

## 概述

本文档详细说明了将 `t.py` 文件从 magic_pdf 1.3.12 版本升级到 mineru 2.1.11 版本的完整过程和关键变化。

## 主要升级变化

### 1. 导入模块的重大变化

#### 旧版本 (1.3.12) 导入：
```python
from magic_pdf.config.enums import SupportedPdfParseMethod
from magic_pdf.data.data_reader_writer import FileBasedDataReader, FileBasedDataWriter
from magic_pdf.data.dataset import PymuDocDataset
from magic_pdf.data.read_api import read_local_images, read_local_office
from magic_pdf.model.doc_analyze_by_custom_model import doc_analyze
```

#### 新版本 (2.1.11) 导入：
```python
# 统一处理管道
from magic_pdf.pipe.UNIPipe import UNIPipe
from magic_pdf.pipe.OCRPipe import OCRPipe
from magic_pdf.pipe.TXTTipe import TXTTipe
import magic_pdf.model as model_config
from magic_pdf.libs.version import __version__ as mineru_version
```

### 2. 处理流程的简化

#### 旧版本的复杂流程：
```python
# 多步骤处理，需要手动管理各个组件
reader = FileBasedDataReader("")
pdf_bytes = reader.read(temp_pdf_path)
ds = PymuDocDataset(pdf_bytes)
is_ocr = ds.classify() == SupportedPdfParseMethod.OCR
infer_result = ds.apply(doc_analyze, ocr=is_ocr)
image_writer = FileBasedDataWriter(temp_image_dir)
pipe_result = infer_result.pipe_ocr_mode(image_writer) if is_ocr else infer_result.pipe_txt_mode(image_writer)
```

#### 新版本的统一管道：
```python
# 统一管道处理，自动管理所有组件
pipe = UNIPipe(
    pdf_file_path=str(pdf_file_path),
    output_dir=output_dir,
    work_dir=work_dir
)
pipe_result = pipe.pipe_analyze()
```