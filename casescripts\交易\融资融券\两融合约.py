from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('交易/融资融券/两融合约/两融合约')
def _728b8085ab39bd545ccaa2cf32cabb75(device, 账号='30002187', 交易密码='123123', 通信密码='123123'):
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/融资融券')
    上滑(device)
    点击(device, '/底部导航栏/首页界面跳转按钮')
    点击(device, '/底部导航栏/交易界面跳转按钮')
    点击(device, '/交易/融资融券/融资融券首页/两融合约')
    目录校验(device, '二', '我的合约', '/页面标题')
    登录前页面处理(device)
    实际结果 = 获取文本信息(device, '/交易/融资融券/两融合约/我的合约页面/合约总资产')
    print(实际结果)
    错误校验点 = 数字校验(device, 实际结果)
    结果 = 实际结果 != ''
    return 结果, 错误校验点


