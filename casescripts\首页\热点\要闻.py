from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('首页/热点/要闻/要闻')
def _c29ce63b1a4bfc09666b9b90acb94c79(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # 初始化
    登录前页面处理(device)
    点击(device, '/底部导航栏/首页界面跳转按钮')
    目录校验(device, '一', '首页', '/首页/选中状态')
    点击(device, '/首页/热点')
    目录校验(device, '二', '热点', '/首页/热点/选中状态')
    循环滑动(device, '/首页/热点/要闻/标题', '上')
    # 点击('/首页/热点/要闻/标题')
    sleep(3)
    实际结果 = 获取文本信息(device, '/首页/热点/要闻/第一条')
    # print(预期结果)
    #('/首页/热点/要闻/第一条')
    # time.sleep(2)
    # 实际结果 = 获取文本信息('/首页/热点/要闻/精选/标题')
    # print(实际结果)
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


