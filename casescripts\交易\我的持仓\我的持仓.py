from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('交易/我的持仓/我的持仓/我的持仓')
def _d24afc3312696f8ab4afaee43dbc726e(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    错误校验点 = ''
    # 真实账号
    # 初始化
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/普通交易')
    点击(device, '/交易/普通交易/普通交易首页/我的持仓')
    目录校验(device, '二', '持仓', '/交易/普通交易/持仓/选中状态')
    控件对象 = ''
    文本信息 = ''
    今日收益 = 获取文本信息(device, '/交易/普通交易/我的持仓/今日收益')
    总市值 = 获取文本信息(device, '/交易/普通交易/我的持仓/总市值')
    总盈亏 = 获取文本信息(device, '/交易/普通交易/我的持仓/总盈亏')
    结果 = (今日收益 != '' and 今日收益 != '--') and (总市值 != '' and 总市值 != '--') and (总盈亏 != '' and 总盈亏 != '--')
    # print('结果：', 结果)
    if 结果:
        try:
            文本信息 = 获取文本信息(device, '/交易/普通交易/普通交易首页/我的持仓/第一个股票/公司名称')
            结果 = 文本信息 != ''
        except:
            文本信息 = 获取控件对象(device, '/交易/普通交易/普通交易首页/我的持仓/暂无记录')
        错误校验点 = 为空校验(device)
    else:
        错误校验点 = 类型校验(device, float, '今日收益：%s，总市值：%s，总盈亏：%s' % (今日收益, 总市值, 总盈亏))
    return 结果, 错误校验点


