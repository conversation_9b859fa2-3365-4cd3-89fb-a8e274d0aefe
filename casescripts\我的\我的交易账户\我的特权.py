from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('我的/我的交易账户/我的特权/上海股东账户')
def _345d55c99a15392b9b1e6117c1a5d674(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    错误校验点 = ''
    # 初始化
    登录前页面处理(device)
    # 理财登陆(账号, 交易密码, 通信密码)
    交易登录(device, 账号, 交易密码, 通信密码)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    目录校验(device, '一', '我的', '/我的/选中状态')
    # time.sleep(3)
    # 上滑()
    循环滑动(device, '/我的/我的交易账户/我的特权', "上")
    点击(device, '/我的/我的交易账户/我的特权')
    目录校验(device, '二', '我的特权', '/页面标题')
    点击(device, '/我的/我的交易账户/我的特权/上海股东账户')
    if 获取控件对象(device, '/我的/我的交易账户/我的特权/上海股东账户/标题'):
        实际结果 = 获取文本信息(device, '/我的/我的交易账户/我的特权/上海股东账户/标题')
        预期结果 = '您已开立上海股东账户'
        错误校验点 = 非空校验(device, 预期结果, 实际结果)
        结果 = 预期结果 == 实际结果
    return 结果, 错误校验点


@casescript('我的/我的交易账户/我的特权/天天发_立即开始现金理财_进入天天发_历史明细')
def _23629202a479640bde5915f883d530c4(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # 初始化
    登录前页面处理(device)
    # 理财登陆(账号, 交易密码, 通信密码)
    交易登录(device, 账号, 交易密码, 通信密码)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    # time.sleep(3)
    # 上滑()
    目录校验(device, '一', '我的', '/我的/选中状态')
    # time.sleep(3)
    # 上滑()
    循环滑动(device, '/我的/我的交易账户/我的特权', "上")
    点击(device, '/我的/我的交易账户/我的特权')
    目录校验(device, '二', '我的特权', '/页面标题')
    上滑(device)
    点击(device, '/我的/我的交易账户/我的特权/天天发')
    点击(device, '/我的/我的交易账户/我的特权/天天发/立即开始按钮')
    点击(device, '/我的/我的交易账户/我的特权/天天发/立即开始按钮/进入天天发')
    sleep(3)
    点击(device, '/我的/我的交易账户/我的特权/天天发/立即开始按钮/进入天天发/查询')
    sleep(3)
    点击(device, '/我的/我的交易账户/我的特权/天天发/立即开始按钮/进入天天发/查询/历史明细')
    实际结果 = 获取文本信息(device, '/我的/我的交易账户/我的特权/天天发/立即开始按钮/进入天天发/查询/历史明细/第一条')
    print(实际结果)
    错误校验点 = 为空校验(device)
    结果 = 实际结果 != ''
    return 结果, 错误校验点


@casescript('我的/我的交易账户/我的特权/天天发_立即开始现金理财_进入天天发_当前委托')
def _1a5df4caa0360899601793c86eca5e02(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    错误校验点 = ''
    # 初始化
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    目录校验(device, '一', '我的', '/我的/选中状态')
    循环滑动(device, '/我的/我的交易账户/我的特权', "上")
    点击(device, '/我的/我的交易账户/我的特权')
    目录校验(device, '二', '我的特权', '/页面标题')
    上滑(device)
    点击(device, '/我的/我的交易账户/我的特权/天天发')
    点击(device, '/我的/我的交易账户/我的特权/天天发/立即开始按钮')
    点击(device, '/我的/我的交易账户/我的特权/天天发/立即开始按钮/进入天天发')
    结果 = False
    if 获取控件对象(device, '/登陆/系统提示'):
        错误校验点 = 获取文本信息(device, '/登陆/系统提示内容')
        截屏(device)
        点击(device, '/提示确认/确认按钮')
    点击(device, '/我的/我的交易账户/我的特权/天天发/立即开始按钮/进入天天发/查询')
    sleep(3)
    # 点击('/我的/我的交易账户/我的特权/天天发/立即开始按钮/进入天天发/查询/历史明细')
    点击(device, '/我的/我的交易账户/我的特权/天天发/立即开始按钮/进入天天发/查询/当前委托')
    if 获取控件对象(device, '/登陆/系统提示'):
        错误校验点 = 获取文本信息(device, '/登陆/系统提示内容')
        截屏(device)
        点击(device, '/提示确认/确认按钮')
    else:
        if 获取控件对象(device, '/交易/普通交易/普通交易首页/天天发/当前委托/第一条委托'):
            实际结果 = 获取文本信息(device, '/交易/普通交易/普通交易首页/天天发/当前委托/第一条委托')
        else:
            实际结果 = 获取控件对象(device, '/交易/普通交易/普通交易首页/天天发/当前委托')
        错误校验点 = 为空校验(device)
        结果 = 实际结果 != ''
    return 结果, 错误校验点


@casescript('我的/我的交易账户/我的特权/打新神器')
def _593c87be7e3efa4a91ff5ca691818d85(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    错误校验点 = ''
    # 初始化
    登录前页面处理(device)
    # 理财登陆(账号, 交易密码, 通信密码)
    交易登录(device, 账号, 交易密码, 通信密码)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    # time.sleep(3)
    # 上滑()
    目录校验(device, '一', '我的', '/我的/选中状态')
    # time.sleep(3)
    # 上滑()
    循环滑动(device, '/我的/我的交易账户/我的特权', "上")
    点击(device, '/我的/我的交易账户/我的特权')
    目录校验(device, '二', '我的特权', '/页面标题')
    点击(device, '/我的/我的交易账户/我的特权/打新神器')
    if 获取控件对象(device, '/我的/我的交易账户/我的特权/打新神器/标题'):
        实际结果 = 获取文本信息(device, '/我的/我的交易账户/我的特权/打新神器/标题')
        预期结果 = '打新神器'
        错误校验点 = 非空校验(device, 预期结果, 实际结果)
        结果 = 预期结果 == 实际结果
    return 结果, 错误校验点


@casescript('我的/我的交易账户/我的特权/柜台市场')
def _8e778848538848b00bd32b6e7e4422b8(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119', 预期结果='已开通'):
    # 初始化
    登录前页面处理(device)
    # 理财登陆(账号, 交易密码, 通信密码)
    交易登录(device, 账号, 交易密码, 通信密码)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    # time.sleep(3)
    # 上滑()
    目录校验(device, '一', '我的', '/我的/选中状态')
    # time.sleep(3)
    # 上滑()
    循环滑动(device, '/我的/我的交易账户/我的特权', "上")
    点击(device, '/我的/我的交易账户/我的特权')
    目录校验(device, '二', '我的特权', '/页面标题')
    点击(device, '/我的/我的交易账户/我的特权/柜台市场')
    是否开通 = 获取文本信息(device, '/首页/热点/我的资产/业务办理/柜台市场产品/是否开通')
    print('是否开通', 是否开通)
    实际结果 = ''
    结果 = 预期结果 in 是否开通
    if 结果:
        实际结果 = 是否开通
        点击(device, '/确定/确定按钮')
    else:
        点击(device, '/我的/我的交易账户/我的特权/柜台市场/免费一键开通')
        点击(device, '/我的/我的交易账户/我的特权/柜台市场/免费一键开通/我已知悉并同意')
        点击(device, '/底部确认/确认按钮')
        点击(device, '/我的/我的交易账户/我的特权/柜台市场/免费一键开通/点击将逐条阅读上述文本')
        sleep(2)
        点击(device, '/我的/我的交易账户/我的特权/柜台市场/免费一键开通/下一份')
        sleep(2)
        点击(device, '/我的/我的交易账户/我的特权/柜台市场/免费一键开通/下一份')
        sleep(2)
        点击(device, '/我的/我的交易账户/我的特权/柜台市场/免费一键开通/阅读完成')
        点击(device, '/我的/我的交易账户/我的特权/柜台市场/免费一键开通/我已同意')
        点击(device, '/我的/我的交易账户/我的特权/柜台市场/免费一键开通/确定')
        点击(device, '/我的/我的交易账户/我的特权/柜台市场/免费一键开通/确认签署')
        点击(device, '/首页/热点/我的资产/业务办理/柜台市场产品/一键开通/问卷调查/第一题')
        点击(device, '/首页/热点/我的资产/业务办理/柜台市场产品/一键开通/问卷调查/第二题')
        上滑(device)
        点击(device, '/首页/热点/我的资产/业务办理/柜台市场产品/一键开通/问卷调查/第三题')
        点击(device, '/首页/热点/我的资产/业务办理/柜台市场产品/一键开通/问卷调查/第四题')
        上滑(device)
        点击(device, '/首页/热点/我的资产/业务办理/柜台市场产品/一键开通/问卷调查/第五题')
        # 提交按钮点击无效
        点击(device, '/首页/热点/我的资产/业务办理/柜台市场产品/一键开通/问卷调查/提交')
        实际结果 = 获取文本信息(device, '/首页/热点/我的资产/业务办理/柜台市场产品/一键开通/提示信息/结果')
        结果 = 预期结果 in 实际结果
        点击(device, '/首页/热点/我的资产/业务办理/柜台市场产品/一键开通/提示信息/确定')
    错误校验点 = 非空校验(device, 预期结果, 实际结果)
    return 结果, 错误校验点


@casescript('我的/我的交易账户/我的特权/深圳股东账户')
def _cf809659fb8cbf127646ea1af804de87(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    错误校验点 = ''
    # 初始化
    登录前页面处理(device)
    # 理财登陆(账号, 交易密码, 通信密码)
    交易登录(device, 账号, 交易密码, 通信密码)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    # time.sleep(3)
    # 上滑()
    目录校验(device, '一', '我的', '/我的/选中状态')
    # time.sleep(3)
    # 上滑()
    循环滑动(device, '/我的/我的交易账户/我的特权', "上")
    点击(device, '/我的/我的交易账户/我的特权')
    目录校验(device, '二', '我的特权', '/页面标题')
    点击(device, '/我的/我的交易账户/我的特权/深圳股东账户')
    if 获取控件对象(device, '/我的/我的交易账户/我的特权/深圳股东账户/标题'):
        实际结果 = 获取文本信息(device, '/我的/我的交易账户/我的特权/深圳股东账户/标题')
        预期结果 = '您已开立深圳股东账户'
        错误校验点 = 非空校验(device, 预期结果, 实际结果)
    结果 = 预期结果 == 实际结果
    return 结果, 错误校验点


@casescript('我的/我的交易账户/我的特权/港股通')
def _640e08b9db8365ba28dcb6f09a76a674(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # 初始化
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    目录校验(device, '一', '我的', '/我的/选中状态')
    # time.sleep(3)
    # 上滑()
    循环滑动(device, '/我的/我的交易账户/我的特权', "上")
    点击(device, '/我的/我的交易账户/我的特权')
    目录校验(device, '二', '我的特权', '/页面标题')
    点击(device, '/我的/我的交易账户/我的特权/港股通')
    实际结果 = 获取文本信息(device, '/页面标题')
    错误校验点 = 为空校验(device)
    结果 = 实际结果 != ''
    return 结果, 错误校验点


@casescript('我的/我的交易账户/我的特权/融资融券')
def _c2df921312e27778940d53e557294fb8(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119', 预期结果='暂不支持线上开通'):
    错误校验点 = ''
    # 初始化
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    目录校验(device, '一', '我的', '/我的/选中状态')
    # time.sleep(3)
    # 上滑()
    循环滑动(device, '/我的/我的交易账户/我的特权', "上")
    点击(device, '/我的/我的交易账户/我的特权')
    目录校验(device, '二', '我的特权', '/页面标题')
    点击(device, '/我的/我的交易账户/我的特权/融资融券')
    点击(device, '/我的/我的交易账户/我的特权/融资融券/开通确定')
    提示 = 获取文本信息(device, '/我的/我的交易账户/我的特权/融资融券/开通/提示/信息')
    结果 = 预期结果 in 提示
    if not 结果:
        错误校验点 = 非空校验(device, 预期结果, 提示)
    点击(device, '/我的/我的交易账户/我的特权/融资融券/开通/提示/知道了')
    return 结果, 错误校验点


