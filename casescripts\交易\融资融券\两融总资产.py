from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('交易/融资融券/两融总资产/两融总资产')
def _ea7af562e94de88e84739f6abdaace83(device, 账号='30002187', 交易密码='123123', 通信密码='123123'):
    # time.sleep(15)
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/融资融券')
    点击(device, '/交易/融资融券/融资融券首页/两融总资产')
    目录校验(device, '二', '两融-资产', '/页面标题')
    实际结果 = 获取文本信息(device, '/交易/融资融券/两融总资产/两融总资产页面/总资产')
    print(实际结果)
    错误校验点 = 为空校验(device)
    结果 = 实际结果 != ''
    return 结果, 错误校验点


