import asyncio
import json
import subprocess
import re
import os
import logging
import threading
from decimal import Decimal, ROUND_HALF_UP, ROUND_CEILING
import datetime
import queue
import time


def null_to_empty(value):
    return "" if value is None else value

def get_battery(battery_info):
    pattern = r'\d+'

    match = re.search(pattern, battery_info)
    if match:
        number = match.group()
        return number
    else:
        print("No number found")

def get_top(top_info):
    res_map = {}
    # 替换top命令返回结果中的特殊字符
    top_info = re.sub(r'\x1B\[[0-?]*[ -/]*[@-~]', '', top_info)

    header_list = []

    if "PID" not in top_info:
        logging.warning(f"未获取到正确的TOP命令返回内容: {top_info}")
        return res_map

    rows = top_info[top_info.index("PID"):].split('\n')
    for i, row in enumerate(rows):
        if not row.strip():
            continue
        cells = row.split()
        k = 0
        for cell in cells:
            if not cell.strip():
                continue
            # 第一行为列头
            if i == 0:
                # 拆分状态和cpu信息，top返回结果为S[%CPU]
                if "S[%" in cell:
                    header_list.append("STATUS")
                    header_list.append("CPU")
                elif cell.lower() == "%mem":
                    header_list.append("mem")
                else:
                    header_list.append(cell)
            else:
                res_map[header_list[k].lower()] = cell
                k += 1

    return res_map

def get_fps_info(fps_info):
    is_draw = False
    normal_time = Decimal('16.67')
    avg_frame2_time = Decimal('84')
    avg_frame3_time = Decimal('125')
    frame_count = 0
    vsync_count = 0
    current_frame_count = 0
    jank = 0
    big_jank = 0
    frame_time_map = {}

    for line in fps_info.split('\n'):
        if line.strip().startswith("Draw"):
            is_draw = True
            continue
        elif line.strip().startswith("Panel:") or line.strip().startswith("View hierarchy"):
            break
        if not is_draw:
            continue
        if not line.strip():
            continue

        is_number = True
        total_time = Decimal('0')
        for cell in line.split('\t'):
            if not cell.strip():
                continue
            try:
                cell_value = Decimal(cell)
            except:
                is_number = False
                break
            total_time += cell_value

        if not is_number:
            continue

        current_frame_count += 1
        if current_frame_count == 1:
            frame_time_map['frame1'] = total_time
        elif current_frame_count == 2:
            frame_time_map['frame2'] = total_time
        elif current_frame_count == 3:
            frame_time_map['frame3'] = total_time
        elif current_frame_count >= 4:
            frame1_time = frame_time_map['frame1']
            frame2_time = frame_time_map['frame2']
            frame3_time = frame_time_map['frame3']
            avg_frame_time = (frame1_time + frame2_time + frame3_time) / Decimal('3')

            frame_time_map['frame1'] = frame2_time
            frame_time_map['frame2'] = frame3_time
            frame_time_map['frame3'] = total_time

            if total_time > avg_frame_time:
                if total_time > avg_frame2_time:
                    jank += 1
                if total_time > avg_frame3_time:
                    big_jank += 1

        frame_count += 1
        if total_time > normal_time:
            vsync_count += (total_time / normal_time).quantize(Decimal('1.'), rounding=ROUND_CEILING) - 1

    res_map = {}
    if frame_count == 0:
        res_map['fps'] = '60'
    else:
        res_map['fps'] = str(frame_count * 60 // (frame_count + vsync_count))
    res_map['jank'] = str(jank)
    res_map['big_jank'] = str(big_jank)
    return res_map


def get_mem_info(mem_info):
    res_map = {}
    if not mem_info:
        logging.warning(f"未获取到正确的内存信息返回内容: {mem_info}")
        return res_map

    # 替换命令返回结果中的特殊字符
    mem_info = re.sub(r'\x1B\[[0-?]*[ -/]*[@-~]', '', mem_info)

    rows = mem_info.split('\n')
    for i, row in enumerate(rows):
        if not row.strip():
            continue
        if i == 0:
            cells = row.split()
            for cell in cells:
                if not cell.strip():
                    continue
                if cell.upper() == 'TOTAL':
                    continue
                # 取第二个值为内存占用大小，单位为kb换算为mb
                mem_size = Decimal(cell) / Decimal('1024')
                res_map['mem_size'] = str(mem_size.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP))
                break
            break

    return res_map

def get_net(net_info):
    res_map = {}
    # 替换返回结果中的特殊字符
    net_info = re.sub(r'\x1B\[[0-?]*[ -/]*[@-~]', '', net_info)

    rows = net_info.split('\n')
    for row in rows:
        if not row.strip():
            continue
        # 只处理wlan0信息
        if 'wlan0' in row:
            cells = row.split()
            k = 0
            for cell in cells:
                if not cell.strip():
                    continue
                if k == 1:
                    # 接收字节总数
                    res_map['receive_bytes'] = cell
                elif k == 2:
                    # 接收数据包总数
                    res_map['receive_packets'] = cell
                elif k == 9:
                    # 发送字节总数
                    res_map['transmit_bytes'] = cell
                elif k == 10:
                    # 发送数据包总数
                    res_map['transmit_packets'] = cell
                    break
                k += 1
            break

    return res_map

# def getPid(adb_path, device_id, appname):
#     # 根据应用包名获取应用pid
#     # appname = 'com.changan.uni'
#     get_pid = f"{adb_path}adb -s {device_id} shell ps | grep {appname}"
#     # pid = asyncio.create_task(run_command(f"{adb_path}adb -s {device_id} shell ps | grep {appname}))
#     # pid = asyncio.create_task(run_command(f"{adb_path}adb -s {device_id} shell ps | grep {appname}"))
#     get_pid_info = subprocess.run(get_pid, shell=True, check=True, capture_output=True, text=True)
#     output_lines = get_pid_info.stdout.strip().split('\n')
#     pids = [line.split()[1] for line in output_lines]
#     pid = pids[0] if pids else None
#     return pid
def getPid(adb_path, device_id, appname):
    # 根据应用包名获取应用pid
    get_pid_cmd = f"{adb_path}adb -s {device_id} shell ps | grep {appname}"

    try:
        get_pid_info = subprocess.run(get_pid_cmd, shell=True, check=False, capture_output=True, text=True)

        # 如果 grep 没有找到匹配项，返回码通常是 1
        if get_pid_info.returncode != 0:
            return None

        output_lines = get_pid_info.stdout.strip().split('\n')
        pids = [line.split()[1] for line in output_lines if line.strip()]

        return pids[0] if pids else None

    except subprocess.CalledProcessError:
        # 如果命令执行失败，返回 None
        return None
    except Exception as e:
        print(f"Error occurred while getting PID: {e}")
        return None

async def get_app_info(adb_path, device_id, pid):
    # 根据应用包名获取应用pid
    # appname = 'com.changan.uni'
    # get_pid = f"{adb_path}adb -s {device_id} shell ps | grep {appname}"
    # # pid = asyncio.create_task(run_command(f"{adb_path}adb -s {device_id} shell ps | grep {appname}))
    # # pid = asyncio.create_task(run_command(f"{adb_path}adb -s {device_id} shell ps | grep {appname}"))
    # get_pid_info = subprocess.run(get_pid, shell=True, check=True, capture_output=True, text=True)
    # output_lines = get_pid_info.stdout.strip().split('\n')
    # pids = [line.split()[1] for line in output_lines]
    # pid = pids[0] if pids else None
    # print(first_pid)
    # 获取app的cpu和内存等信息
    # pid = getPid(adb_path, device_id, appname)
    performance = {}
    # 当前时间戳和设备id
    timestamp_ms = int(time.time())*1000
    # current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
    performance['current_time'] = timestamp_ms
    performance['device_id'] = device_id
    adb_top = asyncio.create_task(run_command(f"{adb_path}adb -s {device_id} shell top -p {pid} -n 1"))

    # 获取内存占用信息
    adb_mem = asyncio.create_task(run_command(f"{adb_path}adb -s {device_id} shell dumpsys meminfo {pid} | grep TOTAL"))

    # 获取app的线程数
    adb_thread = asyncio.create_task(run_command(f"{adb_path}adb -s {device_id} shell ps -T -p {pid} | wc -l"))

    # 获取fps信息
    adb_fps = asyncio.create_task(run_command(f"{adb_path}adb -s {device_id} shell dumpsys gfxinfo {pid}"))

    # 获取网络流量信息
    adb_net = asyncio.create_task(run_command(f"{adb_path}adb -s {device_id} shell cat /proc/{pid}/net/dev"))

    # 获取电池电量
    adb_battery = asyncio.create_task(run_command(f"{adb_path}adb -s {device_id} shell dumpsys battery | grep temperature"))

    # 等待所有任务完成
    # results = await asyncio.gather(adb_top, adb_mem, adb_thread, adb_fps, adb_net)
    res_top, res_mem, res_thread, res_fps, res_net, res_battery = await asyncio.gather(
        adb_top, adb_mem, adb_thread, adb_fps, adb_net, adb_battery
    )

    # 处理top信息
    top = get_top(res_top)
    cpu = top.get('cpu', '')
    if not cpu:
        cpu = '0'
    cpu_data = {}
    # cpu_data['symbol'] = '14'
    cpu_data['cpu_value'] = cpu
    cpu_data['value_text'] = cpu
    performance['cpu'] = cpu
    # print(cpu_data)

    # 处理mem信息
    mem = top.get('mem', '')
    if not mem:
        mem = '0'
    mem_data = {}
    mem_data['mem_value'] = mem
    mem_data['value_text'] = mem
    performance['mem'] = mem
    # print(mem_data)
    # return results

    # 构造 threads 数据
    threads = res_thread.strip() if res_thread else ''
    if not threads or threads.startswith("error:"):
        threads = "0"
    thread_data = {}
    # thread_data['symbol'] = '16'
    thread_data['threads_value'] = threads
    thread_data['value_text'] = threads
    performance['threads'] = threads
    # print(thread_data)

    # 构造 fps 数据
    fps_info_map = get_fps_info(res_fps)
    fps = fps_info_map.get("fps", "")
    if not fps or fps.startswith("error:"):
        fps = "0"
    fps_data = {}
    # fps_data["symbol"] = "17"
    fps_data["fps_value"] = fps
    fps_data["value_text"] = fps
    performance['fps'] = fps
    # print(fps_data)

    # 构造jank数据
    jank = fps_info_map.get("jank")
    if not jank or jank.startswith("error:"):
        jank = "0"
    jank_data = {}
    # jank_data["symbol"] = "22"
    jank_data["jank_value"] = jank
    jank_data["value_text"] = jank
    performance['jank'] = jank
    # print(jank_data)

    # 构造bigJank数据
    big_jank = fps_info_map.get("big_jank")
    if not big_jank or big_jank.startswith("error:"):
        big_jank = "0"
    big_jank_data = {}
    # jank_data["symbol"] = "23"
    big_jank_data["big_jank_value"] = big_jank
    big_jank_data["value_text"] = big_jank
    performance['big_jank'] = big_jank
    # print(big_jank_data)

    # 构造 jank_rate 数据
    jank_rate_data = {}
    jank_rate = "0"
    # 计算卡帧率 jank / fps * 100
    if jank != "0" and fps != "0":
        jank_decimal = Decimal(jank)
        fps_decimal = Decimal(fps)
        jank_rate = str((jank_decimal / fps_decimal).quantize(Decimal('0.0001'), rounding=ROUND_HALF_UP) * Decimal("100"))
    # jank_rate_data["symbol"] = "24"
    jank_rate_data["jank_rate_value"] = jank_rate
    jank_rate_data["value_text"] = jank_rate
    performance['jank_rate'] = jank_rate
    # print(jank_rate_data)

    # 构造 mem_info_data 数据
    mem_info_data = get_mem_info(res_mem)  # 假设get_mem_info是一个已定义的函数
    mem_size = mem_info_data.get("mem_size", "0")
    mem_size_data = {}
    # mem_size_data["symbol"] = "25"
    mem_size_data["mem_size_value"] = mem_size
    mem_size_data["value_text"] = mem_size
    performance['mem_size'] = mem_size
    # print(mem_size_data)

    # 构造网络流量数据
    receive_bytes = "0"
    receive_packets = "0"
    transmit_bytes = "0"
    transmit_packets = "0"
    net = get_net(res_net)
    if net:
        # 构造 receive_bytes 数据
        receive_bytes = null_to_empty(net.get("receive_bytes"))
        if not receive_bytes:
            receive_bytes = "0"

        # 构造 receive_packets 数据
        receive_packets = null_to_empty(net.get("receive_packets"))
        if not receive_packets:
            receive_packets = "0"

        # 构造 transmit_bytes 数据
        transmit_bytes = null_to_empty(net.get("transmit_bytes"))
        if not transmit_bytes:
            transmit_bytes = "0"

        # 构造 transmit_packets 数据
        transmit_packets = null_to_empty(net.get("transmit_packets"))
        if not transmit_packets:
            transmit_packets = "0"

    # 构造 receive_bytes 数据
    receive_bytes_data = {}
    # receive_bytes_data["symbol"] = "18"
    receive_bytes_data["receive_bytes_value"] = receive_bytes
    receive_bytes_data["value_text"] = receive_bytes
    performance['receive_bytes'] = receive_bytes
    # print(receive_bytes_data)

    # performance_list.append(receive_bytes_data)

    # 构造 receive_packets 数据
    receive_packets_data = {}
    # receive_packets_data["symbol"] = "19"
    receive_packets_data["receive_packets_value"] = receive_packets
    receive_packets_data["value_text"] = receive_packets
    performance['receive_packets'] = receive_packets
    # print(receive_packets_data)
    # performance_list.append(receive_packets_data)

    # 构造 transmit_bytes 数据
    transmit_bytes_data = {}
    # transmit_bytes_data["symbol"] = "20"
    transmit_bytes_data["transmit_bytes_value"] = transmit_bytes
    transmit_bytes_data["value_text"] = transmit_bytes
    performance['transmit_bytes'] = transmit_bytes
    # print(transmit_bytes_data)

    # performance_list.append(transmit_bytes_data)

    #电池电量信息
    battery = get_battery(res_battery)
    performance['battery'] = battery
    print(battery)

    # 构造 transmit_packets 数据
    transmit_packets_data = {}
    # transmit_packets_data["symbol"] = "21"
    transmit_packets_data["transmit_packets_value"] = transmit_packets
    transmit_packets_data["value_text"] = transmit_packets
    performance['transmit_packets'] = transmit_packets
    json_data = json.dumps(performance, indent=2)
    # print(json_data)
    return json_data
    # print(transmit_packets_data)

    # performance_list.append(transmit_packets_data)




async def run_command(command):
    process = await asyncio.create_subprocess_shell(
        command,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE
    )
    stdout, stderr = await process.communicate()
    return stdout.decode().strip()




#多个手机同时跑保存文件
async def device_main(adb_path, device_id, appname, base_path):
    last_output_time = datetime.now()
    buffer = []

    while True:
        start_time = datetime.now()

        pid = getPid(adb_path, device_id, appname)
        print('deviceId:'+str(device_id)+'pid:'+str(pid))
        if pid:
            results = await get_app_info(adb_path, device_id, pid)
            if results:
                # temp_result = "{} 输出的性能数据为:{}".format(datetime.now(), results)
                buffer.append(results)
                last_output_time = datetime.now()

        current_time = datetime.now()
        time_since_last_output = (current_time - last_output_time).total_seconds()

        if time_since_last_output > 2 and buffer:
            save_path = os.path.join(
                base_path,
                current_time.strftime("%Y"),
                current_time.strftime("%m"),
                current_time.strftime("%d"),
                device_id
            )
            os.makedirs(save_path, exist_ok=True)

            filename = f"{int(current_time.timestamp() * 1000)}.txt"
            full_path = os.path.join(save_path, filename)

            with open(full_path, 'w') as f:
                f.write(str(buffer))
            buffer.clear()

        end_time = datetime.now()
        execution_time = (end_time - start_time).total_seconds()

        wait_time = 1 - execution_time
        if wait_time > 0:
            await asyncio.sleep(wait_time)

def run_device_main(adb_path, device_id, appname, base_path):
    asyncio.run(device_main(adb_path, device_id, appname, base_path))

def main_multi_devices():
    adb_path = "/home/<USER>/android-sdk/platform-tools/"
    appname = 'com.changan.uni'
    base_path = "/home/<USER>/src/ControlSystem/log/performance"

    print('正在运行')
    # 设备列表
    devices = [
        "s8wwdawkpvzli77x",
        "bivoskr4izh6aiuo",
        # 添加更多设备 ID
    ]

    threads = []
    for device_id in devices:
        thread = threading.Thread(target=run_device_main, args=(adb_path, device_id, appname, base_path))
        threads.append(thread)
        thread.start()

    # 等待所有线程完成
    for thread in threads:
        thread.join()

if __name__ == "__main__":
    main_multi_devices()