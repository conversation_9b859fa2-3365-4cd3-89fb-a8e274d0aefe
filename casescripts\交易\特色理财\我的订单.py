from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('交易/特色理财/我的订单/历史委托')
def _a053a854ec6e0f133a7e885d181a60ad(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # 初始化
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    滑动控件至屏幕内(device, '/交易/普通交易/普通交易首页/特色理财')
    点击(device, '/交易/普通交易/普通交易首页/特色理财')
    目录校验(device, '二', '特色理财', '/页面标题')
    sleep(3)
    点击(device, '/交易/普通交易/特色理财/特色理财首页/我的订单')
    sleep(2)
    点击(device, '/交易/普通交易/特色理财/我的订单/历史委托页面')
    登录前页面处理(device)
    实际结果 = 获取文本信息(device, '/交易/普通交易/特色理财/我的订单/历史委托页面/无记录')
    错误校验点 = 为空校验(device)
    结果 = 实际结果 != ''
    return 结果, 错误校验点


@casescript('交易/特色理财/我的订单/当日委托')
def _baba9c15fdd90da70c58d8e0d2e1e659(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # print(1)
    sleep(5)
    # 初始化
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    滑动控件至屏幕内(device, '/交易/普通交易/普通交易首页/特色理财')
    点击(device, '/交易/普通交易/普通交易首页/特色理财')
    目录校验(device, '二', '特色理财', '/页面标题')
    点击(device, '/交易/普通交易/特色理财/特色理财首页/我的订单')
    实际结果 = 获取文本信息(device, '/交易/普通交易/特色理财/我的订单/无记录')
    错误校验点 = 为空校验(device)
    结果 = 实际结果 != ''
    return 结果, 错误校验点


