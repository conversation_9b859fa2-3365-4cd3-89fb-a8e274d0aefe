from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('交易/华泰理财产品/天天财/天天财累计收益')
def _5eeb7de5bf76ff3139a9fb0de60e7fec(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119', 证券代码='600123', 交易数量='100'):
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    上滑(device)
    点击(device, '/交易/普通交易/普通交易首页/天天财')
    返回(device)
    点击(device, '/交易/普通交易/普通交易首页/天天财')
    目录校验(device, '二', '我的天天财', '/页面标题')
    sleep(5)
    点击(device, '/交易/普通交易/天天财/历史累计收益详情')
    实际结果 = 获取文本信息(device, '/交易/普通交易/华泰理财产品/天天财/历史累计/已实现收益')
    print(实际结果)
    错误校验点 = 为空校验(device)
    结果 = 实际结果 != ''
    return 结果, 错误校验点


@casescript('交易/华泰理财产品/天天财/我的委托_历史委托')
def _2bb3448180f13fb969156158c56c919d(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    上滑(device)
    点击(device, '/交易/普通交易/普通交易首页/天天财')
    返回(device)
    点击(device, '/交易/普通交易/普通交易首页/天天财')
    目录校验(device, '二', '我的天天财', '/页面标题')
    sleep(6)
    点击(device, '/交易/普通交易/普通交易首页/天天财/查看我的委托')
    sleep(3)
    点击(device, '/交易/普通交易/普通交易首页/天天财/查看我的委托/历史委托')
    sleep(3)
    try:
        实际结果 = 获取文本信息(device, '/交易/普通交易/普通交易首页/天天财/查看我的委托/历史委托/暂无委托')
    except:
        # 实际结果 = 获取控件对象('/交易/普通交易/普通交易首页/天天财/查看我的委托/历史委托/第一条记录')
        实际结果 = 获取文本信息(device, '/交易/普通交易/天天财/查看我的委托/历史委托/第一条/时间')
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


@casescript('交易/华泰理财产品/天天财/我的委托_当日委托')
def _e0f32c714d96bb4cd1aa710a8fa8e9ae(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119', 预期结果='暂无委托'):
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    上滑(device)
    点击(device, '/交易/普通交易/普通交易首页/天天财')
    返回(device)
    点击(device, '/交易/普通交易/普通交易首页/天天财')
    目录校验(device, '二', '我的天天财', '/页面标题')
    sleep(6)
    点击(device, '/交易/普通交易/普通交易首页/天天财/查看我的委托')
    sleep(3)
    点击(device, '/交易/普通交易/普通交易首页/天天财/查看我的委托/当日委托')
    实际结果 = 获取文本信息(device, '/交易/普通交易/普通交易首页/天天财/查看我的委托/历史委托/暂无委托')
    错误校验点 = 非空校验(device, 预期结果, 实际结果)
    结果 = 预期结果 == 实际结果
    return 结果, 错误校验点


@casescript('交易/华泰理财产品/天天财/我要抢购今日份额_短期理财服务')
def _1b8ac0b74cf983d437c75c4738218f0f(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119', 购买份额='3000'):
    错误校验点 = ''
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    上滑(device)
    点击(device, '/交易/普通交易/普通交易首页/天天财')
    返回(device)
    点击(device, '/交易/普通交易/普通交易首页/天天财')
    目录校验(device, '二', '我的天天财', '/页面标题')
    sleep(2)
    点击(device, '/交易/普通交易/天天财/我要抢购今日份额')
    try:
        sleep(2)
        点击(device, '/交易/普通交易/天天财/抢购按钮')
        点击(device, '/交易/普通交易/天天财/立即购买')
        sleep(3)
        剩余份数1 = 获取文本信息(device, '/交易/普通交易/天天财/剩余份数')
        输入文本(device, '/交易/普通交易/天天财/输入购买份额', 购买份额)
        点击(device, '/交易/普通交易/天天财/确定购买')
        点击(device, '/交易/普通交易/天天财/确认书同意按钮')
        点击(device, '/交易/普通交易/天天财/投资确认')
        点击(device, '/交易/普通交易/天天财/最终确认')
        实际结果 = 获取文本信息(device, '/交易/普通交易/天天财/信息提示')
        print(实际结果)
        预期结果 = '成功'
        结果 = 预期结果 in 实际结果
        错误校验点 = 非空校验(device, 预期结果, 实际结果)
        if 结果:
            返回(device)
            返回(device)
            剩余份数2 = 获取文本信息(device, '/交易/普通交易/天天财/剩余份数')
            结果 = 剩余份数2<剩余份数1
            print('之前数量为%s,之后数量为%s' % (剩余份数1, 剩余份数2))
            错误校验点 = '之前数量为%s,之后数量为%s' % (剩余份数1, 剩余份数2)
        # 点击('/交易/普通交易/天天财/信息确认')
    except:
        实际结果 = 获取文本信息(device, '/交易/普通交易/天天财/抢购按钮/抢购已结束')
        print(实际结果)
        预期结果 = '抢购'
        结果 = 预期结果 in 实际结果
        错误校验点 = 非空校验(device, 预期结果, 实际结果)
    return 结果, 错误校验点


