from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('我的/我的交易账户/我的账单/我的账单')
def _a450cecfc48ef3d32d1b6d8297ecc0da(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # 初始化
    登录前页面处理(device)
    # 理财登陆(账号, 交易密码, 通信密码)
    交易登录(device, 账号, 交易密码, 通信密码)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    目录校验(device, '一', '我的', '/我的/选中状态')
    滑动控件至屏幕内(device, '/我的/我的交易账户/我的账单')
    点击(device, '/我的/我的交易账户/我的账单')
    循环滑动(device, '/我的/我的交易账户/我的账单/我的完整账单', "上")
    点击(device, '/我的/我的交易账户/我的账单/我的完整账单')
    目录校验(device, '二', '我的账单', '/页面标题')
    实际结果 = 获取文本信息(device, '/我的/我的交易账户/我的账单/我的完整账单/总收益')
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


