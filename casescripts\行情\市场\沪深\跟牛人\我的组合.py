from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('行情/市场/沪深/跟牛人/我的组合/我的组合')
def _1028db55d7ef95a1d7ca08e2c4f89337(device):
    登录前页面处理(device)
    点击(device, '/底部导航栏/行情界面跳转按钮')
    目录校验(device, '一', '行情', '/行情/选中状态')
    点击(device, '/行情/市场/市场标题栏')
    目录校验(device, '二', '沪深', '/行情/更多/选中状态')
    滑动控件至屏幕内(device, '/行情/市场/沪深/跟牛人/进入牛人榜列表')
    点击(device, '/行情/市场/沪深/跟牛人/进入牛人榜列表')
    sleep(5)
    点击(device, '/行情/市场/沪深/跟牛人_常胜榜/我的组合标签')
    预期结果 = ''
    实际结果 = ''
    if 获取控件对象(device, '/登录标题') != '':
        预期结果 = '登录'
        实际结果 = 获取文本信息(device, '/登录标题')
    else:
        预期结果 = '我的组合'
        实际结果 = 获取文本信息(device, '/交易/普通交易/公用页面/适当性确认书页面/产品名称值')
    结果 = 预期结果 in 实际结果
    错误校验点 = 非空校验(device, 预期结果, 实际结果)
    return 结果, 错误校验点


