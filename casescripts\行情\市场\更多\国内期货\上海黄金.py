from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('行情/市场/更多/国内期货/上海黄金/获取首只黄金合约品种名称')
def _1f8512859771be0f4a2203e056ebb56b(device):
    登录前页面处理(device)
    点击(device, '/底部导航栏/行情界面跳转按钮')
    目录校验(device, '一', '行情', '/行情/选中状态')
    点击(device, '/行情/市场/市场标题栏')
    控件左滑(device, '/行情/市场/沪深按钮标签')
    点击(device, '/行情/市场/更多/更多标签')
    目录校验(device, '二', '更多', '/行情/更多/选中状态')
    滑动控件至屏幕内(device, '/行情/市场/更多/国内期货/上海黄金/上海黄金标签')
    点击(device, '/行情/市场/更多/国内期货/上海黄金/上海黄金标签')
    获取首只黄金合约品种名称 = 获取文本信息(device, '/行情/市场/更多/国内期货/上海黄金/获取首只黄金合约品种名称')
    结果 = 获取首只黄金合约品种名称 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


