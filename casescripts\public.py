import os
import sys
import random
import re
import time
import datetime
import subprocess
import signal

from selenium.common.exceptions import NoSuchElementException

from libs.adb import LocalDevice, CanNotLogin, TestCaseFail, NotEnterError
from libs.driver import ElementNotExist
from libs.sdk import hide_error, ai_server_url, MARK_SCREEN, slider_url, current_index_url, current_market_url, \
    index_info_url, ws_index_url, isWin, total_info_url, cloud_api, log_join, API_URL, video_compu
from pandas import DataFrame
import socket
from functools import wraps
from selenium.webdriver.common.by import By


def get_domain_by_re(u):
    d = re.search(r"(?<=http[s]://)[.\w-]*(:\d{,8})?((?=/)|(?!/))", u).group()
    return d


def get_ip_list(domain):
    ip_list = []
    try:
        addrs = socket.getaddrinfo(domain, None)
        for item in addrs:
            if item[4][0] not in ip_list:
                ip_list.append(item[4][0])
    except Exception as e:
        pass
    return ip_list


def unify_ocr_result(result, model_type):
    unified_result = []
    if model_type == "ocr_system":
        for item in result:
            unified_result.append({
                "text": item["text"],
                "confidence": item["confidence"],
                "text_region": item["text_region"]
            })
    elif model_type == "chinese_ocr_db_crnn_server":
        for item in result["data"]:
            unified_result.append({
                "text": item["text"],
                "confidence": item["confidence"],
                "text_region": item["text_box_position"]
            })
    return unified_result


def kill_child_processes(parent_pid, sig=signal.SIGTERM):
    import psutil
    """给定父进程id，杀掉该进程的所有子进程"""
    try:
        parent = psutil.Process(parent_pid)
    except psutil.NoSuchProcess:
        print(f"Parent process with PID {parent_pid} does not exist.")
        return

    children = parent.children(recursive=True)
    for process in children:
        process.send_signal(sig)


def 步骤录屏(device, duration=10):
    # 使用 ADB 录制屏幕
    file_name = f"screen_record_temp.mp4"
    _shell = f"adb -s {device.device_id} shell screenrecord --time-limit {duration} /sdcard/{file_name}"
    device.log.info(_shell)
    record_proces = subprocess.Popen(_shell, shell=True, stdin=subprocess.PIPE, stdout=subprocess.PIPE)
    time.sleep(1)
    device.record_proces = record_proces
    return file_name


def 停止步骤录屏(device, step_name):
    if device.record_proces:
        kill_child_processes(device.record_proces.pid)
        time.sleep(1)
    _t = time.strftime('%Y/%m/%d')
    dir_path = 'log/video/%s/%s/%s' % (_t, device.device_id.replace(':', ''), step_name)
    if not os.path.exists(dir_path):
        os.makedirs(dir_path)

    local_name = dir_path + '/' + device.result_id + ".mp4"
    file_name = f"screen_record_temp.mp4"
    # 将录制文件从设备拉到本地
    _shell = f"adb -s {device.device_id} pull /sdcard/{file_name} {local_name}"
    device.log.info(_shell)
    subprocess.run(_shell, shell=True)
    # 删除设备上的文件
    subprocess.run(f"adb -s {device.device_id} shell rm /sdcard/{file_name}", shell=True)


def 定位应用(device, ctx):
    if video_compu == 'true':
        import base64
        import os
        import requests
        import json
        import time
        try:
            import easyocr
        except Exception as e:
            return e

        subprocess.run(f"adb -s {device.device_id} shell input keyevent KEYCODE_HOME", shell=True)

        def cv2_to_base64(image):
            return base64.b64encode(image).decode('utf8')

        def isRequestApi(filename):
            try:
                # gpu服务器
                headers = {"Content-type": "application/json"}
                fo = open(filename, 'rb')
                img = fo.read()
                if img is None:
                    print("error in loading image:{}".format(filename))
                    return False
                server_url = ai_server_url
                data = {'images': [cv2_to_base64(img)]}
                fo.close()
                ai_response = requests.post(url=server_url, headers=headers, data=json.dumps(data))
                if str(ai_response.status_code) == '200':
                    result = ai_response.json()["results"][0]
                    return result
                return False
            except:
                try:
                    import easyocr
                    reader = easyocr.Reader(['ch_sim', 'en'])
                    result = reader.readtext(filename)
                    return result
                except Exception as e:
                    raise e

        filename = 'pictures/3060_ocr/' + str(int((time.time() * 10000))) + ".png"
        device.appium.driver.save_screenshot(filename)
        result = isRequestApi(filename)
        if os.path.exists(filename):
            os.remove(filename)
        for res in result:
            if 'text' in res:
                if ctx == res['text']:
                    x0 = res['text_region'][0][0]
                    y0 = res['text_region'][0][1]

                    x_t = (res['text_region'][1][0] - res['text_region'][0][0]) / 2
                    y_t = (res['text_region'][3][1] - res['text_region'][0][1]) / 2

                    x = x0 + x_t
                    y = y0 + y_t
                    device.log.info('app坐标(%s,%s)' % (x, y))
                    return x, y
            else:
                if ctx == res[1]:
                    x0 = res[0][0][0]
                    y0 = res[0][0][1]

                    x_t = (res[0][1][0] - res[0][0][0]) / 2
                    y_t = (res[0][3][1] - res[0][0][1]) / 2

                    x = x0 + x_t
                    y = y0 + y_t
                    device.log.info('app坐标(%s,%s)' % (x, y))
                    # device.appium.driver.tap([(x, y)])
                    return x, y
        device.log.info('未找到app坐标(%s,%s)' % (0, 0))
        return 0, 0
    else:
        return 0, 0


def 坐标打开应用(device, x, y):
    if video_compu == 'true':
        device.log.info('点击app坐标(%s,%s)' % (x, y))
        device.appium.driver.tap([(x, y)])


def 域名解析(func):
    @wraps(func)
    def wrapper(*args, domain_url='', **kwargs):
        if args[0] and type(args[0]).__name__ == 'LocalDevice' and domain_url != '' and 'tips' in kwargs.keys():
            args[0].log.info('【%s】访问地址路径<%s>。' % (kwargs['tips'], domain_url))
            args[0].log.info('【%s】访问域名<%s>。' % (kwargs['tips'], get_domain_by_re(domain_url)))
            args[0].log.info('【%s】解析ip<%s>。' % (kwargs['tips'], str(get_ip_list(get_domain_by_re(domain_url)))))
            if isWin:
                sh = args[0].shell('"ping -c 3 %s|findStr rtt"' % get_domain_by_re(domain_url))
            else:
                sh = args[0].shell('"ping -c 3 %s|grep rtt"' % get_domain_by_re(domain_url))
            args[0].log.info('网络检测：%s' % sh)

        return func(*args, **kwargs)

    return wrapper


def 打新列表(device):
    import tushare as ts
    try:
        import requests, json
        data_url = 'http://*************:8010/get_new_stock'
        r = requests.get(data_url)
        res = json.loads(r.text)
        if 'data' in res and len(res['data']) > 0:
            df = DataFrame(res['data'])
            df[['code']] = df[['code']].astype(str)
            df[['sub_code']] = df[['sub_code']].astype(str)
            df[['name']] = df[['name']].astype(str)
            df[['ipo_date']] = df[['ipo_date']].astype(str)
            df[['issue_date']] = df[['issue_date']].astype(str)
            df[['amount']] = df[['amount']].astype(float)
            df[['market_amount']] = df[['market_amount']].astype(float)
            df[['price']] = df[['price']].astype(float)
            if ('pe' in df.columns):
                df[['pe']] = df[['pe']].astype(float)
            df[['limit_amount']] = df[['limit_amount']].astype(float)
            df[['funds']] = df[['funds']].astype(float)
            df[['ballot']] = df[['ballot']].astype(float)
            df['ts_code'] = df['code']
            # df['exchange'] = df['code'].str.split('.',expand=True)[1] if len(df['code'].str.split('.')) > 1 else ''
            df['exchange'] = df['code'].str.split('.', expand=True)[1] if df['code'].str.split('.', expand=True).shape[
                                                                              1] > 1 else ''

            return df
        else:
            ts.set_token('df59bbfff559618c57346d99302a98c8f00d087dfcbf9a2271a66aac')
            pro = ts.pro_api()
            now = datetime.datetime.now().strftime('%Y%m%d')
            df = DataFrame(pro.new_share(start_date=now, end_date=now))
            return df
    except Exception as e:
        df = DataFrame([])
        return df


def 处理权限通知(device):
    source = device.appium.driver.page_source
    if 'text="始终允许"' in source:
        ele = device.has_element('/权限通知/提示信息')
        if ele and device.has_element('/权限通知/始终允许'):
            device.log.info('权限提示信息：%s' % str(ele))
            device.has_element('/权限通知/始终允许')()
            return True
    return False


def 处理通知(device):
    if device.has_element('/提示信息/消息内容', 1):
        msg = str(device.has_element('/提示信息/消息内容'))
        device.log.info('弹出提示消息：%s' % str(msg))
        if '因数据系统升级维护, 该功能使用将受到影响' in msg:
            device.has_element('/系统提示/通知/确定')()
            return True
    return False


def 相机定位识别(device, position_l, position_r):
    import time
    import os
    import cv2
    import base64
    from PIL import Image
    import json
    import requests
    import random
    start_time = time.time()
    if len(position_l) != 2 or len(position_r) != 2:
        print('坐标参数错误！')

    _time_str = str(random.randint(10, 99))
    file_name = _time_str + ".jpg"

    # 设置摄像头索引,ubuntu主控为0，个开发笔记本为1
    camera_index = 0
    cap = cv2.VideoCapture(camera_index)

    # 设置高分辨率，如果不支持，将使用摄像头默认分辨率
    cap.set(cv2.CAP_PROP_FRAME_WIDTH, 3840)
    cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 2160)

    if not cap.isOpened():
        print("无法打开摄像头")
        return

    # 读取几帧进行摄像头预热，减少初始化和对焦时间
    for _ in range(5):
        cap.read()

    # 读取实际帧
    ret, frame = cap.read()
    if ret:
        # 保存图像，设置较低的 JPEG 质量以加速保存过程
        cv2.imwrite(file_name, frame, [cv2.IMWRITE_JPEG_QUALITY, 85])
    else:
        print("无法捕获图像")

    # 释放摄像头资源
    cap.release()

    # 左上角、右下角 两点真实坐标,根据实际位置获取
    x_l = position_l[0]
    y_l = position_l[1]
    x_r = position_r[0]
    y_r = position_r[1]

    # 识别区域上下左右位置
    left = x_l
    top = y_l
    right = x_r
    bottom = y_r

    # 打开的截图
    im = Image.open(file_name)
    # 截取对应位置
    im = im.crop((left, top, right, bottom))
    # 顺时针旋转
    im = im.transpose(Image.Transpose.ROTATE_270)
    file_name_cut = _time_str + '_1.jpg'
    im.save(file_name_cut)

    # uname = 'uFDncJIIAH5H'
    # pwd = 'yC2EVUILo5XO'
    # 调用4090 OCR识别，如果准确率不够可以试用打码平台
    headers = {"Content-type": "application/json"}
    fo = open(file_name_cut, 'rb')
    img = fo.read()
    if img is None:
        print("error in loading image: }".format(file_name_cut))

    def cv2_to_base64(image):
        return base64.b64encode(image).decode('utf8')

    data = {'images': [cv2_to_base64(img)]}
    # data = {"username": uname, "password": pwd, "typeid": 1, "image": cv2_to_base64(img)}
    # result = json.loads(requests.post("http://api.ttshitu.com/predict", json=data, timeout=20).text)
    # if result['success']:
    #     result = result["data"]["result"]
    # else:
    #     result = result["message"]
    fo.close()
    ai_response = requests.post(url=ai_server_url, headers=headers, data=json.dumps(data))
    print(ai_response.text)
    result = ai_response.json()["results"][0]
    print(result)
    end_time = time.time()
    print(f"执行时间: {end_time - start_time} seconds")
    return result


def 标注截图(device, path, func_namme):
    if MARK_SCREEN == 'true':
        try:
            e = device.has_element(path, 2)
            tips_str = str(func_namme) + path
            device.screencap(name='mark', ele=e, tips=tips_str)
        except ElementNotExist as e:
            raise TestCaseFail(e)


def 输入文本(device, path, text, msg=None):
    标注截图(device, path, sys._getframe().f_code.co_name)
    try:
        device[path]()
        device[path] = str(text)
        device.log.info('向<%s>输入内容。' % path)
    except ElementNotExist as e:
        # if 处理通知(device):
        #     device[path]()
        #     device.log.info('点击<%s> 成功。' % path)
        #     return
        if msg:
            e = str(e) + '||' + msg

        # if 'cpshaitong' in API_URL:
        device.log.info('[error]向<%s>输入内容失败。' % path)

        raise TestCaseFail(e)


def 获取文本信息(device, path, msg=None):
    标注截图(device, path, sys._getframe().f_code.co_name)
    ele = None
    value = None
    try_times = 10
    for _ in range(try_times):
        ele = device.has_element(path, 1)
        if ele:
            value = str(ele)
            if value.strip() and value != '--':
                device.log.info('获取 <%s> 内容成功：%s。试了 %s 次' % (path, value, _ + 1))
                return value
    if ele is not None:
        device.log.info('获取 <%s> 内容无效：%s。试了 %s 次' % (path, value, try_times))
        return value

    device.log.info('[error]获取 <%s> 内容失败。' % path)
    raise TestCaseFail(ElementNotExist(path))


def 等待获取文本信息(device, path):
    ele = None
    value = None
    try_times = 10

    ele = device.wait_element(path, 1)
    if ele:
        value = str(ele)
        if value.strip() and value != '--':
            device.log.info('等待获取 <%s> 内容成功：%s。试了 %s 次' % (path, value, 1))
            return value
    if ele is not None:
        device.log.info('等待获取 <%s> 内容无效：%s。试了 %s 次' % (path, value, try_times))
        return value

    device.log.info('[error]等待获取 <%s> 内容失败。' % path)
    raise TestCaseFail(ElementNotExist(path))


def 循环滑动(device, 查找目标, 滑动方式):
    isSwipe = 5
    while isSwipe > 0:
        if not device.has_element(查找目标):
            print('未找到目标！')
            if 滑动方式 == '上':
                device.appium.slide_up()
            elif 滑动方式 == '下':
                device.appium.slide_down()
            elif 滑动方式 == '左':
                device.appium.slide_left()
            elif 滑动方式 == '右':
                device.appium.slide_right()
            isSwipe = isSwipe - 1
        else:
            print('发现目标：', 查找目标)
            break


def 指定位置左滑(device, 查找目标):
    y = device.has_element(查找目标).ele.location.get('y')
    l = device.screen_width, device.screen_height,
    width = l[0]
    print('y:%s,width:%s' % (y, width))
    device.appium.driver.swipe(int(width * 0.7), int(y), int(width * 0.3), int(y), 2000)


def 获取控件对象(device, path):
    标注截图(device, path, sys._getframe().f_code.co_name)
    try:
        e = device[path]
        device.log.info('控件<%s>获取成功' % path)
        return e.ele
    except ElementNotExist:
        device.log.info('未查找到控件：%s' % path)
        return ''


def 等待获取控件对象(device, path):
    try:
        e = device.wait_element(path)
        if e:
            device.log.info('控件<%s>等待获取成功' % path)
            return e.ele
    except ElementNotExist:
        device.log.info('未查找到控件：%s' % path)
        return ''


def 是否存在控件(device, path, trytime=6, msg=None):
    # 华泰2025 测试重试时间专用处理
    if 'cpshtzq' in API_URL:
        trytime = 2
    标注截图(device, path, sys._getframe().f_code.co_name)
    e = device.has_element(path, trytime, msg=msg)
    return bool(e if e else '')


def 是否存在控件并点击(device, path, msg=''):
    try:
        标注截图(device, path, sys._getframe().f_code.co_name)
        e = device.has_element(path, 6, msg=msg)
        if e:
            e.click()
            device.log.info('点击<%s>成功' % path)
            return e
        else:
            device.log.info('未查找到控件：<%s>' % path)
            return ''
    except ElementNotExist as e:
        if msg:
            e = str(e) + '||' + msg
            device.log.info(e)
        return ''


def 点击(device, path, leafid='', leafid_page='', leafpath='', msg=''):
    try:
        if leafid != '':
            e = device.has_element(path, 2)
            if e:
                device.log.info('点击<%s>【埋点编号：leaf%s】成功' % (path, leafid))
                device.screencap(name='leaf' + leafid, ele=e)

        if leafid == '' and leafid_page == '':
            标注截图(device, path, sys._getframe().f_code.co_name)
        device[path]()
        device.log.info('点击<%s> 成功。' % path)

        if leafid_page != '':
            if leafpath != '':
                time.sleep(5)
                e = device.has_element(leafpath, 2)
                if e:
                    device.log.info('点击<%s>【页面埋点编号：leaf%s】成功' % (path, leafid_page))
                    device.screencap(name='leaf' + leafid_page)
                else:
                    device.log.info('点击<%s> 成功【页面埋点编号：leaf%s】跳转校验失败' % (path, leafid_page))
            else:
                device.log.info('【页面埋点编号：leaf%s】缺少跳转校验控件' % leafid_page)

        if leafid != '' or leafid_page != '':
            time.sleep(10)

    except ElementNotExist as e:
        # if 处理通知(device):
        #     device[path]()
        #     device.log.info('点击<%s> 成功。' % path)
        #     return
        if msg:
            e = str(e) + '||' + msg

        # if 'cpshaitong' in API_URL:
        device.log.info('[error]点击<%s> 失败。' % path)

        raise TestCaseFail(e)


def 点击真实等待(device, path):
    try:
        # device[path]()
        _el = device.wait_element(path)
        if _el:
            _el.click()
        device.log.info('等待点击<%s> 成功。' % path)

    except ElementNotExist as e:
        # if 处理通知(device):
        #     device[path]()
        #     device.log.info('点击<%s> 成功。' % path)
        #     return
        raise TestCaseFail(e)


def 返回(device):
    device.log.info('点击返回')
    device.appium.back()


def 为空校验(device):
    device.screencap()
    return "预期结果不为空，实际结果为空。"


def 非空校验(device, 预期结果, 实际结果):
    device.screencap()
    res = '预期结果为:%s,实际结果为:%s' % (str(预期结果) if 预期结果 else None, 实际结果)
    if 预期结果 in 实际结果:
        return res
    li = ['当前时', '交易时间', '不允许', '状态错', '超过融券负债可还数量', '划转申请提交失败']
    if sum([l in 实际结果 for l in li]) > 0:
        raise Exception(实际结果)
    return res


def 不足非空校验(device, 预期结果, 实际结果):
    device.screencap()
    res = '预期结果为:%s,实际结果为:%s' % (str(预期结果) if 预期结果 else None, 实际结果)
    if 预期结果 in 实际结果:
        return res
    li = ['不足', '当前时', '交易时间', '不允许', '状态错', '超过融券负债可还数量', '划转申请提交失败']
    if sum([l in 实际结果 for l in li]) > 0:
        raise Exception(实际结果)
    return res


def 数字校验(device, 实际结果):
    device.screencap()
    return '预期结果大于等于0，实际结果为:%s' % 实际结果


def 比较校验(device, 实际结果1, 实际结果2):
    device.screencap()
    return '实际结果1为:%s,实际结果2为:%s,实际结果1小于实际结果2' % (实际结果1, 实际结果2)


def 两次校验(device, 预期结果1, 实际结果1, 预期结果2, 实际结果2):
    device.screencap()
    return '第一次预期结果为：%s,第一次实际结果为：%s,\n 第二次预期结果为：%s,第二次实际结果为：%s,' \
        % (预期结果1, 实际结果1, 预期结果2, 实际结果2)


def 类型校验(device, 预期, 实际):
    device.screencap()
    类型 = ''
    if 预期 == int:
        类型 = '数字'
    elif 预期 == float:
        类型 = '浮点数'
    return '预期结果类型为%s，实际结果为：%s' % (类型, 实际)


def 对象拖动(device, 控件位置1, 控件位置2):
    控件位置1 = 获取对象位置和手机分辨率(device, 控件位置1)[0]
    控件位置2 = 获取对象位置和手机分辨率(device, 控件位置2)[0]
    device.log.info("控件位置1['x']+10:: %s" % (控件位置1['x'] + 10))
    device.log.info("控件位置1['y']+10:: %s" % (控件位置1['y'] + 10))
    device.log.info("控件位置2['x']+10:: %s" % (控件位置2['x'] + 10))
    device.log.info("控件位置2['y']+10:: %s" % (控件位置2['y'] + 10))
    device.appium.driver.swipe(控件位置1['x'] + 10, 控件位置1['y'] + 20, 控件位置1['x'] + 10, 控件位置2['y'] + 20, 2000)
    device.log.info("滑动结束。。。。")


def 小上滑(device, _t=3000, _p=0.3):
    device.appium.slide_up(_t, _p)
    device.log.info('小幅上滑')


def 上滑(device, _t=3000):
    device.appium.slide_up(_t)
    device.log.info('上滑')
    
def 佣金宝上滑(device, _t=2000):
    device.appium.slide_up(_t)
    device.log.info('上滑')


def 坐标海通期货上滑(device, y_per1, y_per2):
    time.sleep(1)
    xy = device.appium.driver.get_window_size()
    y1 = int(xy['height'] * y_per1)
    x1 = int(xy['width'] * 1 / 2)
    y2 = int(xy['height'] * y_per2)
    print("在高度：%s处上滑" % y1)
    device.appium.driver.swipe(x1, y1, x1, y2, 2000)


def 海通期货控件上滑(device, 控件位置):
    对象位置和手机分辨率 = 获取对象位置和手机分辨率(device, 控件位置)
    y1 = int(对象位置和手机分辨率[1]['height'] * 4 / 5)
    y2 = int(对象位置和手机分辨率[1]['height'] * 1 / 5)
    x1 = int(对象位置和手机分辨率[0]['x'] + 10)
    x2 = int(对象位置和手机分辨率[0]['x'] + 10)
    device.appium.Drive.swipe(x1, y1, x2, y2, 3500)


def 左滑(device, _t=2000):
    device.appium.slide_left(_t)
    device.log.info('左滑')


def 下滑(device, _t=2000):
    device.appium.slide_down(_t)
    device.log.info('下滑')


def 右滑(device, _t=2000):
    device.appium.slide_right(_t)
    device.log.info('右滑')


def 登录前页面处理(device):
    time.sleep(2)
    if not 检测界面弹出层(device):
        device.log.info('未检测到弹窗信息')
    else:
        device.log.info('处理启动弹窗')
        for i in range(3):
            source = device.appium.driver.page_source
            # if "不再提醒" in source and device.has_element("/公用/不再提醒"):
            if device.has_element("/公用/不再提醒"):
                device.has_element("/公用/不再提醒")()
                device.log.info('不再提醒！')
                continue
            elif "我要打新" in source and device.has_element("/今日新股申购提醒/关闭"):
                device.has_element("/今日新股申购提醒/关闭")()
                device.log.info('关闭今日新股申购提醒！')
                continue
            elif (
                    "后台清算系统提示" in source or '系统维护' in source or '系统暂停服务的公告' in source) and device.has_element(
                    '/后台清算系统提示/确定'):
                device.has_element('/后台清算系统提示/确定')()
                device.log.info('关闭系统维护提示')
                continue
            elif ('text="同意" ' in source or '数据系统升级维护' in source) and device.has_element("/隐私协议/同意"):
                device.has_element("/隐私协议/同意")()
                device.log.info("同意隐私条款")
                continue
            elif device.has_element('/涨乐财富通/新版弱账户登录/关闭'):
                device.has_element('/涨乐财富通/新版弱账户登录/关闭')()
                device.log.info('弱账户登录页面关闭')
                continue
            elif device.has_element('/涨乐财富通/公告弹窗/关闭'):
                device.has_element('/涨乐财富通/公告弹窗/关闭')()
                device.log.info('关闭公告弹窗')
                continue
            # elif "科创板交易规则提示" in source or "查看更多" in source and device.has_element("/科创板交易规则提示/关闭"):
            #     device.has_element("/科创板交易规则提示/关闭")()
            #     device.log.info('关闭科创板交易规则提示')
            # elif ("后台清算系统提示" in source or '我知道了' in source or '系统维护' in source) and device.has_element('/后台清算系统提示/确定'):
            # elif "隐私条款" in source and device.has_element("/隐私协议/同意"):
            #     device.has_element("/隐私协议/同意")()
            #     device.log.info("同意隐私条款")
            # elif 'text="知道了" ' in source and device.has_element("/风险提示/知道了"):
            #     device.has_element("/风险提示/知道了")()
            #     device.log.info("关闭风险提示")
            # elif 'text="测评" ' in source and device.has_element("/提示取消/取消按钮"):
            #     device.has_element("/提示取消/取消按钮")()
            #     device.log.info("取消测评")
            # elif '数据系统升级维护' in source and device.has_element('/提示确认/确认按钮'):
            #     device.has_element('/提示确认/确认按钮')()
            #     device.log.info('数据系统升级维护确定')
            #     continue
            # elif device.has_element('/关闭遮罩层'):
            #     device.has_element('/关闭遮罩层')()
            #     device.log.info('关闭遮罩广告')
            #     continue
            else:
                device.log.info('已检查{}次'.format(i + 1))
                break
        device.log.info('已处理启动弹窗')


def 登录后页面处理(device):
    time.sleep(3)
    device.log.info('处理登录后弹窗')
    for i in range(3):
        source = device.appium.driver.page_source
        error_list = ['无此客户号', '请用客户号登录', '网络似乎是不通的', '客户交易密码错误', '帐号或密码错误',
                      '认证账户被锁定', '连续多次输入错误的通讯密码', '接入许可证不存在',
                      '转发错误']
        for e in error_list:
            if e in source:
                raise CanNotLogin(e)
        if '[认证]' in source:
            message = device.has_element('/登陆/系统提示内容')
            print('系统提示: %s' % message)
            raise CanNotLogin(message)

        if "温馨提示" in source or "资产收益统计说明及风险揭示" in source or "系统提示" in source or "风险提示" in source:
            if device.has_element('/提示取消/取消按钮'):
                device['/提示取消/取消按钮']()
        elif 'text="选择常用交易"' in source and device.has_element('/账户弹框提示信息/普通交易'):
            device['/账户弹框提示信息/普通交易']()
        elif device.has_element('/登陆/系统提示') and device.has_element('/系统提示/通知/确定'):
            device.has_element('/系统提示/通知/确定')()
            # raise CanNotLogin(device.has_element('/登陆/系统提示内容'))
        elif ("后台清算系统提示" in source or '我知道了' in source or '系统维护' in source) and device.has_element(
                '/后台清算系统提示/确定'):
            device.has_element('/后台清算系统提示/确定')()
            device.log.info('关闭系统提示')
        elif device.has_element("/通用/登录后弹框/关闭按钮"):
            device.has_element("/通用/登录后弹框/关闭按钮")()
            device.log.info("关闭登录后弹框")
        elif "不再提醒" in source and device.has_element("/公用/不再提醒"):
            device.has_element("/公用/不再提醒")()
            device.log.info('不再提醒！')
        elif device.has_element('/关闭调仓确认'):
            device.has_element('/关闭调仓确认')()
            device.log.info('关闭调仓确认')
        else:
            device.log.info('已处理登录后弹窗')
            return

def 银河登录前页面处理(device):
    time.sleep(2)
    if not 检测界面弹出层(device):
        device.log.info('未检测到 信息')
    else:
        device.log.info('处理启动弹窗')
        for i in range(3):
            source = device.appium.driver.page_source
            # if "不再提醒" in source and device.has_element("/公用/不再提醒"):
            if device.has_element("/银河证券/财富星"):
                device.has_element("/银河证券/财富星")()
                device.log.info('财富星弹窗处理')
                continue
            elif "今日公告" in source and device.has_element("/银河证券/今日公告关闭"):
                device.has_element("/银河证券/今日公告关闭")()
                device.log.info('今日公告弹窗处理！')
                continue
            elif device.has_element('/银河证券/基金投资'):
                device.has_element('/银河证券/基金投资')()
                device.log.info('基金投资弹窗处理')
                continue
            elif device.has_element('/银河证券/ETF大赛'):
                device.has_element('/银河证券/ETF大赛')()
                device.log.info('ETF大赛弹窗处理')
                continue
            # elif (
            #         "后台清算系统提示" in source or '系统维护' in source or '系统暂停服务的公告' in source) and device.has_element(
            #         '/后台清算系统提示/确定'):
            #     device.has_element('/后台清算系统提示/确定')()
            #     device.log.info('关闭系统维护提示')
            #     continue
            # elif ('text="同意" ' in source or '数据系统升级维护' in source) and device.has_element("/隐私协议/同意"):
            #     device.has_element("/隐私协议/同意")()
            #     device.log.info("同意隐私条款")
            #     continue
            # elif device.has_element('/涨乐财富通/新版弱账户登录/关闭'):
            #     device.has_element('/涨乐财富通/新版弱账户登录/关闭')()
            #     device.log.info('弱账户登录页面关闭')
            #     continue
            # elif device.has_element('/涨乐财富通/公告弹窗/关闭'):
            #     device.has_element('/涨乐财富通/公告弹窗/关闭')()
            #     device.log.info('关闭公告弹窗')
            #     continue
            else:
                device.log.info('已检查{}次'.format(i + 1))
                break
        device.log.info('已处理启动弹窗')


def 涨乐财富通登录(device, 登录账户, 交易密码, 通讯密码):
    # 8.3.3新版登录
    # device.log.info('强账户登录')
    # try:
    输入文本(device, '/涨乐财富通/交易/交易登录页面/账户输入框', 登录账户)
    # device['/涨乐财富通/交易/交易登录页面/账户输入框'] = 登录账户
    device['/涨乐财富通/交易/交易登录页面/密码输入框'] = 交易密码
    # if device.has_element('/涨乐财富通/交易/交易登录页面/安全密码输入框'):
    #     device['/涨乐财富通/交易/交易登录页面/安全密码输入框'] = '110119'
    #     device.log.info('安全密码登录')
    device['/涨乐财富通/交易/交易登录页面/登陆按钮'].click()
    if device.has_element('/涨乐财富通/交易/交易登录页面/通讯密码验证'):
        device.log.info('输入通讯密码')
        device['/涨乐财富通/交易/交易登录页面/通讯密码验证'].click()
        device['/涨乐财富通/交易/交易登录页面/通讯密码弹窗/输入框'] = 通讯密码
        device['/涨乐财富通/交易/交易登录页面/通讯密码弹窗/确认'].click()
    # if device.has_element('/涨乐财富通/交易/交易登录页面/开启安全保护'):
    #     device['/涨乐财富通/交易/交易登录页面/开启安全保护'].click()
    #     device.log.info('开启安全保护')
    # if device.has_element('/涨乐财富通/交易/交易登录页面/设备安全密码/设置并登录'):
    #     device.log.info('设置国密密码')
    #     device['/涨乐财富通/交易/交易登录页面/设备安全密码/设置并登录'].click()
    #     device['/涨乐财富通/交易登录页面/设备安全密码/密码输入框'] = '110119'
    # 坐标点击(device, 1 / 2, 640 / 2340)  # 输入框
    # 坐标点击(device, 180 / 1080, 1620 / 2340)  # 1
    # 坐标点击(device, 180 / 1080, 1620 / 2340)  # 1
    # 坐标点击(device, 1 / 2, 2222 / 2340)  # 0
    # 坐标点击(device, 180 / 1080, 1620 / 2340)  # 1
    # 坐标点击(device, 180 / 1080, 1620 / 2340)  # 1
    # 坐标点击(device, 900 / 1080, 2020 / 2340)  # 9
    time.sleep(1)

    if not 检测界面弹出层(device):
        device.log.info('未检测到弹窗信息')
    else:
        device.log.info('处理登录后弹窗')
        for i in range(3):
            source = device.appium.driver.page_source
            # if "资产收益统计说明及风险揭示" in source:
            if device.has_element('/提示取消/取消按钮'):
                device['/提示取消/取消按钮']()
                device.log.info('关闭风险揭示')
                continue
            # elif 'text="选择常用交易"' in source and device.has_element('/账户弹框提示信息/普通交易'):
            #     device['/账户弹框提示信息/普通交易']()
            #     device.log.info('关闭调仓确认')
            #     continue
            elif ("后台数据清算" in source or '系统维护' in source) and device.has_element('/后台清算系统提示/确定'):
                device.has_element('/后台清算系统提示/确定')()
                device.log.info('关闭系统提示')
                continue
            elif "不再提醒" in source and device.has_element("/公用/不再提醒"):
                device.has_element("/公用/不再提醒")()
                device.log.info('不再提醒！')
                continue
            # elif "调仓" in source and device.has_element('/关闭调仓确认'):
            #     device.has_element('/关闭调仓确认')()
            #     device.log.info('关闭调仓确认')
            #     continue
            else:
                device.log.info('已检查{}次'.format(i + 1))
                break
        device.log.info('已处理登录后弹窗')
    # except:
    #     device.log.info('登录异常')


def 登录(device, 账号, 交易密码, 通信密码):
    未登录 = device.has_element('/交易/交易登录页面/登陆按钮')
    if 未登录 != '':
        device.log.info('使用账号 %s 登录' % 账号)
        device['/交易/交易登录页面/账户输入框'] = 账号
        device['/交易/交易登录页面/密码输入框'] = 交易密码
        device['/交易/交易登录页面/通信密码输入框'] = 通信密码
        device['/交易/交易登录页面/登陆按钮'].click()
        # raise CanNotLogin(device.has_element('/登陆/系统提示内容'))
    登录后页面处理(device)


def 交易登录1(device, 账号, 交易密码, 通信密码):
    device['/底部导航栏/交易界面跳转按钮'].click()
    if device.has_element('/关闭遮罩层'):
        点击(device, '/关闭遮罩层')
    ele = device['/交易/普通交易/普通交易首页/登陆按钮']
    if ele == '登录账户':
        device.log.info('使用账号 %s 交易登录' % 账号)
        ele.click()
        device['/交易/交易登录页面/账户输入框'] = 账号
        device['/交易/交易登录页面/密码输入框'] = 交易密码
        device['/交易/交易登录页面/通信密码输入框'] = 通信密码
        device['/交易/交易登录页面/登陆按钮'].click()
    登录后页面处理(device)


def 交易登录(device, 登录账户, 交易密码, 通信密码):
    device['/安信手机证券/交易界面跳转按钮'].click()
    if device.has_element('/关闭遮罩层'):
        点击(device, '/关闭遮罩层')
    ele = device['/交易/普通交易/普通交易首页/登陆按钮']
    if ele == '登录':
        device.log.info('使用账号 %s 交易登录' % 登录账户)
        ele.click()
        account = device['/交易/交易登录页面/账户输入框']
        if account != 登录账户:
            device['/交易/交易登录页面/账户输入框'] = 登录账户
            device['/交易/交易登录页面/通信密码输入框'] = 通信密码
        device['/交易/交易登录页面/密码输入框'] = 交易密码
        device['/交易/交易登录页面/登陆按钮'].click()
    登录后页面处理(device)


def 安信交易登录(device):
    device['/安信手机证券/交易界面跳转按钮'].click()
    未登录 = 获取文本信息(device, '/安信手机证券/交易/登录按钮') == '登录'
    if 未登录:
        点击(device, '/安信手机证券/交易/登录按钮')
        坐标点击(device, 138 / 1080, 1630 / 2340)
        坐标点击(device, 138 / 1080, 1630 / 2340)
        坐标点击(device, 385 / 1080, 2260 / 2340)
        坐标点击(device, 138 / 1080, 1630 / 2340)
        坐标点击(device, 138 / 1080, 1630 / 2340)
        坐标点击(device, 688 / 1080, 2130 / 2340)
        坐标点击(device, 960 / 1080, 2050 / 2340)
    else:
        print('login')


def 理财登陆(device, 账号, 交易密码, 通信密码):
    device['/底部导航栏/理财界面跳转按钮']()
    time.sleep(3)
    _l = device.has_element('/理财/理财首页/登陆按钮', 15)
    if _l:
        device.log.info('使用账号 %s 理财登陆' % 账号)
        _l()
        device['/交易/交易登录页面/账户输入框'] = 账号
        device['/交易/交易登录页面/密码输入框'] = 交易密码
        device['/交易/交易登录页面/通信密码输入框'] = 通信密码
        device['/交易/交易登录页面/登陆按钮'].click()
    登录后页面处理(device)


def 微信登录(device):
    if device.has_element('/登录/微信登录'):
        device.log.info('微信登录')
        点击(device, '/登录/微信登录')
        time.sleep(5)
        # 如果没有安装微信，点击之后还是在原页面
        if 获取控件对象(device, '/登录/微信登录'):
            device.log.info('该手机没有安装微信')
            return False
        if 获取控件对象(device, '/登录/微信登录/账号输入'):
            device.log.info('该手机需要登录微信')
            return False
        return True
    else:
        return True


def 刷新页面_理财(device):
    device.log.info('刷新页面_理财')
    点击(device, '/底部导航栏/首页界面跳转按钮')
    点击(device, '/底部导航栏/理财界面跳转按钮')


def 刷新页面_交易(device):
    device.log.info('刷新页面_交易')
    点击(device, '/底部导航栏/首页界面跳转按钮')
    if device.has_element('/关闭遮罩层'):
        点击(device, '/关闭遮罩层')
    点击(device, '/底部导航栏/交易界面跳转按钮')


def 滑动控件至屏幕内(device: LocalDevice, path):
    _duration = 3000
    _try_time = 5

    # 华泰2025 测试滑动重试次数
    if 'cpshtzq' in API_URL:
        _duration = 2000
        _try_time = 2

    for d in range(10):
        device.log.info('寻找控件：%s， 第 %s 次' % (path, d + 1))
        ele = device.has_element(path, trytime=_try_time)

        device.log.info('滑动寻找控件：%s， 第 %s 次' % (path, d + 1))
        if ele:
            y = ele.location[1]
            height = device.screen_height
            if y > height * 0.8:
                device.appium.slide_up(_duration, 0.4)
                ele = device.has_element(path)
            return ele
        device.appium.slide_up(_duration, 0.5)

    raise TestCaseFail(path)


def 滑动控件至屏幕内_理财(device, 控件):
    for d in range(10):
        device.log.info('理财页滑动寻找控件：%s， 第 %s 次' % (控件, d + 1))
        控件位置 = 获取对象位置和手机分辨率(device, 控件)
        device.log.info("控件位置[0]['y']:: %s" % 控件位置[0]['y'])
        device.log.info("控件位置[1]['height']::%s" % 控件位置[1]['height'])
        if 控件位置 == '':
            device.appium.slide_up(3000, 0.4)
            continue
        if 控件位置[0]['y'] > 控件位置[1]['height'] * 0.9:
            device.log.info("------------向上滑动")
            device.appium.slide_up(3000, 0.4)
            device.log.info('上滑完毕----------------')
        else:
            print("最终定格在：：：", 控件位置)
            break
        # 刷新页面_理财(device)


def 截屏(device):
    device.log.info('截屏')
    device.screencap()


def 截取(device, string, num):
    r = re.search(r"\d{" + str(num) + "}", string)
    print('------------------', r)
    print('================', r.groups())
    return r.group(0)


def 截取合同号(device, string):
    r = re.search(r"\d+", string)
    # print('------------------', r)
    # print('================', r.groups())
    return r.group(0)


def 获取对象位置和手机分辨率(device: LocalDevice, 控件位置):
    v = device.has_element(控件位置)
    return [v.ele.location if v else {'x': -1, 'y': -1}, {
        'width': device.screen_width,
        'height': device.screen_height,
    }]


def 控件大距离左滑(device, 控件位置):
    对象位置和手机分辨率 = 获取对象位置和手机分辨率(device, 控件位置)
    y1 = int(对象位置和手机分辨率[0]['y']) + 10
    y2 = int(对象位置和手机分辨率[0]['y']) + 10
    x1 = int(对象位置和手机分辨率[1]['width'] * 9 / 10)
    x2 = int(对象位置和手机分辨率[1]['width'] * 1 / 10)
    device.appium.driver.swipe(x1, y1, x2, y2, 3500)


def 自定义控件滑动(device, 控件位置, 起始点百分比, 结束点百分比, 滑动持续时间, 类型=True):
    对象位置和手机分辨率 = 获取对象位置和手机分辨率(device, 控件位置)
    if 类型:
        y = int(对象位置和手机分辨率[0]['y']) + 10
        x1 = int(对象位置和手机分辨率[1]['width'] * 起始点百分比)
        x2 = int(对象位置和手机分辨率[1]['width'] * 结束点百分比)
        device.appium.driver.swipe(x1, y, x2, y, 滑动持续时间)
    else:
        x = int(对象位置和手机分辨率[0]['x'] + 10)
        y1 = int(对象位置和手机分辨率[1]['height'] * 起始点百分比)
        y2 = int(对象位置和手机分辨率[1]['height'] * 结束点百分比)
        device.appium.driver.swipe(x, y1, x, y2, 滑动持续时间)


def 控件左滑(device, 控件位置):
    对象位置和手机分辨率 = 获取对象位置和手机分辨率(device, 控件位置)
    y1 = int(对象位置和手机分辨率[0]['y']) + 10
    y2 = int(对象位置和手机分辨率[0]['y']) + 10
    x1 = int(对象位置和手机分辨率[1]['width'] * 4 / 5)
    x2 = int(对象位置和手机分辨率[1]['width'] * 1 / 5)
    device.appium.driver.swipe(x1, y1, x2, y2, 3500)


def 开源控件左滑(device, 控件位置):
    对象位置和手机分辨率 = 获取对象位置和手机分辨率(device, 控件位置)
    y1 = int(对象位置和手机分辨率[0]['y']) + 10
    y2 = int(对象位置和手机分辨率[0]['y']) + 10
    x1 = int(对象位置和手机分辨率[1]['width'] * 7 / 8)
    x2 = int(对象位置和手机分辨率[1]['width'] * 1 / 8)
    device.appium.driver.swipe(x1, y1, x2, y2, 2000)


def 太平洋行情控件左滑(device, 控件位置):
    对象位置和手机分辨率 = 获取对象位置和手机分辨率(device, 控件位置)
    y1 = int(对象位置和手机分辨率[0]['y']) + 10
    y2 = int(对象位置和手机分辨率[0]['y']) + 10
    x1 = int(对象位置和手机分辨率[1]['width'] * 9 / 10)
    x2 = int(对象位置和手机分辨率[1]['width'] * 1 / 10)
    device.appium.driver.swipe(x1, y1, x2, y2, 2000)


def 控件左滑海通(device, 控件位置):
    对象位置和手机分辨率 = 获取对象位置和手机分辨率(device, 控件位置)
    y1 = int(对象位置和手机分辨率[0]['y']) + 10
    y2 = int(对象位置和手机分辨率[0]['y']) + 10
    x1 = int(对象位置和手机分辨率[1]['width'] * 4 / 5)
    x2 = int(对象位置和手机分辨率[1]['width'] * 1 / 5)
    device.appium.driver.swipe(x1, y1, x2, y2, 1000)


def 控件右滑(device, 控件位置):
    对象位置和手机分辨率 = 获取对象位置和手机分辨率(device, 控件位置)
    y1 = int(对象位置和手机分辨率[0]['y']) + 10
    y2 = int(对象位置和手机分辨率[0]['y']) + 10
    x1 = int(对象位置和手机分辨率[1]['width'] * 1 / 5)
    x2 = int(对象位置和手机分辨率[1]['width'] * 4 / 5)
    device.appium.driver.swipe(x1, y1, x2, y2, 3500)


def 控件上滑(device, 控件位置):
    对象位置和手机分辨率 = 获取对象位置和手机分辨率(device, 控件位置)
    y1 = int(对象位置和手机分辨率[0]['height'] * 1 / 5)
    y2 = int(对象位置和手机分辨率[0]['height'] * 4 / 5)
    x1 = int(对象位置和手机分辨率[0]['width'] + 10)
    x2 = int(对象位置和手机分辨率[0]['width'] + 10)
    device.appium.driver.swipe(x1, y1, x2, y2, 3500)


def 控件下滑(device, 控件位置):
    对象位置和手机分辨率 = 获取对象位置和手机分辨率(device, 控件位置)
    y1 = int(对象位置和手机分辨率[0]['height'] * 4 / 5)
    y2 = int(对象位置和手机分辨率[0]['height'] * 1 / 5)
    x1 = int(对象位置和手机分辨率[0]['width'] + 10)
    x2 = int(对象位置和手机分辨率[0]['width'] + 10)
    device.appium.driver.swipe(x1, y1, x2, y2, 3500)
    device.log.info('控件下滑：：：' % 控件位置)


def 控件左滑_理财(device, 控件位置):
    对象位置和手机分辨率 = 获取对象位置和手机分辨率(device, 控件位置)
    y1 = int(对象位置和手机分辨率[0]['y']) + 10
    y2 = int(对象位置和手机分辨率[0]['y']) + 10
    x1 = int(对象位置和手机分辨率[1]['width'] * 4 / 5)
    x2 = int(对象位置和手机分辨率[1]['width'] * 1 / 5)
    device.appium.driver.swipe(x1, y1, x2, y2, 2000)
    # 刷新页面_理财(device)


@hide_error
def 系统提示(device: LocalDevice, 查找目标, 关闭按钮):
    if device.has_element(查找目标, 2):
        device[关闭按钮]()


def 目录校验(device, 级别, 模块, 控件对象):
    time.sleep(2)
    print('=======================%s级目录校验====================' % 级别)
    try:
        页面标题 = 获取文本信息(device, 控件对象)
        返回结果 = 模块 in 页面标题
        if 返回结果:
            device.log.info('目前已经进入到%s级目录:【%s】' % (级别, 模块))
            return 返回结果
        else:
            raise NotEnterError(级别, 模块)
    except:
        raise NotEnterError(级别, 模块)


def 金额转换(device, 金额=None):
    if 金额 is None:
        金额 = device
    金额 = 金额.replace(',', '')
    if 金额.endswith('万'):
        金额 = int(float(金额.replace('万', '')) * 10000)
    return 金额


def 网络投票_获取公司代码(device, start):
    import re
    codes = set()
    for i in range(5):
        source = device.appium.driver.page_source
        # print(source)
        codeList = source.split('公司代码')
        codeList = codeList

        for icodeList in codeList:
            if codeList.index(icodeList) == 0:
                continue
            x_index = icodeList.find('text="' + start) + len('text="')
            code = icodeList[x_index:x_index + 6]
            # print('code ::::::::', code)
            string = re.search(r"\d{6}", code)
            # print('str ::::::::', string)
            if string:
                code = string.group(0)
                codes.add(code)
                # print('newCode :::', code)

        上滑(device)

    print('================================')
    print(codes)
    print('================================')
    return codes


@域名解析
def 坐标点击(device, x_per, y_per, tips=""):
    time.sleep(1)
    try:
        w = device.appium.driver.get_window_size()['width']
        h = device.appium.driver.get_window_size()['height']

        if x_per > 1 or y_per > 1:
            print('x或y的坐标超出当前分辨率！')
            raise ElementNotExist('输入的坐标超出当前屏幕')

        x = x_per * w
        y = y_per * h
        device.appium.driver.tap([(x, y)])
        device.log.info('点击坐标(%s,%s)%s 成功 ' % (x, y, tips))
    except ElementNotExist as e:
        # if 'cpshaitong' in API_URL:
        device.log.info('[error]点击坐标(%s,%s)%s 失败' % (x, y, tips))

        raise TestCaseFail(e)


def 循环校验(device, prefix, contents):
    res = True
    for c in contents:
        text = 获取文本信息(device, prefix + c)
        res = text != '' and text is not None and text != '--'
        device.log.info('%s 的值为: %s' % ((prefix + c), text))
        if not res:
            break

    return res


def 坐标左滑(device, y_per):
    time.sleep(2)
    x = device.screen_width
    y = device.screen_height
    y1 = int(y * y_per)
    x1 = int(x * 9 / 10)
    x2 = int(x * 1 / 10)
    device.log.info('在高度%s的位置向左滑动' % y1)
    device.appium.driver.swipe(x1, y1, x2, y1, 3500)


def 坐标上滑(device, x_per, y_per):
    time.sleep(1)
    xy = device.appium.driver.get_window_size()
    y1 = int(xy['height'] * y_per)
    y2 = int(xy['height'] * 1 / 5)
    x1 = int(xy['width'] * x_per)
    device.appium.driver.swipe(x1, y1, x1, y2, 3500)


# 显示的时间，和本地时间比对
def 分钟时差效验(device, time):
    dt = datetime.datetime
    # 本地当前时间
    now = dt.now()
    # 格式化抓取到的时间
    catch = now.strftime('%Y-%m-%d ') + time
    catch_time = dt.strptime(catch, '%Y-%m-%d %H:%M:%S')
    # 比较秒
    diff = now - catch_time
    ds = diff.seconds
    # 如果差值大于60秒，则返回false
    if (ds > 60):
        return [False, '对比本地时间与短信精灵显示时间，时差超过1分钟']
    else:
        return [True, '对比本地时间与短信精灵显示时间，时差不超过1分钟']


def 获取验证码(device, path, typeid=3):
    import base64
    from PIL import Image
    import json
    import requests
    import random

    uname = 'uFDncJIIAH5H'
    pwd = 'yC2EVUILo5XO'
    filename = 'pictures/pay_ocr/' + "image_screen_" + str(random.randint(10, 99)) + ".png"
    #截屏
    device.appium.driver.save_screenshot(filename)
    # device.save_screenshot(filename)
    element = 获取控件对象(device, path)
    #获取元素上下左右的位置
    left = element.location['x']
    top = element.location['y']
    right = element.location['x'] + element.size['width']
    bottom = element.location['y'] + element.size['height']
    #打开刚才的截图
    im = Image.open(filename)
    #截取对应位置
    im = im.crop((left, top, right, bottom))
    filename2 = 'pictures/pay_ocr/' + 'image_code.png'
    #保存覆盖原有截图
    im.save(filename2)

    def base64_api(uname, pwd, img, typeid):
        with open(img, 'rb') as f:
            base64_data = base64.b64encode(f.read())
            b64 = base64_data.decode()
        data = {"username": uname, "password": pwd, "typeid": typeid, "image": b64}
        result = json.loads(requests.post("http://api.ttshitu.com/predict", json=data).text)
        if result['success']:
            return result["data"]["result"]
        else:
            return result["message"]
        return ""

    result = base64_api(uname=uname, pwd=pwd, img=filename2, typeid=typeid)
    if os.path.exists(filename):
        os.remove(filename)
    if os.path.exists(filename2):
        os.remove(filename2)
    return result


def 新获取验证码(device, path):
    import base64
    from PIL import Image
    import json
    import requests
    import random
    def noise_remove_cv2(image_name, k):
        import cv2
        import ddddocr
        from PIL import Image
        def calculate_noise_count(img_obj, w, h):
            count = 0
            width, height = img_obj.shape
            for _w_ in [w - 1, w, w + 1]:
                for _h_ in [h - 1, h, h + 1]:
                    if _w_ > width - 1:
                        continue
                    if _h_ > height - 1:
                        continue
                    if _w_ == w and _h_ == h:
                        continue
                    if img_obj[_w_, _h_] < 230:
                        count += 1
            return count

        img = cv2.imread(image_name, 1)
        gray_img = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        w, h = gray_img.shape
        for _w in range(w):
            for _h in range(h):
                if _w == 0 or _h == 0:
                    gray_img[_w, _h] = 255
                    continue
                pixel = gray_img[_w, _h]
                if pixel == 255:
                    continue

                if calculate_noise_count(gray_img, _w, _h) < k:
                    gray_img[_w, _h] = 255

        return gray_img

    def ocr_string(path):
        import cv2
        import ddddocr
        from PIL import Image
        name = str(int(time.time())) + ".png"
        image = noise_remove_cv2(path, 4)
        cv2.imwrite(name, image)
        ocr = ddddocr.DdddOcr()
        with open(name, 'rb') as f:
            img_bytes = f.read()
        res = ocr.classification(img_bytes)
        if os.path.exists(name):
            os.remove(name)
        return res

    filename = 'pictures/handle/' + "image_screen_" + str(random.randint(10, 99)) + ".png"
    # 截屏
    device.appium.driver.save_screenshot(filename)
    # device.save_screenshot(filename)
    element = 获取控件对象(device, path)
    # 获取元素上下左右的位置
    left = element.location['x']
    top = element.location['y']
    right = element.location['x'] + element.size['width']
    bottom = element.location['y'] + element.size['height']
    # 打开刚才的截图
    im = Image.open(filename)
    # 截取对应位置
    im = im.crop((left, top, right, bottom))
    filename2 = 'pictures/handle/' + 'image_code.png'
    im.save(filename2)
    result = ocr_string(filename2)
    if os.path.exists(filename):
        os.remove(filename)
    return result


def 新债列表(device):
    try:
        import requests
        import json
        data_url = 'http://*************:8010/get_new_bond'
        r = requests.get(data_url)
        res = json.loads(r.text)

        if 'data' in res and len(res['data']) > 0:
            df = DataFrame(res['data'])
            df[['code']] = df[['code']].astype(str)
            df[['correcode']] = df[['correcode']].astype(str)
            df[['correcode_name']] = df[['correcode_name']].astype(str)
            df[['date']] = df[['date']].astype(str)
            df[['name']] = df[['name']].astype(str)
            df[['pricenew']] = df[['pricenew']].astype(float)
            df[['sub_code']] = df[['sub_code']].astype(str)
            df[['transfer_price']] = df[['transfer_price']].astype(float)

            return df
        else:
            df = DataFrame([])
            return df
    except Exception as e:
        df = DataFrame([])
        return df


def 获取短信验证码列表(device, mobile, before_time=3, content='', count=10, all_msg=False):
    import requests
    '''
    mobile:接收验证码的手机号
    before_time：获取几分钟之内的验证码消息
    content:验证码包含关键字
    count:返回验证码条数，默认10条
    all_msg:是否要获取所有短息内容，默认不需要 只获取识别出来的验证码
    '''
    url = 'http://*************:8010/get_msg_code'

    params = {
        'msg_mobile': mobile,
        'time_cell': before_time,
        'content': content,
        'count': count
    }
    res = []
    response = requests.get(url, params=params)
    if response.status_code == 200:
        response = requests.get(url, params=params).json()
        if response.get('data'):
            for i in response.get('data'):
                if all_msg:
                    res.append(i)
                else:
                    if i['code'] != "":
                        res.append(i['code'])
    return res


def noise_remove_cv2(image_name, k):
    import cv2
    import ddddocr
    from PIL import Image
    def calculate_noise_count(img_obj, w, h):
        count = 0
        width, height = img_obj.shape
        for _w_ in [w - 1, w, w + 1]:
            for _h_ in [h - 1, h, h + 1]:
                if _w_ > width - 1:
                    continue
                if _h_ > height - 1:
                    continue
                if _w_ == w and _h_ == h:
                    continue
                if img_obj[_w_, _h_] < 230:
                    count += 1
        return count

    img = cv2.imread(image_name, 1)
    gray_img = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    w, h = gray_img.shape
    for _w in range(w):
        for _h in range(h):
            if _w == 0 or _h == 0:
                gray_img[_w, _h] = 255
                continue
            pixel = gray_img[_w, _h]
            if pixel == 255:
                continue

            if calculate_noise_count(gray_img, _w, _h) < k:
                gray_img[_w, _h] = 255

    return gray_img


def ocr_string(path):
    import cv2
    import ddddocr
    from PIL import Image
    name = str(int(time.time())) + ".png"
    image = noise_remove_cv2(path, 4)
    cv2.imwrite(name, image)
    ocr = ddddocr.DdddOcr()
    with open(name, 'rb') as f:
        img_bytes = f.read()
    res = ocr.classification(img_bytes)
    if os.path.exists(name):
        os.remove(name)
    # if os.path.exists(path):
    #     os.remove(path)
    return res


def 识别图片文字及坐标(device):
    import random
    import base64
    import requests
    import os
    import json
    import time
    def cv2_to_base64(image):
        return base64.b64encode(image).decode('utf8')

    def isRequestApi(filename):
        try:
            # gpu服务器
            headers = {"Content-type": "application/json"}
            fo = open(filename, 'rb')
            img = fo.read()
            if img is None:
                print("error in loading image:{}".format(filename))
                return False
            server_url = ai_server_url
            data = {'images': [cv2_to_base64(img)]}
            fo.close()
            ai_response = requests.post(url=server_url, headers=headers, data=json.dumps(data))
            if str(ai_response.status_code) == '200':
                result = ai_response.json()["results"][0]
                lists = []
                for i in result:
                    dicts = {}
                    dicts['text'] = i['text']
                    x = int((i['text_region'][0][0] + i['text_region'][1][0]) / 2)
                    y = int((i['text_region'][0][1] + i['text_region'][2][1]) / 2)
                    dicts['site'] = (x, y)
                    lists.append(dicts)
                return lists
            return False
        except:
            try:
                from paddleocr import PaddleOCR
                ocr = PaddleOCR(use_angle_cls=True, lang='ch')
                result = ocr.ocr(filename, cls=True)
                res = []
                for i in result:
                    dicts = {}
                    dicts['text'] = i[-1][0]
                    x = int((i[0][0][0] + i[0][1][0]) / 2)
                    y = int((i[0][0][1] + i[0][2][1]) / 2)
                    dicts['site'] = (x, y)
                    res.append(dicts)
                if os.path.exists(filename):
                    os.remove(filename)
                return res
            except Exception as e:
                raise e

    filename = 'pictures/3060_ocr/' + "image_screen_" + str(random.randint(10, 99)) + ".png"
    # 截屏
    device.appium.driver.save_screenshot(filename)

    result = isRequestApi(filename)
    if os.path.exists(filename):
        os.remove(filename)
    return result


def 弹窗所在坐标(device, text):
    import random
    import base64
    import requests
    import os
    import json
    import time
    def cv2_to_base64(image):
        return base64.b64encode(image).decode('utf8')

    def isRequestApi(filename):
        try:
            # gpu服务器
            headers = {"Content-type": "application/json"}
            fo = open(filename, 'rb')
            img = fo.read()
            if img is None:
                print("error in loading image:{}".format(filename))
                return False
            server_url = ai_server_url
            data = {'images': [cv2_to_base64(img)]}
            fo.close()
            ai_response = requests.post(url=server_url, headers=headers, data=json.dumps(data))
            if str(ai_response.status_code) == '200':
                result = ai_response.json()["results"][0]
                lists = []
                for i in result:
                    dicts = {}
                    dicts['text'] = i['text']
                    x = int((i['text_region'][0][0] + i['text_region'][1][0]) / 2)
                    y = int((i['text_region'][0][1] + i['text_region'][2][1]) / 2)
                    dicts['site'] = (x, y)
                    lists.append(dicts)
                return lists
            return False
        except:
            try:
                from paddleocr import PaddleOCR
                ocr = PaddleOCR(use_angle_cls=True, lang='ch')
                result = ocr.ocr(filename, cls=True)
                res = []
                for i in result:
                    dicts = {}
                    dicts['text'] = i[-1][0]
                    x = int((i[0][0][0] + i[0][1][0]) / 2)
                    y = int((i[0][0][1] + i[0][2][1]) / 2)
                    dicts['site'] = (x, y)
                    res.append(dicts)
                if os.path.exists(filename):
                    os.remove(filename)
                return res
            except Exception as e:
                raise e

    filename = 'pictures/3060_ocr/' + "image_screen_" + str(random.randint(10, 99)) + ".png"
    # 截屏
    device.appium.driver.save_screenshot(filename)
    result = isRequestApi(filename)
    lists = []
    if result != []:
        for i in result:
            if text in i.get('text'):
                lists.append(i.get('site'))
    if os.path.exists(filename):
        os.remove(filename)
    try:
        return lists[-1]
    except:
        return []


def 获取乱序键盘(device, left, top, right, bottom):
    import base64
    from PIL import Image
    import json
    import requests
    import os
    import random
    def noise_remove_cv2(image_name, k):
        import cv2
        import ddddocr
        from PIL import Image
        def calculate_noise_count(img_obj, w, h):
            count = 0
            width, height = img_obj.shape
            for _w_ in [w - 1, w, w + 1]:
                for _h_ in [h - 1, h, h + 1]:
                    if _w_ > width - 1:
                        continue
                    if _h_ > height - 1:
                        continue
                    if _w_ == w and _h_ == h:
                        continue
                    if img_obj[_w_, _h_] < 230:
                        count += 1
            return count

        img = cv2.imread(image_name, 1)
        gray_img = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        w, h = gray_img.shape
        for _w in range(w):
            for _h in range(h):
                if _w == 0 or _h == 0:
                    gray_img[_w, _h] = 255
                    continue
                pixel = gray_img[_w, _h]
                if pixel == 255:
                    continue

                if calculate_noise_count(gray_img, _w, _h) < k:
                    gray_img[_w, _h] = 255

        return gray_img

    def ocr_string(path):
        import cv2
        import ddddocr
        from PIL import Image
        name = str(int(time.time())) + ".png"
        image = noise_remove_cv2(path, 4)
        cv2.imwrite(name, image)
        ocr = ddddocr.DdddOcr()
        with open(name, 'rb') as f:
            img_bytes = f.read()
        res = ocr.classification(img_bytes)
        if os.path.exists(name):
            os.remove(name)
        # if os.path.exists(path):
        #     os.remove(path)
        return res

    filename = 'pictures/handle/' + "image_screen_" + str(random.randint(10, 99)) + ".png"
    # 截屏
    device.appium.driver.save_screenshot(filename)
    # 打开刚才的截图
    im = Image.open(filename)
    # 截取对应位置
    im = im.crop((left, top, right, bottom))
    filename2 = 'image_code.png'
    im.save(filename2)
    result = ocr_string(filename2)
    print(result)
    return result


def 图片相似度对比(device, path, app_name, times):
    import os
    import time
    import cv2
    from PIL import Image
    app_platform = {
        "涨乐财富通": {
            "element": "com.lphtsccft:id/hq_chartlayout",
            "crop": [1080, 670],
            '方法': "hua_tai"
        },
        "财通证券": {
            "element": "com.hexin.plat.android.CaitongSecurity:id/fenshi",
            "crop": [1068, 610],
            '方法': "cai_tong"
        }
    }

    class RGB(object):

        def hua_tai(self, filename2, dir_path):
            img2 = Image.open(filename2)
            img2 = img2.convert('RGBA')
            pixdata = img2.load()
            for y in range(img2.size[1]):
                for x in range(img2.size[0]):
                    if 206 <= pixdata[x, y][0] <= 255 and 206 <= pixdata[x, y][1] <= 256 and pixdata[x, y][2] <= 256:
                        pixdata[x, y] = (255, 255, 255)
                    if 30 <= pixdata[x, y][0] <= 205 and 50 <= pixdata[x, y][1] <= 205 and pixdata[x, y][2] <= 256:
                        pixdata[x, y] = (255, 255, 255)
                    if 0 <= pixdata[x, y][0] <= 20 and pixdata[x, y][1] <= 256 and pixdata[x, y][2] <= 256:
                        pixdata[x, y] = (255, 255, 255)
            res_img = dir_path + '/' + str(int((time.time() * 10000))) + ".png"
            img2.save(res_img)
            return res_img

        def cai_tong(self, filename2, dir_path):
            from PIL import Image
            img2 = Image.open(filename2)
            img2 = img2.convert('RGBA')
            pixdata = img2.load()
            for y in range(img2.size[1]):
                for x in range(img2.size[0]):
                    if pixdata[x, y][0] > 200 and pixdata[x, y][1] <= 255 and pixdata[x, y][2] != 0:
                        pixdata[x, y] = (255, 255, 255)
                    if 0 <= pixdata[x, y][0] < 237 and pixdata[x, y][1] != 51 and pixdata[x, y][2] <= 256:
                        pixdata[x, y] = (255, 255, 255)
            res_img = dir_path + '/' + str(int((time.time() * 10000))) + ".png"
            img2.save(res_img)
            return res_img

    def crop(img_path, x, y, save_path):
        """
        裁剪图片
        """
        img = cv2.imread(img_path)  # img_path为图片所在路径
        crop_img = img[0:y, 0:x]  # x0,y0为裁剪区域左上坐标；x1,y1为裁剪区域右下坐标
        cv2.imwrite(save_path, crop_img)

    def calculate(image1, image2):
        hist1 = cv2.calcHist([image1], [0], None, [256], [0.0, 255.0])
        hist2 = cv2.calcHist([image2], [0], None, [256], [0.0, 255.0])
        # 计算直方图的重合度
        degree = 0
        for i in range(len(hist1)):
            if hist1[i] != hist2[i]:
                degree = degree + (1 - abs(hist1[i] - hist2[i]) / max(hist1[i], hist2[i]))
            else:
                degree = degree + 1
        degree = degree / len(hist1)
        return degree

    def classify_hist_with_split(image1, image2, size=(256, 256)):
        # 将图像resize后，分离为RGB三个通道，再计算每个通道的相似值
        image1 = cv2.resize(image1, size)
        image2 = cv2.resize(image2, size)
        sub_image1 = cv2.split(image1)
        sub_image2 = cv2.split(image2)
        sub_data = 0
        for im1, im2 in zip(sub_image1, sub_image2):
            sub_data += calculate(im1, im2)
        sub_data = sub_data / 3
        return sub_data

    def handle_rgb(device, path, app_name, num):
        _t = time.strftime('%Y/%m/%d')
        _t2 = time.strftime('%H_%M_%S')
        dir_path = 'log/video/%s/%s' % (_t, device.device_id.replace(':', ''))
        if not os.path.exists(dir_path):
            os.makedirs(dir_path)
        # localpath = '%s/%s.png' % (dir_path, _t2)
        device.screencap_history = list() if device.screencap_history is None else device.screencap_history

        filename = '%s/%s.png' % (dir_path, str(int((time.time() * 10000))))

        device.screencap_history.append(filename)

        if log_join == 'true':
            _name_flag = filename.split('/')[-2] + '/' + filename.split('/')[-1] if len(
                filename.split('/')) > 2 else filename
            device.log.info('页面截屏$$%s' % (_name_flag))

        # 截屏
        device.log.info("第{}张:开始截屏".format(num))
        device.appium.driver.save_screenshot(filename)
        # device.save_screenshot(filename)
        element = 获取控件对象(device, path)
        # 获取元素上下左右的位置
        left = element.location['x']
        top = element.location['y']
        right = element.location['x'] + element.size['width']
        bottom = element.location['y'] + element.size['height']
        # 打开刚才的截图
        im = Image.open(filename)
        # 截取对应位置
        device.log.info("第{}张:开始截取控件对应位置".format(num))
        im = im.crop((left, top, right, bottom))
        filename2 = '%s/%s.png' % (dir_path, str(int((time.time() * 10000))))

        imgs = str(int((time.time() * 10000))) + ".png"
        im.save(filename2)
        im.save(imgs)
        time.sleep(0.5)
        # 裁剪图片
        device.log.info("第{}张:开始裁剪".format(num))
        crop(imgs, app_platform[app_name]['crop'][0], app_platform[app_name]['crop'][1], filename2)
        # rgb去色
        time.sleep(0.5)
        rgb_img = RGB()
        res_img = getattr(rgb_img, app_platform[app_name]['方法'])(filename2, dir_path)
        device.screencap_history.append(res_img)

        if log_join == 'true':
            _name_flag = res_img.split('/')[-2] + '/' + res_img.split('/')[-1] if len(
                res_img.split('/')) > 2 else res_img
            device.log.info('图像裁剪$$%s' % (_name_flag))

        device.log.info("第{}张:rgb去色, 处理后图片名为{}".format(num, str(res_img).split('/')[-1]))
        if os.path.exists(filename):
            os.remove(filename)
        if os.path.exists(imgs):
            os.remove(imgs)
        if os.path.exists(filename2):
            os.remove(filename2)
        return res_img

    img1 = handle_rgb(device, path, app_name, 1)
    device.log.info("开始等待{}秒".format(times))
    time.sleep(times)
    img2 = handle_rgb(device, path, app_name, 2)
    # device.log.info('权限提示信息：%s' % str(ele))
    device.log.info(
        "图片名为{}和图片名为{},开始进行相似度比较".format(str(img1).split('/')[-1], str(img2).split('/')[-1]))
    n = classify_hist_with_split(cv2.imread(img1), cv2.imread(img2))
    device.log.info(
        "图片名为{}和图片名为{},相似度比较结果为{}".format(str(img1).split('/')[-1], str(img2).split('/')[-1], n))
    # if os.path.exists(img1):
    #     os.remove(img1)
    # if os.path.exists(img2):
    #     os.remove(img2)
    if n == 1.0:
        device.log.info(
            "图片名为{}和图片名为{},行情曲线没变动".format(str(img1).split('/')[-1], str(img2).split('/')[-1]))
        return n
    device.log.info("图片名为{}和图片名为{},行情曲线在变动".format(str(img1).split('/')[-1], str(img2).split('/')[-1]))
    return n[0]


def 海通执行前站点截屏(device):
    time.sleep(5)
    点击(device, '/e海通财/首页/搜索框')
    输入文本(device, '/e海通财/行情/自选/添加股票/股票代码输入框', '95553')
    time.sleep(1)
    截屏(device)
    device.appium.back()
    device.appium.back()


def 间隔截取图片(device, times, app_name):
    import os
    import time
    img_dir = app_name + "_" + time.strftime("%m-%d", time.localtime(int(time.time())))
    if not os.path.exists(img_dir):
        os.mkdir(img_dir)
    while True:
        timeArray = time.localtime(int(time.time()))
        filename = img_dir + "/" + time.strftime("%H_%M_%S", timeArray) + ".png"
        # 截屏
        device.appium.driver.save_screenshot(filename)
        print("截图文件路径为:{}".format(filename))
        time.sleep(times)


def 文字点击(device, ctx):
    import base64
    import os
    import requests
    import json
    import time
    try:
        import easyocr
    except Exception as e:
        return e

    def cv2_to_base64(image):
        return base64.b64encode(image).decode('utf8')

    def isRequestApi(filename):
        try:
            # gpu服务器
            headers = {"Content-type": "application/json"}
            fo = open(filename, 'rb')
            img = fo.read()
            if img is None:
                print("error in loading image:{}".format(filename))
                return False
            server_url = ai_server_url
            data = {'images': [cv2_to_base64(img)]}
            fo.close()
            ai_response = requests.post(url=server_url, headers=headers, data=json.dumps(data))
            if str(ai_response.status_code) == '200':
                result = ai_response.json()["results"][0]
                return result
            return False
        except:
            try:
                import easyocr
                reader = easyocr.Reader(['ch_sim', 'en'])
                result = reader.readtext(filename)
                return result
            except Exception as e:
                raise e

    filename = 'pictures/3060_ocr/' + str(int((time.time() * 10000))) + ".png"
    device.appium.driver.save_screenshot(filename)
    result = isRequestApi(filename)
    if os.path.exists(filename):
        os.remove(filename)
    for res in result:
        if 'text' in res:
            if ctx == res['text']:
                x0 = res['text_region'][0][0]
                y0 = res['text_region'][0][1]

                x_t = (res['text_region'][1][0] - res['text_region'][0][0]) / 2
                y_t = (res['text_region'][3][1] - res['text_region'][0][1]) / 2

                x = x0 + x_t
                y = y0 + y_t
                device.log.info('点击坐标(%s,%s)' % (x, y))
                device.appium.driver.tap([(x, y)])
                break
        else:
            if ctx == res[1]:
                x0 = res[0][0][0]
                y0 = res[0][0][1]

                x_t = (res[0][1][0] - res[0][0][0]) / 2
                y_t = (res[0][3][1] - res[0][0][1]) / 2

                x = x0 + x_t
                y = y0 + y_t
                device.log.info('点击坐标(%s,%s)' % (x, y))
                device.appium.driver.tap([(x, y)])
                break


def 坐标获取内容(device, position_l, position_r, show_log=True):
    import base64
    import os
    import requests
    import json
    import time
    try:
        import easyocr
        from PIL import Image
    except Exception as e:
        return e

    def cv2_to_base64(image):
        return base64.b64encode(image).decode('utf8')

    def isRequestApi(filename, show_log):
        # gpu服务器
        headers = {"Content-type": "application/json"}
        fo = open(filename, 'rb')
        img = fo.read()
        if img is None:
            print("error in loading image:{}".format(filename))
            return False
        server_url = ai_server_url
        data = {'images': [cv2_to_base64(img)]}
        fo.close()
        ai_response = requests.post(url=server_url, headers=headers, data=json.dumps(data))
        if str(ai_response.status_code) == '200':
            if "results" in ai_response.json() and len(ai_response.json()["results"]) > 0:
                result = ai_response.json()["results"][0]
                if show_log:
                    device.log.info('识别内容【%s】' % result)
                return result
            else:
                return False
        return False

    if len(position_l) != 2 or len(position_r) != 2:
        print('坐标参数错误！')

    time.sleep(1)
    w = device.appium.driver.get_window_size()['width']
    h = device.appium.driver.get_window_size()['height']

    # 左上角、右下角 两点尺寸比例坐标
    x_l_per = position_l[0]
    y_l_per = position_l[1]

    x_r_per = position_r[0]
    y_r_per = position_r[1]

    if x_l_per > 1 or y_l_per > 1 or x_r_per > 1 or y_r_per > 1:
        print('x或y的坐标超出当前分辨率！')
        return

    # 左上角、右下角 两点真实坐标
    x_l = x_l_per * w
    y_l = y_l_per * h
    x_r = x_r_per * w
    y_r = y_r_per * h

    # 识别区域上下左右位置
    left = x_l
    top = y_l
    right = x_r
    bottom = y_r

    filename = 'pictures/3060_ocr/' + str(int((time.time() * 10000))) + ".png"
    device.appium.driver.save_screenshot(filename)
    # 打开刚才的截图
    im = Image.open(filename)
    # 截取对应位置
    im = im.crop((left, top, right, bottom))
    filename2 = 'pictures/3060_ocr/' + str(int((time.time() * 10000))) + "_1.png"
    im.save(filename2)

    result = isRequestApi(filename2, show_log)
    im.close()
    if os.path.exists(filename):
        os.remove(filename)
    if os.path.exists(filename2):
        os.remove(filename2)
    return result


# 20240822
# 真实adb点击
def 真实点击(device, x, y):
    """
    :rtype:
    """
    time.sleep(0.5)
    p = subprocess.Popen('adb -s %s shell input tap %d %d' % (device.device_id, x, y), shell=True,
                         stdout=subprocess.PIPE)
    d = p.stdout.readline()
    device.log.info('坐标(%s,%s)点击 【成功】' % (x, y), '')


def 检测界面弹出层(device):
    try:
        import numpy, cv2
    except ImportError:
        # 如果导入失败，表示numpy未安装
        device.log.info("依赖模块未安装。")
        return False

    popup_window = device.check_pop()

    if popup_window:
        device.log.info('检测到异常弹窗')
        device.screencap()
        return True
    else:
        return False


def 检测弹窗(device):
    from libs.sdk import AI_POLICY, paddle_server, paddle_ocr, punctuation, TIP_KEYWORDS, get_location_online_pd, \
        get_location, get_prediction
    from libs.image_utils import get_proposal, get_binary_image, get_pos
    if AI_POLICY == 'true':
        from libs.sdk import cv2, selectivesearch
        from libs.all_config import SCALE, SIGMA, MIN_SIZE

        try:
            filename = device.device_id + str(int((time.time() * 10000))) + ".png"
            device.appium.driver.save_screenshot(filename)
            popup_window = device.check_pop()
            if popup_window:
                device.log.info('检测到异常弹窗')
                device.screencap()
                is_deal = False
                online_ai = False
                try:
                    result = paddle_server(filename)
                    online_ai = True
                except:
                    result = paddle_ocr.ocr(filename, cls=True)

                for res in result:
                    if online_ai:
                        if res['text'].strip(punctuation) in TIP_KEYWORDS or '知道了' in res['text'].strip(punctuation):
                            x, y = get_location_online_pd(res['text_region'])
                            device.log.info('自动识别元素【%s】,坐标(%s,%s)' % (res['text'].strip(punctuation), x, y))
                            is_deal = True
                            device.appium.driver.tap([(x, y)])
                            break
                    else:
                        if res[1][0] in TIP_KEYWORDS:
                            x, y = get_location(res)
                            device.log.info('自动识别元素【%s】,坐标(%s,%s)' % (res[1][0], x, y))
                            is_deal = True
                            device.appium.driver.tap([(x, y)])
                            break

                if not is_deal:
                    img = cv2.imread(filename)
                    scale = 500 / img.shape[1]
                    img = cv2.resize(img, (0, 0), fx=scale, fy=scale)
                    gray, binary = get_binary_image(img)
                    mg_lbl, regions = selectivesearch.selective_search(binary, SCALE, SIGMA, MIN_SIZE)
                    regions = get_proposal(regions, img.shape)
                    try:
                        rectangles, score_list = get_prediction(binary, regions)
                        if len(score_list) > 0:
                            score = round(max(score_list), 2)
                            rect = rectangles[score_list.index(max(score_list))]
                            position = get_pos(rect, scale)
                            x = position[0]
                            y = position[1]
                            device.log.info('自动识别关闭按钮,坐标(%s,%s)' % (x, y))
                            is_deal = True
                            device.appium.driver.tap([(x, y)])
                    except Exception as e:
                        device.log.info(e)

                if os.path.exists(filename):
                    os.remove(filename)

                if is_deal and device.check_pop():
                    time.sleep(1)

            else:
                if os.path.exists(filename):
                    os.remove(filename)
        except Exception as e:
            device.log.info(e)


def 请求接口(device, url, data, type='post'):
    import requests
    import json
    headers = {
        "Content-Type": "application/json; charset=UTF-8",
        "User-Agent": "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/67.0.3396.87 Safari/537.36",
    }

    try:
        if type == 'post':
            # msg=json.dumps(data, ensure_ascii=False, separators=(',', ':'), default=lambda x: str(x))
            req = requests.post(url, headers=headers, data=json.dumps(data))
        else:
            req = requests.get(url=url, params=data)
        return req
    except Exception as e:
        return e


def 智能点击(device, ctx):
    import os
    import time
    try:
        from PIL import Image
    except Exception as e:
        return e
    # ctx规则:
    # 1.直接点击文字，例：交易
    # 2.方位|文字，例：底部|交易；支持【左侧 右侧 顶部 底部 中间】
    # 3.相对定位|方位|文字，例：打新股|下方|交易；支持【上方 下方 左方 右方】
    ctx_list = ctx.split('|')
    filename = 'pictures/3060_ocr/' + str(int((time.time() * 10000))) + ".png"
    device.appium.driver.save_screenshot(filename)
    img_f = Image.open(filename)
    w = img_f.width  # 图片的宽
    h = img_f.height  # 图片的高
    img_f.close()

    result = []
    try:
        # gpu服务器
        import requests
        import json
        import base64

        def cv2_to_base64(image):
            return base64.b64encode(image).decode('utf8')

        headers = {"Content-type": "application/json"}
        fo = open(filename, 'rb')
        img = fo.read()
        if img is None:
            print("error in loading image:{}".format(filename))
            return False

        data = {'images': [cv2_to_base64(img)]}
        ai_response = requests.post(url=ai_server_url, headers=headers, data=json.dumps(data))
        fo.close()
        if str(ai_response.status_code) == '200':
            result = ai_response.json()["results"][0]
        else:
            print('识别失败')
            return False
    except Exception as e:
        try:
            import easyocr
            from paddleocr import PaddleOCR

            # 本地飞桨
            ocr = PaddleOCR(use_angle_cls=True, lang='ch')
            result = ocr.ocr(filename, cls=True)
        except Exception as e:
            return e

    def get_location_pd(res):
        x0 = res[0][0]
        y0 = res[0][1]

        x_t = (res[1][0] - res[0][0]) / 2
        y_t = (res[3][1] - res[0][1]) / 2

        x = x0 + x_t
        y = y0 + y_t
        return x, y

    punctuation = r"""!"#$%&'()*+,-./:;<=>?@[\]^_`{|}~，。、丶；‘’：“”"""

    if len(result) > 0:
        if len(ctx_list) == 1:
            for res in result:
                if ctx_list[0] == res['text'].strip(punctuation):
                    x, y = get_location_pd(res['text_region'])
                    device.log.info('点击【%s】,坐标(%s,%s)' % (ctx_list[0], x, y))
                    if int(y) < 2288:
                        device.appium.driver.tap([(x, y)])
                    else:
                        device.appium.driver.tap([(x, y - 40)])
                    break
        elif len(ctx_list) == 2:
            for res in result:
                if ctx_list[1] == res[1][0]:
                    x, y = get_location_pd(res['text_region'])
                    if ctx_list[0] == '左侧' and x < w / 2:
                        device.log.info('点击左侧【%s】,坐标(%s,%s)' % (ctx_list[1], x, y))
                        if int(y) < 2288:
                            device.appium.driver.tap([(x, y)])
                        else:
                            device.appium.driver.tap([(x, y - 40)])
                        break
                    elif ctx_list[0] == '右侧' and x > w / 2:
                        device.log.info('点击右侧【%s】,坐标(%s,%s)' % (ctx_list[1], x, y))
                        if int(y) < 2288:
                            device.appium.driver.tap([(x, y)])
                        else:
                            device.appium.driver.tap([(x, y - 40)])
                        break
                    elif ctx_list[0] == '顶部' and y < h / 4:
                        device.log.info('点击顶部【%s】,坐标(%s,%s)' % (ctx_list[1], x, y))
                        if int(y) < 2288:
                            device.appium.driver.tap([(x, y)])
                        else:
                            device.appium.driver.tap([(x, y - 40)])
                        break
                    elif ctx_list[0] == '底部' and y > (3 * h / 4):
                        device.log.info('点击底部【%s】,坐标(%s,%s)' % (ctx_list[1], x, y))
                        if int(y) < 2288:
                            device.appium.driver.tap([(x, y)])
                        else:
                            device.appium.driver.tap([(x, y - 40)])
                        break
                    elif ctx_list[0] == '中间' and y > h / 4 and y < (3 * h / 4):
                        device.log.info('点击中间【%s】,坐标(%s,%s)' % (ctx_list[1], x, y))
                        if int(y) < 2288:
                            device.appium.driver.tap([(x, y)])
                        else:
                            device.appium.driver.tap([(x, y - 40)])
                        break
        elif len(ctx_list) == 3:
            # ctx_list[0] 相对元素
            # ctx_list[1] 方位
            # ctx_list[2] 点击元素

            relative_x = -1
            relative_y = -1
            for res in result:
                # 获取相对元素的坐标
                if ctx_list[0] == res['text'].strip().strip(punctuation):
                    relative_x, relative_y = get_location_pd(res['text_region'])
                    device.log.info('相对元素【%s】,坐标(%s,%s)' % (ctx_list[0], relative_x, relative_x))
                    break

            if relative_x > 0 and relative_y > 0:
                for res in result:
                    if ctx_list[2] == res['text'].strip().strip(punctuation):
                        x, y = get_location_pd(res['text_region'])
                        if ctx_list[1] == '左方' and x < relative_x:
                            device.log.info('点击【%s】左方【%s】,坐标(%s,%s)' % (ctx_list[0], ctx_list[2], x, y))
                            if int(y) < 2288:
                                device.appium.driver.tap([(x, y)])
                            else:
                                device.appium.driver.tap([(x, y - 40)])
                            break
                        elif ctx_list[1] == '右方' and x > relative_x:
                            device.log.info('点击【%s】右方【%s】,坐标(%s,%s)' % (ctx_list[0], ctx_list[2], x, y))
                            if int(y) < 2288:
                                device.appium.driver.tap([(x, y)])
                            else:
                                device.appium.driver.tap([(x, y - 40)])
                            break
                        elif ctx_list[1] == '上方' and y < relative_y:
                            device.log.info('点击【%s】上方【%s】,坐标(%s,%s)' % (ctx_list[0], ctx_list[2], x, y))
                            if int(y) < 2288:
                                device.appium.driver.tap([(x, y)])
                            else:
                                device.appium.driver.tap([(x, y - 40)])
                            break
                        elif ctx_list[1] == '下方' and y > relative_y:
                            device.log.info('点击【%s】下方【%s】,坐标(%s,%s)' % (ctx_list[0], ctx_list[2], x, y))
                            if int(y) < 2288:
                                device.appium.driver.tap([(x, y)])
                            else:
                                device.appium.driver.tap([(x, y - 40)])
                            break
            else:
                for res in result:
                    if ctx_list[2] == res['text'].strip().strip(punctuation):
                        x, y = get_location_pd(res['text_region'])
                        device.log.info('点击【%s】,坐标(%s,%s)' % (ctx_list[2], x, y))
                        if int(y) < 2288:
                            device.appium.driver.tap([(x, y)])
                        else:
                            device.appium.driver.tap([(x, y - 40)])
                        break
    if os.path.exists(filename):
        os.remove(filename)


def 模糊智能点击(device, ctx):
    import os
    import time
    try:
        from PIL import Image
    except Exception as e:
        return e
    # ctx规则:
    # 1.直接点击文字，例：交易
    # 2.方位|文字，例：底部|交易；支持【左侧 右侧 顶部 底部 中间】
    # 3.相对定位|方位|文字，例：打新股|下方|交易；支持【上方 下方 左方 右方】
    ctx_list = ctx.split('|')
    filename = 'pictures/3060_ocr/' + str(int((time.time() * 10000))) + ".png"
    device.appium.driver.save_screenshot(filename)
    img_f = Image.open(filename)
    w = img_f.width  # 图片的宽
    h = img_f.height  # 图片的高
    img_f.close()

    result = []
    try:
        # gpu服务器
        import requests
        import json
        import base64

        def cv2_to_base64(image):
            return base64.b64encode(image).decode('utf8')

        headers = {"Content-type": "application/json"}
        fo = open(filename, 'rb')
        img = fo.read()
        if img is None:
            print("error in loading image:{}".format(filename))
            return False

        data = {'images': [cv2_to_base64(img)]}
        ai_response = requests.post(url=ai_server_url, headers=headers, data=json.dumps(data))
        fo.close()
        if str(ai_response.status_code) == '200':
            result = ai_response.json()["results"][0]
        else:
            print('识别失败')
            return False
    except Exception as e:
        raise e
        # try:
        #     import easyocr
        #     from paddleocr import PaddleOCR
        #
        #     # 本地飞桨
        #     ocr = PaddleOCR(use_angle_cls=True, lang='ch')
        #     result = ocr.ocr(filename, cls=True)
        # except Exception as e:
        #     return e

    def get_location_pd(res):
        x0 = res[0][0]
        y0 = res[0][1]

        x_t = (res[1][0] - res[0][0]) / 2
        y_t = (res[3][1] - res[0][1]) / 2

        x = x0 + x_t
        y = y0 + y_t
        return x, y

    punctuation = r"""!"#$%&'()*+,-./:;<=>?@[\]^_`{|}~，。、丶；‘’：“”"""

    if len(result) > 0:
        if len(ctx_list) == 1:
            for res in result:
                if ctx_list[0] in res['text'].strip(punctuation):
                    x, y = get_location_pd(res['text_region'])
                    device.log.info('点击【%s】,坐标(%s,%s)' % (ctx_list[0], x, y))
                    if int(y) < 2288:
                        device.appium.driver.tap([(x, y)])
                    else:
                        device.appium.driver.tap([(x, y - 40)])
        elif len(ctx_list) == 2:
            for res in result:
                if ctx_list[1] in res[1][0]:
                    x, y = get_location_pd(res['text_region'])
                    if ctx_list[0] == '左侧' and x < w / 2:
                        device.log.info('点击左侧【%s】,坐标(%s,%s)' % (ctx_list[1], x, y))
                        if int(y) < 2288:
                            device.appium.driver.tap([(x, y)])
                        else:
                            device.appium.driver.tap([(x, y - 40)])
                        break
                    elif ctx_list[0] == '右侧' and x > w / 2:
                        device.log.info('点击右侧【%s】,坐标(%s,%s)' % (ctx_list[1], x, y))
                        if int(y) < 2288:
                            device.appium.driver.tap([(x, y)])
                        else:
                            device.appium.driver.tap([(x, y - 40)])
                        break
                    elif ctx_list[0] == '顶部' and y < h / 4:
                        device.log.info('点击顶部【%s】,坐标(%s,%s)' % (ctx_list[1], x, y))
                        if int(y) < 2288:
                            device.appium.driver.tap([(x, y)])
                        else:
                            device.appium.driver.tap([(x, y - 40)])
                        break
                    elif ctx_list[0] == '底部' and y > (3 * h / 4):
                        device.log.info('点击底部【%s】,坐标(%s,%s)' % (ctx_list[1], x, y))
                        if int(y) < 2288:
                            device.appium.driver.tap([(x, y)])
                        else:
                            device.appium.driver.tap([(x, y - 40)])
                        break
                    elif ctx_list[0] == '中间' and y > h / 4 and y < (3 * h / 4):
                        device.log.info('点击中间【%s】,坐标(%s,%s)' % (ctx_list[1], x, y))
                        if int(y) < 2288:
                            device.appium.driver.tap([(x, y)])
                        else:
                            device.appium.driver.tap([(x, y - 40)])
                        break
        elif len(ctx_list) == 3:
            # ctx_list[0] 相对元素
            # ctx_list[1] 方位
            # ctx_list[2] 点击元素

            relative_x = -1
            relative_y = -1
            for res in result:
                # 获取相对元素的坐标
                if ctx_list[0] in res['text'].strip().strip(punctuation):
                    relative_x, relative_y = get_location_pd(res['text_region'])
                    device.log.info('相对元素【%s】,坐标(%s,%s)' % (ctx_list[0], relative_x, relative_x))
                    break

            if relative_x > 0 and relative_y > 0:
                for res in result:
                    if ctx_list[2] in res['text'].strip().strip(punctuation):
                        x, y = get_location_pd(res['text_region'])
                        if ctx_list[1] == '左方' and x < relative_x:
                            device.log.info('点击【%s】左方【%s】,坐标(%s,%s)' % (ctx_list[0], ctx_list[2], x, y))
                            if int(y) < 2288:
                                device.appium.driver.tap([(x, y)])
                            else:
                                device.appium.driver.tap([(x, y - 40)])
                            break
                        elif ctx_list[1] == '右方' and x > relative_x:
                            device.log.info('点击【%s】右方【%s】,坐标(%s,%s)' % (ctx_list[0], ctx_list[2], x, y))
                            if int(y) < 2288:
                                device.appium.driver.tap([(x, y)])
                            else:
                                device.appium.driver.tap([(x, y - 40)])
                            break
                        elif ctx_list[1] == '上方' and y < relative_y:
                            device.log.info('点击【%s】上方【%s】,坐标(%s,%s)' % (ctx_list[0], ctx_list[2], x, y))
                            if int(y) < 2288:
                                device.appium.driver.tap([(x, y)])
                            else:
                                device.appium.driver.tap([(x, y - 40)])
                            break
                        elif ctx_list[1] == '下方' and y > relative_y:
                            device.log.info('点击【%s】下方【%s】,坐标(%s,%s)' % (ctx_list[0], ctx_list[2], x, y))
                            if int(y) < 2288:
                                device.appium.driver.tap([(x, y)])
                            else:
                                device.appium.driver.tap([(x, y - 40)])
                            break
            else:
                for res in result:
                    if ctx_list[2] in res['text'].strip().strip(punctuation):
                        x, y = get_location_pd(res['text_region'])
                        device.log.info('点击【%s】,坐标(%s,%s)' % (ctx_list[2], x, y))
                        if int(y) < 2288:
                            device.appium.driver.tap([(x, y)])
                        else:
                            device.appium.driver.tap([(x, y - 40)])
                        break
    if os.path.exists(filename):
        os.remove(filename)


# 20240822
# 真实adb点击
def 真实点击(device, x, y):
    """
    :rtype:
    """
    time.sleep(0.5)
    p = subprocess.Popen('adb -s %s shell input tap %d %d' % (device.device_id, x, y), shell=True,
                         stdout=subprocess.PIPE)
    d = p.stdout.readline()
    device.log.info('坐标(%s,%s)点击 【成功】' % (x, y), '')


def 输入英文数字(device, text):
    drice_id = device.device_id
    p = subprocess.Popen('adb -s %s shell input text %s' % (drice_id, text), shell=True, stdout=subprocess.PIPE)
    d = p.stdout.readline()
    return d.strip().decode()


def 输入中文数字(device, text):
    # 纯adb操作
    drice_id = device.device_id
    p = subprocess.Popen('adb -s %s shell ime set com.android.adbkeyboard/.AdbIME' % (drice_id), shell=True,
                         stdout=subprocess.PIPE)
    time.sleep(0.5)
    p = subprocess.Popen('adb -s %s shell am broadcast -a ADB_INPUT_TEXT --es msg %s' % (drice_id, text), shell=True,
                         stdout=subprocess.PIPE)
    device.log.info('输入中文%s 成功 ' % (text))
    d = p.stdout.readline()
    return d.strip().decode()


def 华泰虚线判断(device, path):
    from PIL import Image
    import cv2
    import os
    import time
    import random
    def mycrop(img_path, x1, x2, y1, y2, save_path):
        img = cv2.imread(img_path)  # img_path为图片所在路径
        crop_img = img[y1:y2, x1:x2]  # x0,y0为裁剪区域左上坐标；x1,y1为裁剪区域右下坐标
        cv2.imwrite(save_path, crop_img)

    filename = 'pictures/handle/' + "image_screen_" + str(random.randint(10, 99)) + ".png"
    # 截屏
    device.appium.driver.save_screenshot(filename)
    # device.save_screenshot(filename)
    element = 获取控件对象(device, path)
    # 获取元素上下左右的位置
    left = element.location['x']
    top = element.location['y']
    right = element.location['x'] + element.size['width']
    bottom = element.location['y'] + element.size['height']
    # 打开刚才的截图
    im = Image.open(filename)
    # 截取
    im = im.crop((left, top, right, bottom))
    filename2 = 'pictures/handle/' + str(int((time.time() * 10000))) + ".png"
    imgs = 'pictures/handle/' + str(int((time.time() * 10000))) + ".png"
    im.save(imgs)
    time.sleep(0.5)
    device.log.info('开始截取柱状图最后一个柱子')
    mycrop(imgs, 1065, 1078, 780, 915, filename2)
    img2 = Image.open(filename2)
    img2 = img2.convert('RGBA')
    pixdata = img2.load()
    i = 0
    for y in range(img2.size[1]):
        for x in range(img2.size[0]):
            if 140 <= pixdata[x, y][0] <= 160 and 140 <= pixdata[x, y][1] <= 160 and 140 <= pixdata[x, y][2] <= 160:
                i += 1
    device.log.info("灰色像素点有{}个".format(i))
    img2.close()
    if os.path.exists(filename):
        os.remove(filename)
    if os.path.exists(imgs):
        os.remove(imgs)
    if os.path.exists(filename2):
        os.remove(filename2)
    if i > 10:
        device.log.info('有虚线')
        return True
    return False


def 比较图片相似度值(device, path, second):
    from PIL import Image
    import cv2
    def classify_hist_with_split(image1, image2, size=(256, 256)):
        # 将图像resize后，分离为RGB三个通道，再计算每个通道的相似值
        image1 = cv2.resize(image1, size)
        image2 = cv2.resize(image2, size)
        sub_image1 = cv2.split(image1)
        sub_image2 = cv2.split(image2)
        sub_data = 0
        for im1, im2 in zip(sub_image1, sub_image2):
            sub_data += calculate(im1, im2)
        sub_data = sub_data / 3
        return sub_data

    def calculate(image1, image2):
        hist1 = cv2.calcHist([image1], [0], None, [256], [0.0, 255.0])
        hist2 = cv2.calcHist([image2], [0], None, [256], [0.0, 255.0])
        # 计算直方图的重合度
        degree = 0
        for i in range(len(hist1)):
            if hist1[i] != hist2[i]:
                degree = degree + (1 - abs(hist1[i] - hist2[i]) / max(hist1[i], hist2[i]))
            else:
                degree = degree + 1
        degree = degree / len(hist1)
        return degree

    _t = time.strftime('%Y/%m/%d')
    _t2 = time.strftime('%H_%M_%S')
    dir_path = 'log/video/%s/%s' % (_t, device.device_id.replace(':', ''))
    if not os.path.exists(dir_path):
        os.makedirs(dir_path)

    device.screencap_history = list() if device.screencap_history is None else device.screencap_history
    filename1 = '%s/%s.png' % (dir_path, str(int((time.time() * 10000))))
    # 截屏
    device.log.info("第1张:开始截屏")
    device.appium.driver.save_screenshot(filename1)
    device.screencap_history.append(filename1)

    if log_join == 'true':
        _name_flag = filename1.split('/')[-2] + '/' + filename1.split('/')[-1] if len(
            filename1.split('/')) > 2 else filename1
        device.log.info('截屏图像$$%s' % (_name_flag))

    # device.save_screenshot(filename)
    element = 获取控件对象(device, path)
    # 获取元素上下左右的位置
    left = element.location['x']
    top = element.location['y']
    right = element.location['x'] + element.size['width']
    bottom = element.location['y'] + element.size['height']
    # 打开刚才的截图
    im = Image.open(filename1)
    # 截取
    device.log.info("第1张:开始裁剪K线图")
    im = im.crop((left, top, right, bottom))
    img1 = dir_path + '/' + str(int((time.time() * 10000))) + ".png"
    im.save(img1)
    device.screencap_history.append(img1)

    if log_join == 'true':
        _name_flag = img1.split('/')[-2] + '/' + img1.split('/')[-1] if len(img1.split('/')) > 2 else img1
        device.log.info('裁剪K线图$$%s' % (_name_flag))

    device.log.info("开始等待{}秒".format(second))
    time.sleep(second)
    filename2 = '%s/%s.png' % (dir_path, str(int((time.time() * 10000))))
    # 截屏
    device.log.info("第2张:开始截屏")
    device.appium.driver.save_screenshot(filename2)
    device.screencap_history.append(filename2)

    if log_join == 'true':
        _name_flag = filename2.split('/')[-2] + '/' + filename2.split('/')[-1] if len(
            filename2.split('/')) > 2 else filename2
        device.log.info('截屏图像$$%s' % (_name_flag))

    # device.save_screenshot(filename)
    element = 获取控件对象(device, path)
    # 获取元素上下左右的位置
    left = element.location['x']
    top = element.location['y']
    right = element.location['x'] + element.size['width']
    bottom = element.location['y'] + element.size['height']
    # 打开刚才的截图
    im = Image.open(filename2)
    # 截取
    device.log.info("第2张:开始裁剪K线图")
    im = im.crop((left, top, right, bottom))
    img2 = '%s/%s.png' % (dir_path, str(int((time.time() * 10000))))
    im.save(img2)
    device.screencap_history.append(img2)

    if log_join == 'true':
        _name_flag = img2.split('/')[-2] + '/' + img2.split('/')[-1] if len(img2.split('/')) > 2 else img2
        device.log.info('裁剪K线图$$%s' % (_name_flag))

    n = classify_hist_with_split(cv2.imread(img1), cv2.imread(img2))
    device.log.info("相似度值为：{}".format(n))
    if os.path.exists(filename1):
        os.remove(filename1)
    if os.path.exists(filename2):
        os.remove(filename2)
    # if os.path.exists(img1):
    #     os.remove(img1)
    # if os.path.exists(img2):
    #     os.remove(img2)
    if n == 1.0:
        return n
    return n[0]


def 比较图片相似度值非自动(device, image1, image2, size=(256, 256)):
    from PIL import Image
    import cv2
    """
    将图像resize后，分离为RGB三个通道，再计算每个通道的相似值
    """
    # 将图像resize后，分离为RGB三个通道
    image1 = cv2.resize(image1, size)
    image2 = cv2.resize(image2, size)
    sub_image1 = cv2.split(image1)
    sub_image2 = cv2.split(image2)

    sub_data = 0

    for im1, im2 in zip(sub_image1, sub_image2):
        # 计算直方图
        hist1 = cv2.calcHist([im1], [0], None, [256], [0.0, 255.0])
        hist2 = cv2.calcHist([im2], [0], None, [256], [0.0, 255.0])

        # 计算直方图的重合度
        degree = 0
        for i in range(len(hist1)):
            if hist1[i] != hist2[i]:
                degree += (1 - abs(hist1[i] - hist2[i]) / max(hist1[i], hist2[i]))
            else:
                degree += 1
        degree /= len(hist1)

        sub_data += degree

    sub_data /= 3
    return sub_data


def 开启监控悬浮窗(device):
    from appium.webdriver.common.touch_action import TouchAction
    device.has_element('/涨乐财富通/8.09/行情界面跳转按钮')()
    device.has_element('/涨乐财富通/行情/搜索按钮')()
    # device.has_element('/涨乐财富通/通用/首页/综合搜索按钮')()
    device['/涨乐财富通/通用/首页/综合搜索/搜索框'] = '.httest'
    time.sleep(1)
    x = device.screen_width
    y = device.screen_height
    x1 = int(x * 0.5)
    x2 = int(x * 0.5)
    y1 = int(y * 0.8)
    y2 = int(y * 0.4)
    device.appium.driver.swipe(x1, y1, x2, y2, 3500)
    device.has_element('/涨乐财富通/开发后门/link')()
    device.has_element('/涨乐财富通/开发后门/link/打开请求监控悬浮框')()
    device.appium.back()
    device.appium.back()
    device.appium.back()
    device.has_element('/涨乐财富通/打开请求监控悬浮框/最小化悬浮窗')()
    ele = 获取控件对象(device, '/涨乐财富通/打开请求监控悬浮框/悬浮窗')
    # ele = device.has_element('/涨乐财富通/打开请求监控悬浮框/悬浮窗')
    TouchAction(device.appium.driver).press(ele).move_to(x=947, y=1200).release().perform()


def 判断图片是否存在(device, small_img, big_img):
    import cv2
    import numpy as np
    # 找图 返回最近似的点
    def search_returnPoint(img, template, template_size):
        img_gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        template_ = cv2.cvtColor(template, cv2.COLOR_BGR2GRAY)
        result = cv2.matchTemplate(img_gray, template_, cv2.TM_CCOEFF_NORMED)
        threshold = 0.7
        # res大于70%
        loc = np.where(result >= threshold)
        # 使用灰度图像中的坐标对原始RGB图像进行标记
        point = ()
        for pt in zip(*loc[::-1]):
            cv2.rectangle(img, pt, (pt[0] + template_size[1], pt[1] + + template_size[0]), (7, 249, 151), 2)
            point = pt
        if point == ():
            return None, None, None
        return img, point[0] + template_size[1] / 2, point[1]

    scale = 1

    img = cv2.imread(big_img)  # 要找的大图
    img = cv2.resize(img, (0, 0), fx=scale, fy=scale)

    template = cv2.imread(small_img)  # 图中的小图
    template = cv2.resize(template, (0, 0), fx=scale, fy=scale)
    template_size = template.shape[:2]
    img, x_, y_ = search_returnPoint(img, template, template_size)

    if (img is None):
        return False
    return True


def 屏幕截图(device):
    import time
    filename = 'pictures/handle/' + '%s.png' % (str(int((time.time() * 10000))))
    device.appium.driver.save_screenshot(filename)
    return filename


def 控件截图(device, path):
    from PIL import Image
    import time
    import os
    filename = 'pictures/handle/' + '%s.png' % (str(int((time.time() * 10000))))
    device.appium.driver.save_screenshot(filename)

    element = 获取控件对象(device, path)
    # 获取元素上下左右的位置
    left = element.location['x']
    top = element.location['y']
    right = element.location['x'] + element.size['width']
    bottom = element.location['y'] + element.size['height']
    # 打开刚才的截图
    im = Image.open(filename)
    # 截取
    im = im.crop((left, top, right, bottom))
    img = 'pictures/handle/' + str(int((time.time() * 10000))) + ".png"
    im.save(img)
    if os.path.exists(filename):
        os.remove(filename)
    return img


def 坐标截取(device, position_l, position_r):
    from PIL import Image
    import time
    if len(position_l) != 2 or len(position_r) != 2:
        print('坐标参数错误！')

    time.sleep(1)
    w = device.appium.driver.get_window_size()['width']
    h = device.appium.driver.get_window_size()['height']

    # 左上角、右下角 两点尺寸比例坐标
    x_l_per = position_l[0]
    y_l_per = position_l[1]

    x_r_per = position_r[0]
    y_r_per = position_r[1]

    if x_l_per > 1 or y_l_per > 1 or x_r_per > 1 or y_r_per > 1:
        print('x或y的坐标超出当前分辨率！')
        return

    # 左上角、右下角 两点真实坐标
    x_l = x_l_per * w
    y_l = y_l_per * h
    x_r = x_r_per * w
    y_r = y_r_per * h

    # 识别区域上下左右位置
    left = x_l
    top = y_l
    right = x_r
    bottom = y_r

    filename = 'pictures/handle/' + str(int((time.time() * 10000))) + ".png"
    device.appium.driver.save_screenshot(filename)
    # 打开刚才的截图
    im = Image.open(filename)
    # 截取对应位置
    im = im.crop((left, top, right, bottom))
    filename2 = 'pictures/handle/' + str(int((time.time() * 10000))) + "_1.png"
    im.save(filename2)
    if os.path.exists(filename):
        os.remove(filename)
    return filename2


def 返回首页(device):
    首页 = ''
    while 首页 == '':
        首页 = 获取控件对象(device, '/浙商汇金谷/9.0/首页界面')
        if 首页 == '':
            device.appium.driver.back()
            print('返回上一级！')
        else:
            点击(device, '/浙商汇金谷/9.0/首页界面')


def 返回首创首页(device):
    首页 = ''
    while 首页 == '':
        首页 = 获取控件对象(device, '/首创番茄财富/4.0/首页界面')
        if 首页 == '':
            device.appium.driver.back()
            print('返回上一级！')
        else:
            点击(device, '/首创番茄财富/4.0/首页界面')


def 控件长按(device, path, second):
    from appium.webdriver.common.touch_action import TouchAction
    el = 获取控件对象(device, path)
    TouchAction(device.appium.driver).long_press(el).perform().wait(second * 1000)


def 自定义控件滑动(device, 控件位置, 起始点百分比, 结束点百分比, 滑动持续时间, 类型=True):
    """
    类型:默认横向滑动
    传类型=False 为纵向滑动
    """
    对象位置和手机分辨率 = 获取对象位置和手机分辨率(device, 控件位置)
    if 类型:
        y = int(对象位置和手机分辨率[0]['y']) + 10
        x1 = int(对象位置和手机分辨率[1]['width'] * 起始点百分比)
        x2 = int(对象位置和手机分辨率[1]['width'] * 结束点百分比)
        device.appium.driver.swipe(x1, y, x2, y, 滑动持续时间)
        device.log.info("横向滑动距离为:{}".format(int(x1 - x2)))
    else:
        x = int(对象位置和手机分辨率[0]['x'] + 10)
        y1 = int(对象位置和手机分辨率[1]['height'] * 起始点百分比)
        y2 = int(对象位置和手机分辨率[1]['height'] * 结束点百分比)
        device.appium.driver.swipe(x, y1, x, y2, 滑动持续时间)
        device.log.info("纵向滑动距离为:{}".format(int(y1 - y2)))


def 是否存在生僻字乱码(device, text):
    try:
        text.encode("gb2312")
    except UnicodeEncodeError:
        return True
    return False


def 控件图片是否存在(device, path):
    has_image, location = device.has_element_image(path)
    if has_image:
        device.log.info('控件图片存在<%s> 成功，坐标(%s,%s)。' % (path, str(location[0]), str(location[1])))
    else:
        device.log.info('未匹配到控件图片<%s> 。' % path)
    return has_image


def 点击控件图片(device, path):
    """
    small_img 控件小图路径
    """

    has_image, location = device.has_element_image(path)

    if has_image:
        x = float(location[0])
        y = float(location[1])
        device.appium.driver.tap([(x, y)])
        device.log.info('点击控件图片<%s> 成功。' % path)
    else:
        device.log.info('未匹配到控件图片<%s> 。' % path)


def 获取实时行情(device, code):
    import requests
    params = {
        'code': code
    }
    response = requests.get(url=current_market_url, params=params, timeout=20).json()
    device.log.info("数据来源【dfcf】:{}".format(response))
    return response


def 获取实时指数(device, code):
    import requests
    params = {
        'code': code
    }
    response = requests.get(url=current_index_url, params=params, timeout=20).json()
    device.log.info("数据来源【dfcf】:{}".format(response.get('data')))
    return response.get('data')


def 自定义图片比较(device, img1, img2):
    # 计算单通道的直方图的相似值
    import cv2
    def calculate(image1, image2):
        import cv2
        hist1 = cv2.calcHist([image1], [0], None, [256], [0.0, 255.0])
        hist2 = cv2.calcHist([image2], [0], None, [256], [0.0, 255.0])
        # 计算直方图的重合度
        degree = 0
        for i in range(len(hist1)):
            if hist1[i] != hist2[i]:
                degree = degree + (1 - abs(hist1[i] - hist2[i]) / max(hist1[i], hist2[i]))
            else:
                degree = degree + 1
        degree = degree / len(hist1)
        return degree

    # 通过得到RGB每个通道的直方图来计算相似度
    def classify_hist_with_split(image1, image2, size=(256, 256)):
        # 将图像resize后，分离为RGB三个通道，再计算每个通道的相似值
        import cv2
        image1 = cv2.resize(image1, size)
        image2 = cv2.resize(image2, size)
        sub_image1 = cv2.split(image1)
        sub_image2 = cv2.split(image2)
        sub_data = 0
        for im1, im2 in zip(sub_image1, sub_image2):
            sub_data += calculate(im1, im2)
        sub_data = sub_data / 3
        return sub_data

    n = classify_hist_with_split(cv2.imread(img1), cv2.imread(img2))
    if n == 1.0:
        return n
    return n[0]


def 判断区域内是否存在文字(device, left, top, right, bottom):
    import random
    import base64
    import requests
    import os
    from PIL import Image
    import json
    import time

    def cv2_to_base64(image):
        return base64.b64encode(image).decode('utf8')

    def isRequestApi(filename):
        try:
            # gpu服务器
            headers = {"Content-type": "application/json"}
            fo = open(filename, 'rb')
            img = fo.read()
            if img is None:
                print("error in loading image:{}".format(filename))
                return False
            server_url = ai_server_url
            data = {'images': [cv2_to_base64(img)]}
            fo.close()
            ai_response = requests.post(url=server_url, headers=headers, data=json.dumps(data))
            if str(ai_response.status_code) == '200':
                result = ai_response.json()["results"][0]
                return result
            return False
        except:
            try:
                import easyocr
                reader = easyocr.Reader(['ch_sim', 'en'])
                result = reader.readtext(filename, detail=0)
                return result
            except Exception as e:
                raise e

    filename = 'pictures/3060_ocr/' + "image_screen_" + str(random.randint(10, 99)) + ".png"
    # 截屏
    device.appium.driver.save_screenshot(filename)
    # 打开刚才的截图
    im = Image.open(filename)
    # 截取对应位置
    im = im.crop((left, top, right, bottom))
    # filename2 = 'image_copy.png'
    # 保存覆盖原有截图
    im.save(filename)

    result = isRequestApi(filename)
    im.close()
    if os.path.exists(filename):
        os.remove(filename)
    if len(result):
        device.log.info('成功识别到坐标[%s,%s][%s,%s]内文字' % (left, top, right, bottom))
        return True
    device.log.info('未识别到坐标[%s,%s][%s,%s]内文字' % (left, top, right, bottom))
    return False


def 验证获取值是否为数字(device, s):
    try:
        float(s)
        return True
    except ValueError:
        pass

    try:
        import unicodedata
        unicodedata.numeric(s)
        return True
    except (TypeError, ValueError):
        pass

    return False


def 获取基金净值(device, date, code):
    import akshare as ak
    fund_open_fund_info_em_df = ak.fund_open_fund_info_em(fund=str(code), indicator="单位净值走势")
    fund_open_fund_info_em_df["净值日期"] = fund_open_fund_info_em_df["净值日期"].astype('str')
    df = fund_open_fund_info_em_df[fund_open_fund_info_em_df['净值日期'] == date]
    return {"单位净值": df['单位净值'].values[0], "日增长率": str(df['日增长率'].values[0]) + "%"}


def 获取当天日期(device):
    from datetime import datetime
    # t = date.today()  # date类型
    # dt = datetime.strptime(str(t), '%Y-%m-%d %H:%M:%S')  # date转str再转datetime
    # dt = t.strftime('%Y-%m-%d')  # date转str再转datetime
    # return dt
    return str(datetime.now()).split(' ')[0] + " 00:00:00"


def 获取前一天日期(device):
    from datetime import datetime, timedelta
    # today = 获取当天日期(device)  # datetime类型当前日期
    # yesterday = today + timedelta(days = -1)  # 减去一天
    # return yesterday
    return datetime.today() - timedelta(days=1)


def 滑块拖动(device, path):
    from appium.webdriver.common.touch_action import TouchAction
    import base64
    import requests
    import random
    import os
    from PIL import Image

    def request_slider(img_path):
        url = slider_url
        with open(img_path, 'rb') as f:
            image = base64.b64encode(f.read()).decode()
        data = {
            'image': image,
        }
        response = requests.post(url, data=data, timeout=60).json()
        return response.get('instance')

    filename = 'pictures/handle/' + "image_screen_" + str(random.randint(10, 99)) + ".png"
    device.appium.driver.save_screenshot(filename)
    element = 获取控件对象(device, path)
    # 截屏
    # 获取元素上下左右的位置
    left = element.location['x']
    top = element.location['y']
    right = element.location['x'] + element.size['width']
    bottom = element.location['y'] + element.size['height']
    # 打开刚才的截图
    im = Image.open(filename)
    # 截取
    im = im.crop((left, top, right, bottom))
    img = 'pictures/handle/' + str(int((time.time() * 10000))) + ".png"
    im.save(img)
    im.close()
    instance = request_slider(img)
    action = TouchAction(device.appium.driver)
    # action.press(x=231, y=1441).wait(1000).move_to(x=231 + instance, y=1441).release().perform()
    action.press(x=232, y=1388).wait(random.randint(800, 1500)).move_to(x=221 + instance,
                                                                        y=1388).release().perform()  # 9xp
    if os.path.exists(filename):
        os.remove(filename)
    if os.path.exists(img):
        os.remove(img)


def 获取股东户数统计截止日(device, code):
    import akshare as ak
    stock_zh_a_gdhs_detail_em_df = ak.stock_zh_a_gdhs_detail_em(symbol=code)
    return stock_zh_a_gdhs_detail_em_df.loc[0:0]['股东户数统计截止日'].values.astype('str')


def 清空(device, path):
    print("运行到获取控件对象")
    element = 获取控件对象(device, path)
    element.clear()


def 获取股东数据解禁日(device, code):
    import akshare as ak
    stock_restricted_shares_df = ak.stock_restricted_shares(stock=code)
    return stock_restricted_shares_df.loc[0:0]['解禁日期'].values.astype('str')


def 获取每周星期数(device):
    import time
    import datetime
    week_list = ["星期一", "星期二", "星期三", "星期四", "星期五", "星期六", "星期日"]
    local_time = time.localtime()  # 获取当前时间的时间元组
    week_index = local_time.tm_wday  # 获取时间元组内的tm_wday值
    week = week_list[week_index]
    return week


def 获取前几天日期(device, n):
    from datetime import datetime, timedelta
    # today = 获取当天日期()  # datetime类型当前日期
    # yesterday = today + timedelta(days = -n)  # 减去n天
    # return yesterday
    return datetime.today() - timedelta(days=n)


def 获取最新基金净值更新日期(device, code):
    import akshare as ak
    fund_open_fund_info_em_df = ak.fund_open_fund_info_em(fund=str(code), indicator="单位净值走势")
    fund_open_fund_info_em_df["净值日期"] = fund_open_fund_info_em_df["净值日期"].astype('str')
    return fund_open_fund_info_em_df.iloc[-1]["净值日期"]


def 多级获取控件对象(device, xpath1, xpath2):
    return device.appium.driver.find_element_by_xpath(xpath1).find_elements_by_class_name(xpath2)


def 华泰分时图是否存在(device, path):
    """
    路径id com.lphtsccft:id/hq_chartview
    判断RGB是否存在橘黄色 蓝色
    """
    from PIL import Image
    import time
    import os
    def mycrop(img_path, x1, x2, y1, y2, save_path):
        import cv2
        img = cv2.imread(img_path)  # img_path为图片所在路径
        crop_img = img[y1:y2, x1:x2]  # x0,y0为裁剪区域左上坐标；x1,y1为裁剪区域右下坐标
        cv2.imwrite(save_path, crop_img)

    filename = 'pictures/handle/' + str(int((time.time() * 10000))) + ".png"
    device.appium.driver.save_screenshot(filename)
    element = 获取控件对象(device, path)
    # 获取元素上下左右的位置
    left = element.location['x']
    top = element.location['y']
    right = element.location['x'] + element.size['width']
    bottom = element.location['y'] + element.size['height']
    # 打开刚才的截图
    im = Image.open(filename)
    # 截取对应位置
    im = im.crop((left, top, right, bottom))
    img = 'pictures/handle/' + str(int((time.time() * 10000))) + ".png"
    im.save(img)
    im.close()
    k_img = 'pictures/handle/' + str(int((time.time() * 10000))) + "_1.png"
    w = device.appium.driver.get_window_size()['width']
    if w == 720:
        mycrop(img, 0, 720, 0, 465, k_img)
    else:
        mycrop(img, 0, 1080, 0, 690, k_img)
    自定义图片上传展示(device, k_img)
    img2 = Image.open(k_img)
    img2 = img2.convert('RGBA')
    pixdata = img2.load()
    i = 0
    j = 0
    for y in range(img2.size[1]):
        for x in range(img2.size[0]):
            if pixdata[x, y][0] > 200 and 100 <= pixdata[x, y][1] <= 160 and 50 <= pixdata[x, y][2] <= 80:
                i += 1
            if pixdata[x, y][0] < 100 and pixdata[x, y][1] <= 100 and 150 <= pixdata[x, y][2]:
                j += 1
    device.log.info("橘黄色像素点：{}个".format(i))
    device.log.info("蓝色像素点：{}个".format(j))
    if os.path.exists(filename):
        os.remove(filename)
    if os.path.exists(img):
        os.remove(img)
    if os.path.exists(k_img):
        os.remove(k_img)
    if i > 10 and j > 10:
        return True
    return False


def 华泰日图是否存在(device, path):
    """
    路径id com.lphtsccft:id/hq_crosslineview
    判断RGB是否存在红色 绿色
    """
    from PIL import Image
    import time
    import os
    def mycrop(img_path, x1, x2, y1, y2, save_path):
        import cv2
        img = cv2.imread(img_path)  # img_path为图片所在路径
        crop_img = img[y1:y2, x1:x2]  # x0,y0为裁剪区域左上坐标；x1,y1为裁剪区域右下坐标
        cv2.imwrite(save_path, crop_img)

    filename = 'pictures/handle/' + str(int((time.time() * 10000))) + ".png"
    device.appium.driver.save_screenshot(filename)
    element = 获取控件对象(device, path)
    # 获取元素上下左右的位置
    left = element.location['x']
    top = element.location['y']
    right = element.location['x'] + element.size['width']
    bottom = element.location['y'] + element.size['height']
    # 打开刚才的截图
    im = Image.open(filename)
    # 截取对应位置
    im = im.crop((left, top, right, bottom))
    img = "pictures/handle/" + str(int((time.time() * 10000))) + ".png"
    im.save(img)
    im.close()
    k_img = 'pictures/handle/' + str(int((time.time() * 10000))) + "_1.png"
    w = device.appium.driver.get_window_size()['width']
    if w == 720:
        mycrop(img, 0, 720, 40, 450, k_img)
    else:
        mycrop(img, 0, 1080, 0, 690, k_img)
    自定义图片上传展示(device, k_img)
    img2 = Image.open(k_img)
    img2 = img2.convert('RGBA')
    pixdata = img2.load()
    i = 0
    j = 0
    for y in range(img2.size[1]):
        for x in range(img2.size[0]):
            if pixdata[x, y][0] == 255 and pixdata[x, y][1] == 0 and pixdata[x, y][2] == 3:
                i += 1
            if pixdata[x, y][0] == 90 and pixdata[x, y][1] == 161 and pixdata[x, y][2] == 9:
                j += 1
    device.log.info("红色像素点：{}个".format(i))
    device.log.info("绿色像素点：{}个".format(j))
    if os.path.exists(filename):
        os.remove(filename)
    if os.path.exists(img):
        os.remove(img)
    if os.path.exists(k_img):
        os.remove(k_img)
    if i > 10 and j > 10:
        return True
    return False


def 获取字符串中的浮点数(device, arg):
    import re
    return re.findall(r"\d*\.\d+|\d+", arg)[0]


def 获取最新日期(device, code):
    import requests
    params = {
        'code': code
    }
    response = requests.get(url=current_market_url, params=params, timeout=20).json()
    device.log.info("数据来源【dfcf】:{}".format(response))
    return response.get('date')


def 获取上一个交易日(device, tradecal_name='上交所'):
    import requests
    from datetime import datetime, timedelta
    from script.config import Config
    url = Config.API_URL + 'tradecal'
    token = Config.API_TOKEN
    headers = {'Authorization': 'token {}'.format(token)}
    response = requests.get(url, headers=headers).json()
    tradecal_dict = response.get('tradecal')
    if not tradecal_dict:
        return response
    notrade_lsit = tradecal_dict[tradecal_name]
    lasttradeday = (datetime.now() - timedelta(days=1)).date()
    while lasttradeday.weekday() > 4 or lasttradeday.strftime('%Y-%m-%d') in notrade_lsit:
        lasttradeday -= timedelta(days=1)
    return lasttradeday.strftime('%Y-%m-%d')


def 运算验证码(device, path):
    """
    第三方打码平台
    """
    import json
    import requests
    import base64
    uname = 'uFDncJIIAH5H'
    pwd = 'yC2EVUILo5XO'

    def base64_api(uname, pwd, img, typeid):
        with open(img, 'rb') as f:
            base64_data = base64.b64encode(f.read())
            b64 = base64_data.decode()
        data = {"username": uname, "password": pwd, "typeid": typeid, "image": b64}
        result = json.loads(requests.post("http://api.ttshitu.com/predict", json=data, timeout=20).text)
        if result['success']:
            return result["data"]["result"]
        else:
            return result["message"]
        return ""

    result = base64_api(uname=uname, pwd=pwd, img=path, typeid=11)
    print(result)
    return result


def 招商当天日期(device):
    import datetime
    return datetime.date.today().day


def 打印日志(device, info):
    device.log.info(info)


def 获取指数涨跌信息(device, code):
    import requests
    params = {
        'code': code
    }

    response = requests.get(index_info_url, params=params, timeout=20).json()
    device.log.info("数据来源【dfcf】:{}".format(response))
    return response


def 新股个数(device, 控件路径):
    num = 0
    for i in range(1, 100):
        element = 控件路径.format(i)
        try:
            device.appium.driver.find_element_by_xpath(element)
            num += 1
        except:
            break
    return num


def 涨乐理财产品页面验证(device):
    判断结果 = False
    time.sleep(5)
    source = device.appium.driver.page_source
    if device.has_element('/涨乐财富通/理财/通用/数据异常验证'):
        device.log.info('存在异常数据')
        if 'ETF' in source or '产品目前处于封闭期' in source:
            device.log.info('当前产品不符合校验条件')
            判断结果 = True
    else:
        获取结果 = 获取控件对象(device, '/涨乐财富通/市场/理财/通用/基金经理')
        判断结果 = 获取结果 != ''
        # 滑动控件至屏幕内(device, '/涨乐财富通/理财/热门/搜索/产品详情/简况/基金概况/成立日')
        # # 上滑(device)
        # # if not 是否存在控件(device, '/涨乐财富通/市场/理财/通用/持仓'):
        # #     小上滑(device)
        # 点击(device, '/涨乐财富通/市场/理财/通用/持仓')
        # # 上滑(device)
        # 小上滑(device)
        # if 是否存在控件(device, '/涨乐财富通/市场/理财/通用/持仓/投资分布/净资产'):  # 不加这句循环找多条产品若找不到会直接中断
        #     获取结果 = 获取文本信息(device, '/涨乐财富通/市场/理财/通用/持仓/投资分布/净资产')
        #     判断结果 = 获取结果 != '' and 获取结果 != '--'
    return 判断结果


def 涨乐全球通弹窗处理(device):
    time.sleep(2)
    if device.has_element('/涨乐全球通/平台协议/同意并继续'):
        device.has_element('/涨乐全球通/平台协议/同意并继续')()
        device.log.info('已同意平台协议')
    if device.has_element('/涨乐全球通/手机号登录/关闭'):
        device.has_element('/涨乐全球通/手机号登录/关闭')()
        device.log.info('关闭手机号登录提示')
    if not 检测界面弹出层(device):
        device.log.info('未检测到弹窗信息')
    else:
        device.log.info('开始处理启动弹窗')
        for i in range(3):
            # source = device.appium.driver.page_source
            # if "今日新股" in source and device.has_element("/涨乐全球通/今日新股/关闭"):
            if device.has_element("/涨乐全球通/今日新股/关闭"):
                device.has_element("/涨乐全球通/今日新股/关闭")()
                device.log.info('关闭今日新股申购提醒！')
                continue
            elif device.has_element('/涨乐全球通/广告弹窗/不再提醒'):
                device.has_element('/涨乐全球通/广告弹窗/不再提醒')()
                device.log.info('关闭广告弹窗')
                continue
            elif device.has_element("/涨乐全球通/广告弹窗/关闭"):
                device.has_element("/涨乐全球通/广告弹窗/关闭")()
                device.log.info('关闭广告弹窗')
                continue
            else:
                break
        device.log.info('启动弹窗处理完毕')


def 获取瞬时指数信息(device, code):
    import requests
    params = {
        'code': code
    }

    response = requests.get(ws_index_url, params=params, timeout=20).json()
    device.log.info("数据来源【dfcf】:{}".format(response))
    return response


def 华泰九转历史信号是否存在(device, path):
    from PIL import Image
    import time
    import os
    def mycrop(img_path, x1, x2, y1, y2, save_path):
        import cv2
        img = cv2.imread(img_path)  # img_path为图片所在路径
        crop_img = img[y1:y2, x1:x2]  # x0,y0为裁剪区域左上坐标；x1,y1为裁剪区域右下坐标
        cv2.imwrite(save_path, crop_img)

    filename = "pictures/handle/" + str(int((time.time() * 10000))) + ".png"
    device.appium.driver.save_screenshot(filename)
    element = 获取控件对象(device, path)
    # 获取元素上下左右的位置
    left = element.location['x']
    top = element.location['y']
    right = element.location['x'] + element.size['width']
    bottom = element.location['y'] + element.size['height']
    # 打开刚才的截图
    im = Image.open(filename)
    # 截取对应位置
    im = im.crop((left, top, right, bottom))
    img = "pictures/handle/" + str(int((time.time() * 10000))) + ".png"
    im.save(img)
    im.close()
    mycrop(img, 0, 720, 40, 450, img)
    img2 = Image.open(img)
    img2 = img2.convert('RGBA')
    pixdata = img2.load()
    i = 0
    j = 0
    for y in range(img2.size[1]):
        for x in range(img2.size[0]):
            if pixdata[x, y][0] == 246 and pixdata[x, y][1] == 38 and pixdata[x, y][2] == 53:
                i += 1
            if pixdata[x, y][0] == 76 and pixdata[x, y][1] == 170 and pixdata[x, y][2] == 93:
                j += 1
    device.log.info("红色像素点：{}个".format(i))
    device.log.info("绿色像素点：{}个".format(j))
    if os.path.exists(filename):
        os.remove(filename)
    if os.path.exists(img):
        os.remove(img)
    if i > 10 and j > 10:
        return True
    return False


def 图片趋势比率(device, path, platform, nums):
    # 各个app平台剪裁参数
    from PIL import Image
    import time
    info_dict = {
        'hai_tong': {
            'crop_list': [70, 690, 924, 1050],
            'crops': [23, 620, 21, 130],
            'name': '海通'
        },
        'hua_tai': {
            'crop_list': [55, 680, 999, 1080],
            'crops': [14, 625, 13, 80],
            'name': '华泰'
        }
    }

    def my_crop(img_path, y1, y2, x1, x2, save_path):
        import cv2
        img = cv2.imread(img_path)  # img_path为图片所在路径
        crop_img = img[y1:y2, x1:x2]  # x0,y0为裁剪区域左上坐标；x1,y1为裁剪区域右下坐标
        cv2.imwrite(save_path, crop_img)

    def get_ratio(path):
        img = Image.open(path)
        img = img.convert('RGBA')
        pixdata = img.load()
        lists = []
        y_distance = img.size[1]
        for y in range(y_distance):
            for x in range(img.size[0]):
                if pixdata[x, y][1] < 200 and pixdata[x, y][2] < 200:
                    lists.append(y)
        return (round(lists[0] / y_distance, 3), round(lists[-1] / y_distance, 3))

    # 时间判断
    time_tuple = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
    if int(time_tuple.split(':')[-1]) > 25:
        time.sleep(30)
    # 剪裁图片
    device.log.info('开始剪裁{}图片'.format(info_dict[platform]['name']))
    my_crop(path, info_dict[platform]['crop_list'][0], info_dict[platform]['crop_list'][1],
            info_dict[platform]['crop_list'][2], info_dict[platform]['crop_list'][3], path)
    i = 0
    if not os.path.exists(platform):
        os.mkdir(platform)
    自定义图片上传展示(device, path)
    j = info_dict[platform]['crops'][0]
    pic_dir = platform + "/{}".format(int(time.time() * 100000))
    os.mkdir(pic_dir)
    # 切图
    device.log.info('开始{}切图'.format(info_dict[platform]['name']))
    pic_list = []
    for u in range(1000):
        cut_picture = '{}/img_{}.png'.format(pic_dir, i)
        my_crop(path, 0, info_dict[platform]['crops'][1], i, j, cut_picture)
        pic_list.append(cut_picture)
        i += info_dict[platform]['crops'][2]
        j += info_dict[platform]['crops'][2]
        if j > info_dict[platform]['crops'][3]:
            break

    # 颜色检索
    device.log.info('开始颜色检索{}张{}图片'.format(nums, info_dict[platform]['name']))
    ratio_list = []
    for i in pic_list[0:nums]:
        ratio_list.append(get_ratio(i))
    device.log.info('{}各个图片的趋势比率值：{}'.format(info_dict[platform]['name'], ratio_list))
    return ratio_list


def 比率相似度对比(device, ratio_list1, ratio_list2, tag):
    """
    ratio_list1 图片趋势比率
    ratio_list2 图片趋势比率
    tag  对比范围值
    """
    difference_list = []
    for i in zip(ratio_list1, ratio_list2):
        difference_list.append(abs(i[0][0] - i[1][0]))
        difference_list.append(abs(i[0][1] - i[1][1]))
    device.log.info('两张图片趋势比率相差值: {};给定相差值最大临界值：{}'.format(difference_list, tag))
    if max(difference_list) <= tag:
        return True
    return False


def 自定义图片上传展示(device, path):
    """

    """
    if isinstance(path, str):
        device.add_pictures(path)
    else:
        for i in path:
            device.add_pictures(i)


def 坐标获取验证码(device, position_l, position_r):
    import base64
    from PIL import Image
    import json
    import requests
    import random
    def noise_remove_cv2(image_name, k):
        import cv2
        import ddddocr
        from PIL import Image
        def calculate_noise_count(img_obj, w, h):
            count = 0
            width, height = img_obj.shape
            for _w_ in [w - 1, w, w + 1]:
                for _h_ in [h - 1, h, h + 1]:
                    if _w_ > width - 1:
                        continue
                    if _h_ > height - 1:
                        continue
                    if _w_ == w and _h_ == h:
                        continue
                    if img_obj[_w_, _h_] < 230:
                        count += 1
            return count

        img = cv2.imread(image_name, 1)
        gray_img = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        w, h = gray_img.shape
        for _w in range(w):
            for _h in range(h):
                if _w == 0 or _h == 0:
                    gray_img[_w, _h] = 255
                    continue
                pixel = gray_img[_w, _h]
                if pixel == 255:
                    continue

                if calculate_noise_count(gray_img, _w, _h) < k:
                    gray_img[_w, _h] = 255

        return gray_img

    def ocr_string(path):
        import cv2
        import ddddocr
        from PIL import Image
        name = "pictures/own_ocr/" + str(int(time.time())) + ".png"
        image = noise_remove_cv2(path, 4)
        cv2.imwrite(name, image)
        ocr = ddddocr.DdddOcr()
        with open(name, 'rb') as f:
            img_bytes = f.read()
        res = ocr.classification(img_bytes)
        if os.path.exists(name):
            os.remove(name)
        # if os.path.exists(path):
        #     os.remove(path)
        return res

    filename = "pictures/handle/" + "image_screen_" + str(random.randint(10, 99)) + ".png"
    # 截屏
    device.appium.driver.save_screenshot(filename)

    if len(position_l) != 2 or len(position_r) != 2:
        print('坐标参数错误！')

    time.sleep(1)
    w = device.appium.driver.get_window_size()['width']
    h = device.appium.driver.get_window_size()['height']

    # 左上角、右下角 两点尺寸比例坐标
    x_l_per = position_l[0]
    y_l_per = position_l[1]

    x_r_per = position_r[0]
    y_r_per = position_r[1]

    if x_l_per > 1 or y_l_per > 1 or x_r_per > 1 or y_r_per > 1:
        print('x或y的坐标超出当前分辨率！')
        return

    # 左上角、右下角 两点真实坐标
    x_l = x_l_per * w
    y_l = y_l_per * h
    x_r = x_r_per * w
    y_r = y_r_per * h

    # 识别区域上下左右位置
    left = x_l
    top = y_l
    right = x_r
    bottom = y_r

    # 打开刚才的截图
    im = Image.open(filename)
    # 截取对应位置
    im = im.crop((left, top, right, bottom))
    filename2 = "pictures/own_ocr/" + 'image_code.png'
    # 保存覆盖原有截图
    im.save(filename2)
    result = ocr_string(filename2)
    print(result)
    if os.path.exists(filename):
        os.remove(filename)
    return result


def 获取全量信息(device, code, types):
    import requests
    params = {
        'code': str(code),
        'types': str(types)
    }
    response = requests.get(url=total_info_url, params=params, timeout=20).json()
    device.log.info("数据来源【dfcf】:{}".format(response))
    return response.get('data')


def 获取申购上市股票信息(device):
    import requests
    params = {
        'code': 'new_share',
        'types': '1'
    }
    response = requests.get(url=total_info_url, params=params, timeout=20).json()
    device.log.info("数据来源【dfcf】:{}".format(response))
    return response.get('data').get('data')


def 打开应用(device, app_package, activity):
    device.appium.driver.start_activity(app_package, activity)


def 切换应用(device, app_package):
    device.appium.driver.activate_app(app_package)


def 关闭应用(device, app_package):
    device.appium.driver.terminate_app(app_package)


def 获取控件指定文本(device, path):
    标注截图(device, path, sys._getframe().f_code.co_name)
    ele = None
    value = None
    try_times = 10
    for _ in range(try_times):
        控件对象 = 获取控件对象(device, path)
        ele = 控件对象.get_attribute('content-desc')
        if ele:
            value = str(ele)
            if value.strip() and value != '--':
                device.log.info('获取 <%s> 内容成功：%s。试了 %s 次' % (path, value, _ + 1))
                return value
    if ele is not None:
        device.log.info('获取 <%s> 内容无效：%s。试了 %s 次' % (path, value, try_times))
        return value

    device.log.info('[error]获取 <%s> 内容失败。' % path)
    raise TestCaseFail(ElementNotExist(path))


def 下滑_自选(device):
    def get_size():
        x = device.appium.driver.get_window_size()['width']
        y = device.appium.driver.get_window_size()['height']
        return x, y

    time.sleep(3)
    print('==========下滑===========')
    l = get_size()
    x1 = int(l[0] * 0.5)
    x2 = int(l[0] * 0.5)
    y1 = int(l[1] * 0.5)
    y2 = int(l[1] * 0.8)
    device.appium.driver.swipe(x1, y1, x2, y2, 3000)


def 左滑_首页(device):
    def get_size():
        x = device.appium.driver.get_window_size()['width']
        y = device.appium.driver.get_window_size()['height']
        return x, y

    time.sleep(3)
    print('==========左滑===========')
    l = get_size()
    x1 = int(l[0] * 0.8)
    x2 = int(l[0] * 0.1)
    y1 = int(l[1] * 0.25)
    y2 = int(l[1] * 0.25)
    device.appium.driver.swipe(x1, y1, x2, y2, 3000)


def 验证浮点数(device, string):
    try:
        float(string)
        return True
    except:
        return False


def 涨乐多密码登录(device, 账号, 交易密码1, 交易密码2, 通讯密码1, 通讯密码2):
    device['/涨乐财富通/交易/交易登录页面/账户输入框'] = 账号
    device['/涨乐财富通/交易/交易登录页面/密码输入框'] = 交易密码1
    if device.has_element('/涨乐财富通/交易/交易登录页面/安全密码输入框'):
        device['/涨乐财富通/交易/交易登录页面/安全密码输入框'] = '110119'
    device['/涨乐财富通/交易/交易登录页面/登陆按钮'].click()
    登录情况 = ''
    if device.has_element('/涨乐财富通/交易/交易登录页面/通讯密码验证'):
        device['/涨乐财富通/交易/交易登录页面/通讯密码验证'].click()
        device['/涨乐财富通/交易/交易登录页面/通讯密码弹窗/输入框'] = 通讯密码1
        device['/涨乐财富通/交易/交易登录页面/通讯密码弹窗/确认'].click()
    if device.has_element('/涨乐财富通/交易/交易登录页面/设备安全密码/设置并登录'):
        device.log.info('国密登录')
        device['/涨乐财富通/交易/交易登录页面/设备安全密码/设置并登录'].click()
        device['/涨乐财富通/交易登录页面/设备安全密码/密码输入框'] = '110119'
        time.sleep(1)
    if device.has_element('/涨乐财富通/账户/登录/系统提示/交易密码错误') or device.has_element(
            '/涨乐财富通/账户/登录/系统提示/密码校验错误'):
        登录情况 = '交易密码错误'
        device.log.info('交易密码错误')
        device['/涨乐财富通/账户/登录/系统提示/交易密码错误/重新输入'].click()
        device['/涨乐财富通/交易/交易登录页面/密码输入框'] = 交易密码2
        if device.has_element('/涨乐财富通/交易/交易登录页面/安全密码输入框'):
            device['/涨乐财富通/交易/交易登录页面/安全密码输入框'] = '110119'
        device['/涨乐财富通/交易/交易登录页面/登陆按钮'].click()
    device.log.info('处理登录后弹窗')
    for i in range(3):
        source = device.appium.driver.page_source
        if device.has_element('/提示取消/取消按钮'):
            device['/提示取消/取消按钮']()
            device.log.info('关闭风险揭示')
            continue
        elif "后台数据清算" in source or '系统维护' in source:
            device.has_element('/后台清算系统提示/确定')()
            device.log.info('关闭系统提示')
            continue
        elif "不再提醒" in source and device.has_element("/公用/不再提醒"):
            device.has_element("/公用/不再提醒")()
            device.log.info('不再提醒！')
            continue
        else:
            device.log.info('已检查{}次'.format(i + 1))
            break
    device.log.info('已处理登录后弹窗')
    return 登录情况


def 专业实时行情数据(device, code):
    import requests

    url = 'http://172.31.76.109:5000/get_data'

    params = {
        'ticker': code
    }

    response = requests.get(url, params).json()
    if response.get('data'):
        return response.get('data')[-1]
    return ''


def 全球通滑动至对应市场行情页(device, mtext):
    x = device.appium.driver.get_window_size()['width']
    y = device.appium.driver.get_window_size()['height']
    if mtext == '香港':
        x1 = int(x * 0.1)
        x2 = int(x * 0.9)
        y1 = int(y * 0.5)
        y2 = int(y * 0.5)
        device.appium.driver.swipe(x1, y1, x2, y2)
    try:
        for i in range(4):
            x1 = int(x * 0.5)
            x2 = int(x * 0.5)
            y1 = int(y * 0.5)
            y2 = int(y * 0.2)
            device.appium.driver.swipe(x1, y1, x2, y2)
            所在行情 = 获取文本信息(device, '/涨乐全球通/行情/当前行情标题')
            if mtext in 所在行情:
                device.log.info('寻找到对应市场页面')
                break
            else:
                x1 = int(x * 0.9)
                x2 = int(x * 0.1)
                y1 = int(y * 0.5)
                y2 = int(y * 0.5)
                device.appium.driver.swipe(x1, y1, x2, y2)
    except:
        device.log.info('寻找页面异常')


def 当日新股新债信息(device):
    import requests
    params = {
        'code': 'stock_bond',
        'types': '1'
    }
    response = requests.get(url=total_info_url, params=params, timeout=20).json()
    return response


def 获取近期全量新股新债信息(device):
    """
    获取东方财富-2025年1月1日之后的所有新股和新债信息北交所（包含待上市）。

    :param device: 调用此函数的设备或客户端标识，用于区分不同来源。
    :return: 返回包含新股、新债和北交所数据的完整响应字典，示例格式：
        {
            'code': 'all_stock_bond_bj_1',  # 业务标识
            'new_stock': [...],        # 新股列表
            'bj': [...],               # 北交所列表
            'new_bond': [...],         # 新债列表
        }
    """
    import requests

    # 构造请求参数
    params = {
        'code': 'all_stock_bond_bj',  # 数据类型标识，与爬虫发送时保持一致
        'types': '1'
    }

    # 发送 GET 请求到后端总接口，超时时间为 20 秒
    response = requests.get(url=total_info_url,params=params,timeout=20).json()
    # 直接返回解析后的 JSON 响应
    return response


def 涨停榜信息(device):
    import requests
    params = {
        'code': 'TopicZTPool',
        'types': '1'
    }
    response = requests.get(url=total_info_url, params=params, timeout=20).json()
    return response


def 领涨板块信息(device):
    import requests
    params = {
        'code': 'leading_rise',
        'types': '1'
    }
    response = requests.get(url=total_info_url, params=params, timeout=20).json()
    return response


def 涨幅榜信息(device):
    import requests
    params = {
        'code': 'increase_top',
        'types': '1'
    }
    response = requests.get(url=total_info_url, params=params, timeout=20).json()
    return response


def 跌幅榜信息(device):
    import requests
    params = {
        'code': 'decline_top',
        'types': '1'
    }
    response = requests.get(url=total_info_url, params=params, timeout=20).json()
    return response


def 换手率信息(device):
    import requests
    params = {
        'code': 'turnover_top',
        'types': '1'
    }
    response = requests.get(url=total_info_url, params=params, timeout=20).json()
    return response


def 成交额信息(device):
    import requests
    params = {
        'code': 'turnover_price',
        'types': '1'
    }
    response = requests.get(url=total_info_url, params=params, timeout=20).json()
    return response


def 涨速信息(device):
    import requests
    params = {
        'code': 'speed_increase',
        'types': '1'
    }
    response = requests.get(url=total_info_url, params=params, timeout=20).json()
    return response


def 获取用例执行信息(device):
    _api = 'get_testcase_lastresult/' + str(device.running_case_id)
    res_data = cloud_api(_api)
    return res_data


def 模拟键盘输入(device, keycode):
    os.system(f"adb shell input keyevent {keycode}")
    print(f"模拟按下键: {keycode}")


def 自定义抛出异常(device, info):
    raise Exception(info)


def 全球通账户登录(device, 手机号, 交易密码):
    # device.log.info('强账户登录')
    # 全球通一机一账户且大多数场景强账户不可变更
    # device['/涨乐全球通/自选/自选/个人菜单/交易登录/手机号输入框'] = 手机号
    输入文本(device, '/涨乐全球通/自选/自选/个人菜单/交易登录/交易密码输入框', 交易密码)
    # device['/涨乐全球通/自选/自选/个人菜单/交易登录/交易密码输入框'] = 交易密码
    device['/涨乐全球通/自选/自选/个人菜单/交易登录/协议签署栏'].click()
    device['/涨乐全球通/自选/自选/个人菜单/交易登录/登录按钮'].click()
    # 点击(device, '/涨乐全球通/自选/自选/个人菜单/交易登录/登录按钮')
    time.sleep(1)
    if device.has_element('/涨乐全球通/强账户登录/密码更新提示/沿用当前密码'):
        device['/涨乐全球通/强账户登录/密码更新提示/沿用当前密码'].click()
    if device.has_element('/涨乐全球通/强账户登录/周年检视通知/去更新'):
        device.log.info('账户周年检视更新')
        device['/涨乐全球通/强账户登录/周年检视通知/去更新'].click()
        time.sleep(2)
        device.appium.slide_up(3000)
        device['/涨乐全球通/强账户登录/周年检视/本人确认'].click()
        device['/涨乐全球通/强账户登录/周年检视/一键提交'].click()


def 自定义用例结果展示(device, msg):
    device.add_msg_result(str(msg))


def 智能点击自定义偏移坐标(device, filename, ctx, x1, y1):
    import os
    import time
    from PIL import Image
    import requests
    import json
    import base64

    ctx_list = ctx.split('|')

    def get_location_pd(res):
        x0 = res[0][0]
        y0 = res[0][1]

        x_t = (res[1][0] - res[0][0]) / 2
        y_t = (res[3][1] - res[0][1]) / 2

        x = x0 + x_t
        y = y0 + y_t
        return x, y

    def cv2_to_base64(image):
        return base64.b64encode(image).decode('utf8')

    headers = {"Content-type": "application/json"}
    fo = open(filename, 'rb')
    img = fo.read()
    if img is None:
        print("error in loading image:{}".format(filename))
        return False
    data = {'images': [cv2_to_base64(img)]}
    ai_response = requests.post(url=ai_server_url, headers=headers, data=json.dumps(data))
    fo.close()
    if str(ai_response.status_code) == '200':
        result = ai_response.json()["results"][0]
    else:
        print('识别失败')
        return False
    punctuation = r"""!"#$%&'()*+,-./:;<=>?@[\]^_`{|}~，。、丶；‘’：“”"""

    if len(result) > 0:
        if len(ctx_list) == 1:
            for res in result:
                if ctx_list[0] == res['text'].strip(punctuation):
                    x, y = get_location_pd(res['text_region'])
                    device.log.info('点击【%s】,坐标(%s,%s)' % (ctx_list[0], x, y))
                    if int(y) < 2288:
                        device.appium.driver.tap([(x + x1, y + y1)])
                    else:
                        device.appium.driver.tap([(x + x1, y + y1 - 40)])

    if os.path.exists(filename):
        os.remove(filename)


def 招商三方对比当天历史行情数据(device, code):
    import requests
    url = 'http://cpszhaoszq.njzfit.cn/api/stock_data'
    headers = {
        'Authorization': 'token {}'.format('575f0875a048aa07548ded5115784ccb2dfbd941')}
    params = {
        'code': code
    }
    response = requests.get(url, params=params, headers=headers).json()
    return response.get('data')


def 涨乐行情循环校验(device, prefix, contents, flag=''):
    res = True
    if flag == '':
        flag = 0
    import datetime
    当前小时 = datetime.datetime.now().hour
    当前分钟 = datetime.datetime.now().minute
    时间条件 = 9 <= float(当前小时) < 11 or (float(当前小时) == 11 and float(当前分钟) < 30) or 13 <= float(
        当前小时) < 15
    五档手数变动校验 = False
    涨跌停判断 = False
    for c in contents:
        if not 时间条件 and ('时间' in c or '五档' in c or '明细' in c):
            device.log.info('【' + c + '：非交易时间不校验' + '】')
            pass
        else:
            if c == '名称' or c == '代码':
                text = 获取文本信息(device, prefix + c)
                res = text != '' and text != '--' and text is not None
                device.log.info('【' + c + '：' + text + '非空' + '】')
            elif '盘口' in c or c == '实时价格' or c == '明细/首行/价格':
                text = 获取文本信息(device, prefix + c)
                try:
                    if 'iopv' in c or '换手' in c:
                        res = float(
                            text.replace('%', '').replace(',', '').replace('万', '').replace('股', '').replace('亿',
                                                                                                               '')) >= 0
                        device.log.info('【' + c + '：' + text + '大于等于0' + '】')
                    elif '溢价折价率' in c:
                        res = -10 <= float(text.replace('%', '')) < 50
                        device.log.info('【' + c + '：' + text + '大于等于-10小于50' + '】')
                    elif '溢价率' in c:
                        res = -100 < float(text.replace('%', '')) <= 500
                        device.log.info('【' + c + '：' + text + '大于-100%小于等于500%' + '】')
                    else:
                        res = float(
                            text.replace('%', '').replace(',', '').replace('万', '').replace('股', '').replace('亿',
                                                                                                               '')) > 0
                        device.log.info('【' + c + '：' + text + '大于0' + '】')
                except:
                    res = False
            elif '手数' in c:
                text = 获取文本信息(device, prefix + c)
                try:
                    res = float(text.replace('万', '').replace('亿', '')) >= 0
                except:
                    res = False
                device.log.info('【' + c + '：' + text + '大于等于0' + '】')
                if res:
                    if float(text.replace('万', '').replace('亿', '')) == 0 and ('买1' in c or '卖1' in c):
                        价格 = 获取文本信息(device, '/涨乐财富通/行情/通用/个股详情/' + c.replace('手数', '价格'))
                        涨跌停判断 = '--' in 价格
                    if '五档' in c and not 五档手数变动校验 and not 涨跌停判断 and not flag:
                        for i in range(6):
                            time.sleep(5)
                            text2 = 获取文本信息(device, prefix + c)
                            res = text != text2
                            device.log.info('【' + c + f'变化校验：第一次结果{text},第二次结果{text2}' + '】')
                            if res:
                                五档手数变动校验 = True
                                break
                            else:
                                res = '买5' not in c
                    else:
                        if c == '明细/首行/手数' and not 涨跌停判断 and not flag:
                            for i in range(6):
                                time.sleep(12)
                                text2 = 获取文本信息(device, prefix + c)
                                res = text != text2
                                device.log.info('【' + c + f'变化校验:第一次结果{text},第二次结果{text2}' + '】')
                                if res:
                                    break
                        else:
                            if 五档手数变动校验:
                                device.log.info('【' + '五档变化校验已通过' + '】')
                            else:
                                if flag:
                                    device.log.info('【' + '产品交易量小不校验变化' + '】')
                                else:
                                    device.log.info('【' + '涨跌停不校验变化' + '】')
            elif '时间' in c:
                text = 获取文本信息(device, prefix + c)
                try:
                    明细首条小时 = text.split(":")[0]
                    明细首条分钟 = text.split(":")[1]
                    if 涨跌停判断 or flag:
                        res = float(明细首条小时) >= 9
                        if 涨跌停判断:
                            device.log.info('【' + '涨跌停状态：校验' + text + '明细时间大于9:00' + '】')
                        else:
                            device.log.info('【' + '产品成交量较小：校验' + text + '明细时间大于9:00' + '】')
                    else:
                        当前小时 = datetime.datetime.now().hour
                        当前分钟 = datetime.datetime.now().minute
                        res = (float(当前小时) == float(明细首条小时) and 0 <= float(当前分钟) - float(
                            明细首条分钟) < 5) or (float(当前小时) - float(明细首条小时) == 1 and 0 < float(
                            明细首条分钟) + float(当前分钟) <= 65)
                        device.log.info('【' + '校验' + text + '与当前时间相差小于5min' + '】')
                except:
                    res = False
        if not res:
            device.log.info('【' + '%s 的值不符合校验标准' % (prefix + c) + '】')
            break
    return res


def 中原财升宝登录(device, 账号, 交易密码):
    if device.has_element('/中原证券财升宝/9.0/公告/关闭'):
        device['/中原证券财升宝/9.0/公告/关闭'].click()
    if device.has_element('/中原证券财升宝/9.0/今日打新提醒/关闭'):
        device['/中原证券财升宝/9.0/今日打新提醒/关闭'].click()
    device['/中原证券财升宝/9.0/交易界面'].click()
    time.sleep(3)
    # 登录非证书登录
    device['/中原证券财升宝/9.0/交易/普通交易/登录'].click()
    time.sleep(6)
    device['/中原证券财升宝/9.0/交易/普通交易/登录/请输入您的资金账号'].click()
    device['/中原证券财升宝/9.0/交易/普通交易/登录/请输入您的资金账号'] = 账号
    device['/中原证券财升宝/9.0/交易/普通交易/登录/请输入交易密码'].click()
    device['/中原证券财升宝/9.0/交易/普通交易/登录/请输入交易密码'] = 交易密码
    device['/中原证券财升宝/9.0/交易/普通交易/登录/普通交易登录按钮'].click()
    if device.has_element('/中原证券财升宝/9.0/公告/关闭'):
        device['/中原证券财升宝/9.0/公告/关闭'].click()
        time.sleep(2)
    if device.has_element('/中原证券财升宝/9.0/今日打新提醒/关闭'):
        device['/中原证券财升宝/9.0/今日打新提醒/关闭'].click()
        time.sleep(2)
    if device.has_element('/中原证券财升宝/9.0/清算提醒通知'):
        device['/中原证券财升宝/9.0/清算提醒通知/确认'].click()
    time.sleep(10)


def 万和手机证券交易登录(device, fund_account, fund_password):
    #   --------------登录---------------------
    time.sleep(10)
    if device.has_element('/万和手机证券/首页/跳出弹框_提示信息'):
        device['/万和手机证券/首页/跳出弹框_提示信息/按钮_确定按钮'].click()
    if device.has_element('/万和手机证券/首页/跳出弹框_提示信息/我知道了_按钮'):
        device['/万和手机证券/首页/跳出弹框_提示信息/我知道了_按钮'].click()
    time.sleep(3)
    if device.has_element('/万和手机证券/首页/新股新债申购/新债申购提醒'):
        device['/万和手机证券/首页/新股新债申购/新债申购提醒/取消'].click()
    time.sleep(2)
    device['/万和手机证券/交易'].click()
    if device.has_element('/万和手机证券/首页/跳出弹框_提示信息/我知道了_按钮'):
        device['/万和手机证券/首页/跳出弹框_提示信息/我知道了_按钮'].click()
    device['/万和手机证券/交易/按钮_立即登录'].click()
    time.sleep(2)
    坐标点击(device, 500 / 1080, 2232 / 2380)
    if device.has_element('/万和手机证券/交易/登录过程/是否存在账户账户'):
        device['/万和手机证券/交易/登录过程/账户输入框'] = fund_account
        time.sleep(1)
        device['/万和手机证券/交易/请输入交易密码'] = fund_password
        time.sleep(2)
        test_picture_context = 获取文本信息(device, '/万和手机证券/交易/验证码图片')
        device['/万和手机证券/交易/请输入验证码'].click()
        device['/万和手机证券/交易/请输入验证码'] = test_picture_context
        time.sleep(2)
        device['/万和手机证券/交易/登录按钮'].click()
    else:
        device['/万和手机证券/交易/请输入交易密码'] = fund_password
        time.sleep(2)
        test_picture_context = 获取文本信息(device, '/万和手机证券/交易/验证码图片')
        device['/万和手机证券/交易/请输入验证码'].click()
        device['/万和手机证券/交易/请输入验证码'] = test_picture_context
        time.sleep(2)
        坐标点击(device, 930 / 1080, 2259 / 2380)
        time.sleep(2)

    if device.has_element('/万和手机证券/交易/登录按钮/授权通知'):
        device['/万和手机证券/交易/登录按钮/授权通知/同意'].click()
        device['/万和手机证券/交易/登录按钮'].click()
    if device.has_element('/万和手机证券/首页/跳出弹框_提示信息/我知道了_按钮'):
        device['/万和手机证券/首页/跳出弹框_提示信息/我知道了_按钮'].click()
    time.sleep(10)
    # ===========登录结束========


def 万和阳指交易登录(device, fund_account, fund_password):
    if device.has_element('/万和e阳指/公告/今日不再提示'):
        device['/万和e阳指/公告/今日不再提示'].click()
        time.sleep(2)
    device['/万和e阳指/交易'].click()
    time.sleep(2)
    device['/万和e阳指/交易/登录_开户'].click()
    time.sleep(2)
    if device.has_element('/万和e阳指/交易/登录_开户/开始输入资金账户'):
        device['/万和e阳指/交易/登录_开户/开始输入资金账户'].click()
        time.sleep(2)
        输入英文数字(device, fund_account)
        time.sleep(2)
    device['/万和e阳指/交易/登录_开户/请输入交易密码'].click()
    time.sleep(2)
    输入英文数字(device, fund_password)
    time.sleep(2)
    device['/万和e阳指/交易/登录_开户/登录_按钮'].click()
    time.sleep(3)
    if device.has_element('/万和e阳指/交易/登录_开户/上次登录信息弹窗'):
        device['/万和e阳指/交易/登录_开户/上次登录信息弹窗_确定按钮'].click()
    time.sleep(8)


def 安信滑块拖动(device, path):
    from appium.webdriver.common.touch_action import TouchAction
    import base64
    import requests
    import random
    import os
    from PIL import Image

    def request_slider(img_path):
        url = slider_url
        with open(img_path, 'rb') as f:
            image = base64.b64encode(f.read()).decode()
        data = {
            'image': image,
        }
        response = requests.post(url, data=data, timeout=60).json()
        return response.get('instance')

    filename = 'pictures/handle/' + "image_screen_" + str(random.randint(10, 99)) + ".png"
    device.appium.driver.save_screenshot(filename)
    element = 获取控件对象(device, path)
    # 截屏
    # 获取元素上下左右的位置
    left = element.location['x']
    top = element.location['y']
    right = element.location['x'] + element.size['width']
    bottom = element.location['y'] + element.size['height']
    # 打开刚才的截图
    im = Image.open(filename)
    # 截取
    im = im.crop((left, top, right, bottom))
    img = 'pictures/handle/' + str(int((time.time() * 10000))) + ".png"
    im.save(img)
    im.close()
    instance = request_slider(img)
    action = TouchAction(device.appium.driver)
    # action.press(x=231, y=1441).wait(1000).move_to(x=231 + instance, y=1441).release().perform()
    # action.press(x=232, y=1388).wait(1000).move_to(x=221 + instance, y=1388).release().perform()  # 9xp
    action.press(x=150, y=2240).wait(1000).move_to(x=150 + instance, y=2240).release().perform()
    if os.path.exists(filename):
        os.remove(filename)
    if os.path.exists(img):
        os.remove(img)


def 颜色辨别(device, path, colour):
    from PIL import Image
    img2 = Image.open(path)
    img2 = img2.convert('RGBA')
    pixdata = img2.load()
    red_num = 0
    green_num = 0
    grey_num = 0
    blue_num = 0
    light_yellow_num = 0
    dark_yellow_num = 0
    standard_yellow_num = 0
    light_blue_num = 0
    coffee_num = 0
    steel_blue_num = 0

    for y in range(img2.size[1]):
        for x in range(img2.size[0]):
            if pixdata[x, y][0] < 100 and pixdata[x, y][1] < 200 and pixdata[x, y][2] <= 100:
                green_num += 1
            if pixdata[x, y][0] > 200 and pixdata[x, y][1] < 100 and pixdata[x, y][2] < 100:
                red_num += 1
            if 130 < pixdata[x, y][0] < 150 and 130 < pixdata[x, y][1] < 150 and 130 < pixdata[x, y][2] < 150:
                grey_num += 1
            if pixdata[x, y][0] < 60 and 120 < pixdata[x, y][1] < 150 and 240 < pixdata[x, y][2]:
                blue_num += 1
            if 210 < pixdata[x, y][0] < 240 and 130 < pixdata[x, y][1] < 150 and pixdata[x, y][2] < 61:
                light_yellow_num += 1
            if 240 < pixdata[x, y][0] and 100 < pixdata[x, y][1] < 150 and pixdata[x, y][2] < 50:
                dark_yellow_num += 1
            if 240 < pixdata[x, y][0] and 200 > pixdata[x, y][1] > 150 and pixdata[x, y][2] < 100:
                standard_yellow_num += 1
            if 200 > pixdata[x, y][0] > 150 and 200 > pixdata[x, y][1] > 190 and 230 < pixdata[x, y][2] < 240:
                light_blue_num += 1
            if 200 > pixdata[x, y][0] > 170 and 150 > pixdata[x, y][1] > 120 and 70 < pixdata[x, y][2] < 100:
                coffee_num += 1
            if 120 > pixdata[x, y][0] >= 70 and 220 >= pixdata[x, y][1] >= 140 and 235 < pixdata[x, y][2]:
                steel_blue_num += 1
    colour_dict = {
        'red': red_num,
        'green': green_num,
        'grey': grey_num,
        'blue': blue_num,
        'light_yellow': light_yellow_num,
        'dark_yellow': dark_yellow_num,
        'standard_yellow': standard_yellow_num,
        'light_blue': light_blue_num,
        'coffee': coffee_num,
        'steel_blue': steel_blue_num
    }
    # device.log.info("图片像素点数统计:{}".format(colour_dict))
    device.log.info("图片像素点数统计 {}:{}".format(colour, colour_dict.get(colour)))
    img2.close()
    if colour_dict.get(colour) > 50:
        return True
    return False


def 京东滑块拖动(device, path):
    from appium.webdriver.common.touch_action import TouchAction
    import base64
    import requests
    import random
    import os
    from PIL import Image

    def request_slider(img_path):
        url = slider_url
        with open(img_path, 'rb') as f:
            image = base64.b64encode(f.read()).decode()
        data = {
            'image': image,
        }
        response = requests.post(url, data=data, timeout=60).json()
        return response.get('instance')

    filename = 'pictures/handle/' + "image_screen_" + str(random.randint(10, 99)) + ".png"
    device.appium.driver.save_screenshot(filename)
    element = 获取控件对象(device, path)
    # 截屏
    # 获取元素上下左右的位置
    left = element.location['x']
    top = element.location['y']
    right = element.location['x'] + element.size['width']
    bottom = element.location['y'] + element.size['height']
    # 打开刚才的截图
    im = Image.open(filename)
    # 截取
    im = im.crop((left, top, right, bottom))
    img = 'pictures/handle/' + str(int((time.time() * 10000))) + ".png"
    im.save(img)
    im.close()
    instance = request_slider(img)
    action = TouchAction(device.appium.driver)
    action.press(x=221, y=1875).wait(1000).move_to(x=167 + instance, y=1875).release().perform()
    if os.path.exists(filename):
        os.remove(filename)
    if os.path.exists(img):
        os.remove(img)


def 中泰线图是否存在(device, path):
    """
    路径id com.lphtsccft:id/hq_chartview
    判断RGB是否存在橘黄色 或 蓝色
    """
    from PIL import Image

    def mycrop(img_path, x1, x2, y1, y2, save_path):
        """
        裁剪图片
        :param img_path:
        :param x:
        :param y:
        :param save_path:
        :return:
        """
        import cv2
        img = cv2.imread(img_path)  # img_path为图片所在路径
        crop_img = img[y1:y2, x1:x2]  # x0,y0为裁剪区域左上坐标；x1,y1为裁剪区域右下坐标
        cv2.imwrite(save_path, crop_img)

    filename = str(int((time.time() * 10000))) + ".png"
    device.appium.driver.save_screenshot(filename)
    element = 获取控件对象(device, path)
    # 获取元素上下左右的位置
    left = element.location['x']
    top = element.location['y']
    right = element.location['x'] + element.size['width']
    bottom = element.location['y'] + element.size['height']
    # 打开刚才的截图
    im = Image.open(filename)
    # 截取对应位置
    im = im.crop((left, top, right, bottom))
    img = str(int((time.time() * 10000))) + ".png"
    im.save(img)
    im.close()
    k_img = str(int((time.time() * 10000))) + "_1.png"
    mycrop(img, 0, 1000, 0, 800, k_img)
    img2 = Image.open(k_img)
    img2 = img2.convert('RGBA')
    pixdata = img2.load()
    i = 0
    j = 0
    for y in range(img2.size[1]):
        for x in range(img2.size[0]):
            if pixdata[x, y][0] > 200 and 100 <= pixdata[x, y][1] <= 160 and 50 <= pixdata[x, y][2] <= 80:
                i += 1
            if pixdata[x, y][0] < 100 and pixdata[x, y][1] <= 100 and 150 <= pixdata[x, y][2]:
                j += 1
    print(i)
    print(j)
    if os.path.exists(filename):
        os.remove(filename)
    if os.path.exists(img):
        os.remove(img)
    if os.path.exists(k_img):
        os.remove(k_img)
    if i > 10 or j > 10:
        return True
    return False


def 控件图片存在返回坐标(device, platform):
    import requests
    import base64
    import numpy as np
    import cv2

    def save_img(img, img_name):
        image_data = np.fromstring(img, np.uint8)
        image_data = cv2.imdecode(image_data, cv2.IMREAD_COLOR)
        cv2.imwrite(img_name, image_data)

    def match(small_img, big_img):
        def search_returnPoint(img, template, template_size):
            img_gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
            template_ = cv2.cvtColor(template, cv2.COLOR_BGR2GRAY)
            result = cv2.matchTemplate(img_gray, template_, cv2.TM_CCOEFF_NORMED)
            threshold = 0.7
            # res大于70%
            loc = np.where(result >= threshold)
            # 使用灰度图像中的坐标对原始RGB图像进行标记
            point = ()
            for pt in zip(*loc[::-1]):
                cv2.rectangle(img, pt, (pt[0] + template_size[1], pt[1] + + template_size[0]), (7, 249, 151), 2)
                point = pt
            if point == ():
                return None, None, None
            return img, point[0] + template_size[1] / 2, point[1]

        scale = 1

        img = cv2.imread(big_img)  # 要找的大图
        img = cv2.resize(img, (0, 0), fx=scale, fy=scale)

        template = cv2.imread(small_img)  # 图中的小图
        template = cv2.resize(template, (0, 0), fx=scale, fy=scale)
        template_size = template.shape[:2]
        img, x_, y_ = search_returnPoint(img, template, template_size)
        return [x_, y_]

    # 全屏截图
    filename = str(int((time.time() * 10000))) + ".png"
    device.appium.driver.save_screenshot(filename)
    # 小图获取
    url = 'http://***********:5000/get_img'
    params = {
        'platform': platform
    }
    response = requests.get(url, params=params).json()
    img_name = str(int((time.time() * 10000))) + ".png"
    img = base64.b64decode(response['data'])
    save_img(img, img_name)
    return match(img_name, filename)


def 根据坐标匹配文本(device, site, ocr_result):
    target = site[1]
    numbers = [i['site'][1] for i in ocr_result]
    closest = min(numbers, key=lambda x: abs(x - target))
    print(closest)
    for j in ocr_result:
        if j['site'][-1] == closest:
            return j['text']
    return None


def 切换窗口(device, 窗口序号=1):
    """
        使用该方法 进入手机的H5页面
    """
    from appium.webdriver.mobilecommand import MobileCommand
    # content = 获取窗体位置(path)
    # print(content)
    窗口list = device.appium.driver.contexts
    # if len(窗口list) == 2:
    web窗口 = 窗口list[窗口序号]
    device.appium.driver.execute(MobileCommand.SWITCH_TO_CONTEXT, {"name": web窗口})
    # else:
    #     return False


def 切换窗体(device, 窗体序号=1):
    """
    使用该方法 进入h5页面的下一个H5页面
    """
    # handle = 获取窗体位置(path)
    # print(handle)
    handles = device.appium.driver.window_handles
    print(handles)
    device.appium.driver.switch_to.window(handles[窗体序号])


def 获取控件文本(device, path):
    text = 获取控件对象(device, path).text
    device.log.info('获取 <%s> 内容成功：%s。' % (path, text))
    return text


def 循环校验网页文本(device, prefix, contents):
    res = True
    for c in contents:
        text = 获取控件文本(device, prefix + c)
        res = text != '' and text is not None and text != '--'
        device.log.info('%s 的值为: %s' % ((prefix + c), text))
        if not res:
            break
    return res


def 开源线图是否存在(device, path):
    """
    路径id com.lphtsccft:id/hq_chartview
    判断RGB是否存在红绿蓝橙色
    """
    from PIL import Image

    def mycrop(img_path, x1, x2, y1, y2, save_path):
        """
        裁剪图片
        :param img_path:
        :param x:
        :param y:
        :param save_path:
        :return:
        """
        import cv2
        img = cv2.imread(img_path)  # img_path为图片所在路径
        crop_img = img[y1:y2, x1:x2]  # x0,y0为裁剪区域左上坐标；x1,y1为裁剪区域右下坐标
        cv2.imwrite(save_path, crop_img)

    filename = str(int((time.time() * 10000))) + ".png"
    device.appium.driver.save_screenshot(filename)
    element = 获取控件对象(device, path)
    # 获取元素上下左右的位置
    left = element.location['x']
    top = element.location['y']
    right = element.location['x'] + element.size['width']
    bottom = element.location['y'] + element.size['height']
    # 打开刚才的截图
    im = Image.open(filename)
    # 截取对应位置
    im = im.crop((left, top, right, bottom))
    img = str(int((time.time() * 10000))) + ".png"
    im.save(img)
    im.close()
    img2 = Image.open(img)
    img2 = img2.convert('RGBA')
    pixdata = img2.load()
    lightblue = 0
    for y in range(img2.size[1]):
        for x in range(img2.size[0]):
            if pixdata[x, y][0] == 233 and pixdata[x, y][1] == 244 and pixdata[x, y][2] == 254:
                lightblue += 1
    print(lightblue)
    if os.path.exists(filename):
        os.remove(filename)
    if os.path.exists(img):
        os.remove(img)
    if lightblue > 1:
        return True
    return False


def get_focused_activity(device_id):
    # 运行 adb shell dumpsys window activities 命令，并解析输出
    result = subprocess.run(
        ["adb", "-s", device_id, "shell", "dumpsys", "activity", "activities"],
        capture_output=True,
        text=True
    )
    output = result.stdout

    # 提取当前活动的名称
    for line in output.splitlines():
        if "mCurrentFocus=W" in line:
            return line.strip()

    return None


def 获取控件加载时间(device, path, find_path):
    def get_element(path, num):
        try:
            e = device[path]
            device.log.info('控件<{}>获取成功, 循环第{}次'.format(path, num))
            return e.ele
        except ElementNotExist:
            device.log.info('未查找到控件：{}, 循环第{}次'.format(path, num))
            return ''

    e = device.has_element(path, 2)
    # device.log.info('控件位置{}'.format(e.location_middle))

    _device_id = device.device_id

    previous_activity = None
    previous_time = None

    previous_activity = get_focused_activity(_device_id)

    previous_time = time.time()

    command = ['adb', '-s', _device_id, 'shell', 'input', 'tap', str(500), str(1150)]
    process = subprocess.Popen(command, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    # subprocess.run(command, check=True)
    device.log.info('点击' + path)
    try:
        while True:
            current_activity = get_focused_activity(_device_id)
            current_time = time.time()
            if current_time - previous_time > 30:
                device.log.info('登录activity切换超时')
                break

            if current_activity != previous_activity:
                if previous_activity is not None:
                    switch_duration = current_time - previous_time
                    # device.log.info('检查到activity切换从{}, 到{}'.format(previous_activity, current_activity))
                    device.log.info('检查到activity切换')
                    device.log.info(f'登录activity切换耗时{switch_duration:.2f}秒#save#')
                    break
    except KeyboardInterrupt:
        print("Monitoring stopped.")

    stdout, stderr = process.communicate()

    # 点击(device, path)

    current_time = time.time()
    for i in range(10):
        element_obj = get_element(find_path, i + 1)
        if element_obj:
            ues_time = round(time.time() - current_time, 2)
            return ues_time
    return ''


def 线图是否存在(device, img, r=255, g=0, b=0):
    """
    img传递图片，可以用坐标截取()来截取图片
    r, g, b传递线图中某条线的rgb数值，去网上搜一下rgb识别工具
    """
    from PIL import Image
    img2 = Image.open(img)
    img2 = img2.convert('RGBA')
    pixdata = img2.load()
    colorCount = 0
    for y in range(img2.size[1]):
        for x in range(img2.size[0]):
            if pixdata[x, y][0] == r and pixdata[x, y][1] == g and pixdata[x, y][2] == b:
                colorCount += 1
    print(colorCount)
    if os.path.exists(img):
        os.remove(img)
    if colorCount >= 1:
        return True
    return False


def 网络检测(device, host, count='6'):
    import subprocess

    param = '-n' if subprocess.os.name == 'nt' else '-c'
    command = ['ping', param, count, host]
    try:
        output = subprocess.check_output(command, stderr=subprocess.STDOUT, universal_newlines=True)
        return output
    except subprocess.CalledProcessError as e:
        return f"Ping failed: {e.output}"


def 智能识别左滑(device, ctx):
    import os
    import time
    try:
        from PIL import Image
    except Exception as e:
        return e
    # reader= easyocr.Reader(['ch_sim','en']) # ,detail=0
    # result=reader.readtext(filename)

    # ctx规则:
    # 1.找到文字后在该文字的y轴左滑，例：交易
    # 2.方位|文字，例：底部|交易；支持【左侧 右侧 顶部 底部 中间】
    # 3.相对定位|方位|文字，例：打新股|下方|交易；支持【上方 下方 左方 右方】
    ctx_list = ctx.split('|')
    print(ctx_list)
    filename = str(int((time.time() * 10000))) + ".png"
    device.appium.driver.save_screenshot(filename)
    img_f = Image.open(filename)
    w = img_f.width  # 图片的宽
    h = img_f.height  # 图片的高
    img_f.close()

    result = []
    online_ai = False
    try:
        # gpu服务器
        headers = {"Content-type": "application/json"}
        fo = open(filename, 'rb')
        img = fo.read()
        if img is None:
            print("error in loading image:{}".format(filename))
            return False
        server_url = gpu_ocr_url
        data = {'images': [cv2_to_base64(img)]}
        fo.close()
        ai_response = requests.post(url=server_url, headers=headers, data=json.dumps(data))

        if str(ai_response.status_code) == '200':
            online_ai = True
            result = ai_response.json()["results"][0]
        else:
            print('识别失败')
            return False
    except Exception as e:
        try:
            import easyocr
            from paddleocr import PaddleOCR

            # 本地飞桨
            ocr = PaddleOCR(use_angle_cls=True, lang='ch')
            result = ocr.ocr(filename, cls=True)
        except Exception as e:
            return e

    def get_location(res):
        x0 = res[0][0][0]
        y0 = res[0][0][1]

        x_t = (res[0][1][0] - res[0][0][0]) / 2
        y_t = (res[0][3][1] - res[0][0][1]) / 2

        x = x0 + x_t
        y = y0 + y_t
        return x, y

    def get_location_pd(res):
        x0 = res[0][0]
        y0 = res[0][1]

        x_t = (res[1][0] - res[0][0]) / 2
        y_t = (res[3][1] - res[0][1]) / 2

        x = x0 + x_t
        y = y0 + y_t
        return x, y

    punctuation = r"""!"#$%&'()*+,-./:;<=>?@[\]^_`{|}~，。、丶；‘’：“”"""
    print('================{}'.format(result))
    if len(result) > 0:
        if len(ctx_list) == 1:
            for res in result:
                if online_ai:
                    test = res['text'].strip(punctuation)
                    text_region = res['text_region']
                else:
                    test = res[1][0]
                    text_region = res[0]

                if ctx_list[0] == test:
                    x, y = get_location_pd(text_region)
                    device.log.info('找到【%s】,坐标(%s,%s)，在y=%s左滑' % (ctx_list[0], x, y, y), '')
                    if int(y) < 2289:
                        device.appium.driver.swipe(980, y, 230, y, 1000)
                    else:
                        y = y / h * 2289
                        device.appium.driver.swipe(980, y, 230, y, 1000)
                        break
        elif len(ctx_list) == 2:
            for res in result:
                if online_ai:
                    test = res['text'].strip(punctuation)
                    text_region = res['text_region']
                else:
                    test = res[1][0]
                    text_region = res[0]

                if ctx_list[1] == test:
                    x, y = get_location_pd(text_region)
                    if ctx_list[0] == '左侧' and x < w / 2:
                        device.log.info('在左侧找到【%s】,坐标(%s,%s)，在y=%s左滑' % (ctx_list[0], x, y, y), '')
                        device.appium.driver.swipe(980, y, 230, y, 1000)
                        break
                    elif ctx_list[0] == '右侧' and x > w / 2:
                        device.log.info('在右侧找到【%s】,坐标(%s,%s)，在y=%s左滑' % (ctx_list[0], x, y, y), '')
                        device.appium.driver.swipe(980, y, 230, y, 1000)
                        break
                    elif ctx_list[0] == '顶部' and y < h / 4:
                        device.log.info('在顶部找到【%s】,坐标(%s,%s)，在y=%s左滑' % (ctx_list[0], x, y, y), '')
                        device.appium.driver.swipe(980, y, 230, y, 1000)
                        break
                    elif ctx_list[0] == '底部' and y > (3 * h / 4):
                        device.log.info('在底部找到【%s】,坐标(%s,%s)，在y=%s左滑' % (ctx_list[0], x, y, y), '')
                        device.appium.driver.swipe(980, y, 230, y, 1000)
                        break
                    elif ctx_list[0] == '中间' and y > h / 4 and y < (3 * h / 4):
                        device.log.info('在中间找到【%s】,坐标(%s,%s)，在y=%s左滑' % (ctx_list[0], x, y, y), '')
                        device.appium.driver.swipe(980, y, 230, y, 1000)
                        break
        elif len(ctx_list) == 3:
            # ctx_list[0] 相对元素
            # ctx_list[1] 方位
            # ctx_list[2] 点击元素

            relative_x = -1
            relative_y = -1
            for res in result:
                print(res)
                if online_ai:
                    test = res['text'].strip().strip(punctuation)
                    text_region = res['text_region']
                else:
                    test = res[1][0]
                    text_region = res[0]

                # 获取相对元素的坐标
                if ctx_list[0] == test:
                    relative_x, relative_y = get_location_pd(text_region)
                    print(relative_x, relative_y)
                    device.log.info('相对元素【%s】,坐标(%s,%s)' % (ctx_list[0], relative_x, relative_x), '')
                    break

            if relative_x > 0 and relative_y > 0:
                print('相对元素定位')
                for res in result:
                    if online_ai:
                        test = res['text'].strip().strip(punctuation)
                        text_region = res['text_region']
                    else:
                        test = res[1][0]
                        text_region = res[0]
                    if ctx_list[2] == test:
                        x, y = get_location_pd(text_region)
                        if ctx_list[1] == '左方' and x < relative_x:
                            device.log.info('找到【%s】左方【%s】,坐标(%s,%s)' % (ctx_list[0], ctx_list[2], x, y), '')
                            device.appium.driver.swipe(980, y, 230, y, 1000)
                            break
                        elif ctx_list[1] == '右方' and x > relative_x:
                            device.log.info('找到【%s】右方【%s】,坐标(%s,%s)' % (ctx_list[0], ctx_list[2], x, y), '')
                            device.appium.driver.swipe(980, y, 230, y, 1000)
                            break
                        elif ctx_list[1] == '上方' and y < relative_y:
                            device.log.info('找到【%s】上方【%s】,坐标(%s,%s)' % (ctx_list[0], ctx_list[2], x, y), '')
                            device.appium.driver.swipe(980, y, 230, y, 1000)
                            break
                        elif ctx_list[1] == '下方' and y > relative_y:
                            device.log.info('找到【%s】下方【%s】,坐标(%s,%s)' % (ctx_list[0], ctx_list[2], x, y), '')
                            device.appium.driver.swipe(980, y, 230, y, 1000)
                            break
            else:
                for res in result:
                    if online_ai:
                        test = res['text'].strip().strip(punctuation)
                        text_region = res['text_region']
                    else:
                        test = res[1][0]
                        text_region = res[0]
                    if ctx_list[2] == test:
                        x, y = get_location_pd(text_region)
                        device.log.info('点击【%s】,坐标(%s,%s)' % (ctx_list[2], x, y), '')
                        device.appium.driver.swipe(980, y, 230, y, 1000)
                        break

    if os.path.exists(filename):
        os.remove(filename)


def 拼接图片(device, image_list: list, output_path: str, header_height: int = 255, footer_height: int = 300,
             x_columns=None, ext: str = 'png'):
    """
    图片拼接
    Args:
        image_list(list): 图片路径列表
        output_path(str): 拼接后图片的输出路径
        header_height(int): 头部高度
        footer_height(int): 尾部高度
        x_columns(list): 指定对比的列的x坐标
        ext(str): 图片格式
    """
    from PIL import Image

    def preprocess(image_list: list, header_height: int, footer_height: int, output_path: str, ext: str) -> tuple:
        """
        图片预处理
        Args:
            image_list(list): 图片路径列表
            header_height(int): 头部高度
            footer_height(int): 尾部高度
            output_path(str): 处理后图片的输出目录
            ext(str): 图片格式
        Returns:
            tuple: 原始图片列表和处理后的图片列表
        """
        origin_image_list = []
        processed_image_list = []
        i = 0
        for image in image_list:
            i += 1
            # 打开图片
            img = Image.open(image)
            origin_image_list.append(img)
            # 转换为灰度图
            img = img.convert('L')
            # 剪裁头尾, header_height & footer_height
            img = img.crop((0, header_height, img.width, img.height - footer_height))
            # 保存图片到临时文件夹
            img.save(os.path.join(output_path, f'1-{i}.{ext}'))
            processed_image_list.append(img)
        return origin_image_list, processed_image_list

    def get_columns_color(img: Image, columns: list) -> list:
        """
        获取指定列的颜色
        Args:
            img(Image): 图片
            columns(list): 指定的图片列
        Returns:
            list(list): 图片每列的颜色列表
        """
        from PIL import Image
        columns_colors = []
        for column in columns:
            column_colors = []
            for row in range(img.height):
                color = img.getpixel((column, row))
                column_colors.append(color)
            columns_colors.append(column_colors)
        return columns_colors

    def calc_average_absolute_deviation(list1: list, list2: list, shift: int) -> tuple:
        """
        计算平均绝对偏差
        Args:
            list1(list): 列表1
            list2(list): 列表2
            shift(int): 偏移量
        Returns:
            tuple: 偏移量和平均绝对偏差
        """
        total_diff = 0
        count_pixels = 0

        for column in range(len(list1)):
            column1 = list1[column]
            column2 = list2[column]
            sum_column_diff = 0
            count_pixel = 0
            for y_pixel in range(shift, len(column1)):
                sum_column_diff += abs(column1[y_pixel] - column2[y_pixel - shift])
                count_pixel += 1
            total_diff += sum_column_diff
            count_pixels += count_pixel

        return shift, (total_diff // count_pixels)

    def find_coincidence(list1: list, list2: list) -> int:
        """
        寻找重合列
        Args:
            list1(list): 颜色灰度值列表1
            list2(list): 颜色灰度值列表2
        Returns:
            int: 偏移量
        """
        sum_diff_list = []
        for i in range(0, len(list1[0])):
            diff = calc_average_absolute_deviation(list1, list2, i)
            sum_diff_list.append(diff)

        # 查找"平均绝对差"最小的值， min_diff = （<偏移值>，<平均绝对差>）
        min_diff = sum_diff_list[0]
        for diff in sum_diff_list:
            if diff[1] < min_diff[1]:
                min_diff = diff

        print(min_diff)
        return min_diff[0]

    def remove_black_bottom(image_path: str, output_path: str):
        """
        去除图片底部黑色部分
        Args:
            image_path(str): 图片路径
            output_path(str): 输出路径
        """
        # 打开图片
        from PIL import Image
        img = Image.open(image_path)
        # 转换为灰度图
        gray_img = img.convert('L')
        width, height = img.size
        # 从下往上扫描，找到第一个非黑色像素的位置
        for y in range(height - 1, -1, -1):
            row_is_black = True
            for x in range(width):
                if gray_img.getpixel((x, y)) > 5:  # 如果像素值大于5，则认为不是黑色
                    row_is_black = False
                    break
            if not row_is_black:
                break
        # 裁剪图片
        cropped_img = img.crop((0, 0, width, y + 1))
        # 保存裁剪后的图片
        cropped_img.save(output_path)

    if x_columns is None:
        x_columns = [340, 500, 660]
    # 判断图片是否存在
    for image in image_list:
        if not os.path.exists(image):
            return False
    # 设置临时目录
    root_path = os.path.abspath(os.path.join(__file__, '../'))
    tmp_dir = os.path.join(root_path, 'tmp')
    if not os.path.exists(tmp_dir):
        os.makedirs(tmp_dir)
    # 设置默认输出路径
    if output_path is None:
        output_path = os.path.join(root_path, "out.png")
    # 图片预处理
    origin_image_list, processed_image_list = preprocess(image_list, header_height, footer_height, tmp_dir, ext)
    origin_img1 = origin_image_list[0]
    next_image_top = 0
    last_columns_colors = get_columns_color(processed_image_list[0], x_columns)
    # 创建足够大的画布
    long_img = Image.new('RGB', (origin_img1.width, origin_img1.height * len(origin_image_list) * 2))

    # 添加第一张图片
    device.log.info(f'处理第 1 张图片')
    header = origin_img1.crop((0, 0, origin_img1.width, origin_img1.height - footer_height))
    long_img.paste(header, (0, 0, header.width, header.height))
    long_img.save(os.path.join(tmp_dir, f'2-0.{ext}'))
    next_image_top += header.height
    # 处理剩余的图片
    for i in range(1, len(processed_image_list)):
        device.log.info(f'处理第 {i + 1} 张图片')
        t1 = time.time()

        # 获取当前图片的颜色列表
        current_columns_colors = get_columns_color(processed_image_list[i], x_columns)
        # 查找最佳重合位置
        shift = find_coincidence(last_columns_colors, current_columns_colors)
        # 更新用于对比的颜色列表
        last_columns_colors = current_columns_colors
        # 更新顶部位置
        next_image_top += shift
        # 获取原始图片
        origin_img = origin_image_list[i]
        # 裁剪并粘贴内容
        img_content = origin_img.crop((0, header_height, origin_img.width, origin_img.height - footer_height))
        long_img.paste(img_content, (0, next_image_top - img_content.height, img_content.width, next_image_top))

        # 保存中间结果
        long_img.save(os.path.join(tmp_dir, f'2-{i + 1}.{ext}'))

        t2 = time.time()
        device.log.info(f'处理耗时: {t2 - t1:.2f} 秒')
    footer_img = origin_image_list[-1]
    footer = footer_img.crop((0, footer_img.height - footer_height, footer_img.width, footer_img.height))
    long_img.paste(footer, (0, next_image_top, footer.width, next_image_top + footer.height))
    next_image_top += footer.height
    final_image_path = os.path.join(tmp_dir, f'2-end.{ext}')
    long_img.save(final_image_path)
    remove_black_bottom(final_image_path, output_path)
    device.log.info(f'拼接完成，保存路径为：{output_path}')


def 获取最新图片(device, device_folder, filename):
    device_id = device.device_id
    command = f'adb -s {device_id} shell ls -t {device_folder}*.png {device_folder}*.jpg {device_folder}*.jpeg'
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        output = result.stdout.strip()
        if not output:
            device.log.info("未找到图片文件。")
            return None

        # 获取最新的图片文件名
        latest_image = output.split('\n')[0].strip()
        # 执行 ADB 命令，将最新的图片从手机下载到电脑
        download_command = f'adb -s {device_id} pull {latest_image} {filename}'
        subprocess.run(download_command, shell=True)

        # 删除图片
        delete_command = f'adb -s {device_id} shell rm {latest_image}'
        subprocess.run(delete_command, shell=True)

        device.log.info(f"最新的图片已下载到：{filename}")
        return filename
    except Exception as e:
        device.log.info(f"发生错误：{e}")
        return None


def 识别图片内容(device, filename):
    try:
        # gpu服务器
        headers = {"Content-type": "application/json"}
        fo = open(filename, 'rb')
        img = fo.read()
        if img is None:
            device.log.info("error in loading image:{}".format(filename))
            return False
        server_url = gpu_ocr_url
        data = {'images': [cv2_to_base64(img)]}
        fo.close()
        ai_response = requests.post(url=server_url, headers=headers, data=json.dumps(data))
        if str(ai_response.status_code) == '200':
            result = ai_response.json()["results"][0]
            return result
        else:
            device.log.info('识别失败')
            return False
    except:
        return False


def 多次截图(device, save_path, swipe_distance=800, similarity_threshold=0.95):
    """
    实现手机端长截图功能，滑倒底部停止截图
    :param save_path: 截图的保存路径前缀
    :param swipe_distance: 每次滑动的距离
    :param similarity_threshold: 判断是否滑动到底部的相似度阈值
    """
    from PIL import ImageChops, Image
    previous_screenshot = None
    index = 0

    while True:
        # 截取当前屏幕
        screenshot_path = os.path.join(save_path, f'{index:02d}.png')
        device.appium.driver.save_screenshot(screenshot_path)
        device.log.info(f'截图已保存至 {screenshot_path}')

        if previous_screenshot and screenshot_path:
            # 计算两次截图的差异
            with Image.open(previous_screenshot) as img1, Image.open(screenshot_path) as img2:
                diff = ImageChops.difference(img1, img2)
                diff = diff.convert('L')
                diff_pixels = diff.getdata()
                total_pixels = len(diff_pixels)
                non_zero_pixels = sum(pixel > 0 for pixel in diff_pixels)
                similarity = 1 - (non_zero_pixels / total_pixels)

                # 判断是否滑动到底部
                if similarity >= similarity_threshold:
                    os.remove(screenshot_path)
                    device.log.info("已滑动到底部，停止截图。")
                    break

        previous_screenshot = screenshot_path
        index += 1
        上滑(device)
        time.sleep(1)


def 获取所有图片(device, folder_path):
    png_files = []
    try:
        for root, dirs, files in os.walk(folder_path):
            for file in files:
                if file.lower().endswith('.png'):
                    png_files.append(os.path.join(root, file))
    except FileNotFoundError:
        device.log.info(f"错误：未找到文件夹 {folder_path}。")
    except Exception as e:
        device.log.info(f"发生未知错误：{e}")
    return png_files


def 坐标长按(device, x, y, second=1):
    w = device.appium.driver.get_window_size()['width']
    h = device.appium.driver.get_window_size()['height']
    from appium.webdriver.common.touch_action import TouchAction
    # action = TouchAction(全局变量.Drive)
    # action.long_press(x = x*w, y = y*h, duration=500).perform()
    TouchAction(device.appium.driver).long_press(x=x * w, y=y * h, duration=second * 1000).release().perform()
    device.log.info(f"在坐标位置 {x},{y} 长按 {second} 秒。")


def 等待元素出现(device, time, element_by, element, msg):
    """
    等待元素出现
    :param driver: driver
    :param time: 等待时间
    :param element_by: 元素类型
    :param element: 元素关键字
    :param msg: 输出信息
    :return:
    """
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support import expected_conditions
    from selenium.webdriver.support.wait import WebDriverWait
    return WebDriverWait(device.appium.driver, time).until(
        expected_conditions.presence_of_element_located((element_by, element)), msg)


def 自定义处理弹窗(device, use_default=True, optional_texts=None):
    """
    :param device: 设备对象
    :param use_default: 是否包含默认黑名单（默认True）
    :param optional_texts: 自定义要检测的文本列表（默认None，表示不添加自定义文本）
    """
    # 基础黑名单逻辑
    base_texts = ["确定", "知道了", '不再提醒'] if use_default else []

    # 合并列表并去重
    custom_texts = optional_texts if optional_texts else []
    blacklist_texts = list(set(base_texts + custom_texts))

    for text in blacklist_texts:
        try:
            el = device.appium.driver.find_element(By.XPATH, f"//*[contains(@text, '{text}')]")
            print(f"[弹窗处理] 点击: {text}")
            el.click()
        except NoSuchElementException:
            continue

def 国泰君安君弘安卓登录(device, 资金账号, 营业厅, 交易密码):
    """君弘 App 自动登录流程"""
    if 是否存在控件(device, '/国泰君安君弘/确定'):
        点击(device, '/国泰君安君弘/确定')
    time.sleep(3)

    点击(device, '/国泰君安君弘/交易')
    time.sleep(2)
    点击(device, '/国泰君安君弘/交易/普通交易/立即登录')
    time.sleep(5)

    点击(device, '/国泰君安君弘/交易/普通交易/立即登录/去登陆')
    点击(device, '/国泰君安君弘/交易登录/切换')
    点击(device, '/国泰君安君弘/交易登录/切换/添加账号')
    time.sleep(3)

    点击(device, '/国泰君安君弘/交易/登录/输入账户')
    device['/国泰君安君弘/交易/登录/输入账户'] = 资金账号

    点击(device, '/国泰君安君弘/交易/登录/选择登录账户')
    time.sleep(3)
    点击(device, '/国泰君安君弘/交易/登录/选择登录账户/营业厅')
    device['/国泰君安君弘/交易/登录/选择登录账户/营业厅'] = 营业厅
    time.sleep(3)
    点击(device, f'/国泰君安君弘/交易/登录/{营业厅}')
    time.sleep(1)

    点击(device, '/国泰君安君弘/交易/登录/普通')
    time.sleep(1)
    点击(device, '/国泰君安君弘/交易登录/输入交易密码')
    time.sleep(1)
    device['/国泰君安君弘/交易登录/输入交易密码'] = 交易密码
    点击(device, '/国泰君安君弘/交易登录/登录按钮')
    time.sleep(5)

    if 是否存在控件(device, '/国泰君安君弘/交易/登录/保留账户信息'):
        点击(device, '/国泰君安君弘/交易/登录/保留账户信息')
        time.sleep(5)

    自定义处理弹窗(device)
    点击(device, '/国泰君安君弘/交易/普通交易/切换金额显示')
    time.sleep(1)
    坐标点击(device, 836 / 1080, 157 / 2388)
    time.sleep(3)


def 国泰君安君弘安卓登录买入流程(device, 代码):
    点击(device, '/国泰君安君弘/交易/普通交易/买入')
    time.sleep(4)

    if 是否存在控件(device, '/国泰君安君弘/交易登录/普通交易/输入股票框'):
        点击(device, '/国泰君安君弘/交易登录/普通交易/输入股票框')
        device['/国泰君安君弘/交易登录/普通交易/输入股票'] = 代码
    else:
        点击(device, '/国泰君安君弘/交易登录/普通交易/买入/输入股票')
        device['/国泰君安君弘/交易登录/普通交易/买入/输入股票框'] = 代码
    time.sleep(5)

    try:
        跌停价 = 获取文本信息(device, '/国泰君安君弘/交易登录/普通交易/买入/跌停价').split('停')[1].strip()
    except:
        print("[买入失败] 无法获取跌停价")
        return False

    if '-' in 跌停价:
        print("[买入失败] 跌停价无效：", 跌停价)
        return False

    if 是否存在控件(device,'/国泰君安君弘/交易登录/普通交易/买入/跌停'):
        点击(device, '/国泰君安君弘/交易登录/普通交易/买入/跌停')
        time.sleep(1)
        for _ in range(4):
            坐标点击(device, 963 / 1080, 662 / 2388)
            time.sleep(0.5)

    股票代码 = 获取文本信息(device, '/国泰君安君弘/交易登录/普通交易/股票名称')
    点击(device, '/国泰君安君弘/交易登录/普通交易/买卖数量')
    time.sleep(0.5)
    点击(device, '/国泰君安君弘/交易登录/普通交易/买入卖出按钮')
    time.sleep(2)

    if 是否存在控件(device,'/国泰君安君弘/交易登录/普通交易/买入卖出按钮/当前委托价'):
        委托请求_委托价 = 获取文本信息(device, '/国泰君安君弘/交易登录/普通交易/买入卖出按钮/当前委托价')
        try:
            跌停价_f = float(跌停价)
            委托价_f = float(委托请求_委托价)
        except:
            print("[买入失败] 委托价或跌停价格式错误")
            return False

        if round(委托价_f - 跌停价_f, 2) <= 0.05:
            点击(device, '/国泰君安君弘/通用/确认')
            return True, 股票代码, 委托请求_委托价
        else:
            print("[买入失败] 当前委托价不符合预期")
            return False
    else:
        print("[买入失败] 委托价控件不存在")
        return False, None

def 国泰君安君弘安卓登录卖出流程(device, 代码):
    点击(device, '/国泰君安君弘/交易/普通交易/卖出')
    time.sleep(4)

    if 是否存在控件(device, '/国泰君安君弘/交易登录/普通交易/输入股票框'):
        点击(device, '/国泰君安君弘/交易登录/普通交易/输入股票框')
        device['/国泰君安君弘/交易登录/普通交易/输入股票'] = 代码
    else:
        点击(device, '/国泰君安君弘/交易登录/普通交易/买入/输入股票')
        device['/国泰君安君弘/交易登录/普通交易/买入/输入股票框'] = 代码
    time.sleep(5)

    try:
        涨停价 = 获取文本信息(device, '/国泰君安君弘/交易登录/普通交易/卖出/涨停价').split('停')[1].strip()
    except:
        print("[卖出失败] 无法获取涨停价")
        return False

    if '-' in 涨停价:
        print("[卖出失败] 涨停价无效：", 涨停价)
        return False

    if 是否存在控件(device, '/国泰君安君弘/交易登录/普通交易/卖出/涨停价'):
        点击(device, '/国泰君安君弘/交易登录/普通交易/卖出/涨停价')
        for _ in range(4):
            坐标点击(device, 405 / 1080, 662 / 2388)
            time.sleep(0.5)

    股票代码 = 获取文本信息(device, '/国泰君安君弘/交易登录/普通交易/股票名称')
    点击(device, '/国泰君安君弘/交易登录/普通交易/买卖数量')
    time.sleep(0.5)
    点击(device, '/国泰君安君弘/交易登录/普通交易/买入卖出按钮')
    time.sleep(2)

    if 是否存在控件(device, '/国泰君安君弘/交易登录/普通交易/买入卖出按钮/当前委托价'):
        委托请求_委托价 = 获取文本信息(device, '/国泰君安君弘/交易登录/普通交易/买入卖出按钮/当前委托价')
        try:
            涨停价_f = float(涨停价)
            委托价_f = float(委托请求_委托价)
        except:
            print("[卖出失败] 委托价或涨停价格式错误")
            return False

        if round(涨停价_f - 委托价_f, 2) <= 0.05:
            点击(device, '/国泰君安君弘/通用/确认')
            return True, 股票代码, 委托请求_委托价
        else:
            print("[卖出失败] 当前委托价不符合预期")
            return False
    else:
        print("[卖出失败] 委托价控件不存在")
        return False, Nonedef


def 国泰君安君弘安卓交易撤单流程(device, 实际结果, 委托价):
    """
    功能：执行君弘安卓 App 中的撤单流程
    其中 一级条件为正确的股票号码 二级条件为 撤单的价格和委托价格一致
    一级二级条件确定后，点击该条撤单单号判断合同号是否相同
    参数：
        device: 封装的设备操作对象，支持点击、查找、日志等方法
        实际结果: 下单后返回的文本结果，用于截取合同号进行比对
        委托价: 当前委托的价格，作为 xpath 查找元素的关键值
    返回：
        (bool, int): 是否成功撤单，以及命中的委托索引
    异常说明：
        - 若控件找不到会记录日志并跳过
        - 若点击异常会进入 except 块处理
    """

    time.sleep(2)  # 等待弹窗稳定加载，避免立即操作导致控件未渲染

    # 提取当前委托合同号，用于后续撤单匹配
    委托合同号 = 截取合同号(device, 实际结果)
    time.sleep(1)  # 稍作等待，确保页面响应完毕

    # 点击弹窗中的“确认”按钮
    点击(device, '/国泰君安君弘/通用/确认')
    time.sleep(2)

    # 进入“撤单”页面
    点击(device, '/国泰君安君弘/交易登录/普通交易/撤单')
    time.sleep(2)

    # 格式化委托价格，保留三位小数，以匹配页面展示格式
    委托价_fmt = f"{float(委托价):.3f}"

    尝试次数 = 3  # 最多尝试匹配 3 条委托记录

    for index in range(1, 尝试次数 + 1):
        # 构造 XPath，模糊匹配委托价格（第 index 条）
        xpath = f"(//*[contains(@text, '{委托价_fmt}')])[{index}]"
        elements = device.appium.driver.find_elements(By.XPATH, xpath)

        if not elements:
            device.log.info('[跳过] 没找到元素：%s' % xpath)
            continue

        try:
            # 点击目标委托记录
            elements[0].click()
            time.sleep(1)

            # 获取当前点击项的合同号
            合同号 = 获取文本信息(device, '/国泰君安君弘/交易登录/普通交易/撤单/委托详情/委托编号')

            # 判断合同号是否与下单结果一致
            if 合同号 == 委托合同号:
                # 点击撤单按钮
                点击(device, '/国泰君安君弘/交易登录/普通交易/撤单')
                time.sleep(1)

                # 确认撤单
                点击(device, '/国泰君安君弘/交易登录/普通交易/撤单/撤单确认')
                print(f"[成功] 撤单完成，匹配第 {index} 条委托")
                return True, index  # 返回成功标志和索引号

            else:
                # 合同号不匹配，返回上一层重试
                print(f"[跳过] 第 {index} 条合同号不匹配：{合同号}")
                返回(device)
                time.sleep(2)

        except Exception as e:
            # 点击或后续步骤失败，记录异常并继续尝试下一个
            device.log.error(f"[异常] 点击或处理委托失败（第 {index} 条）：{e}")
            continue

    # 所有尝试失败，记录错误日志
    device.log.error("[失败] 所有尝试均未找到匹配的合同号")
    return False, 尝试次数  # 返回失败标志和尝试次数

def 新股新债列表_数据过滤(device,data, categories=None, offset_days=7, reference_date=None, date_field='date_time'):
    """
    通用筛选：对每个 category 中的 date_field 列做区间过滤。
      - offset_days > 0: 筛选 (T, T+offset_days]，即未来 offset_days 天内的数据
      - offset_days < 0: 筛选 [T+offset_days, T)，即过去 abs(offset_days) 天内的数据

    参数:
      data            原始字典，结构同接口返回值
      categories      要筛选的类别列表，如 ['bj','new_bond','new_stock']，
                      也可传单个字符串 'new_stock'
      offset_days     偏移天数：正值表示未来 X 天，负值表示过去 X 天，默认 7
      reference_date  基准日期，datetime 类型；默认取当天（datetime.today()）
      date_field      申购类型： 申购日期（默认)date_time 上市日期 LISTING_DATE
    返回:
      dict: 每个 category 对应筛选后的列表
    """
    from datetime import datetime, timedelta

    # 支持直接传字符串
    if isinstance(categories, str):
        categories = [categories]
    # 默认处理三类
    if not categories:
        categories = ['bj', 'new_bond', 'new_stock']

    # 确定基准日 T
    if reference_date is None:
        reference_date = datetime.today()
    # 计算阈值
    threshold = reference_date + timedelta(days=offset_days)

    result = {}
    for cat in categories:
        items = data.get('data', {}).get(cat, [])
        filtered = []
        for item in items:
            dt_str = item.get(date_field)
            if not dt_str:
                # 如果上市日期为None
                if date_field == 'LISTING_DATE':
                    filtered.append(item)
                continue
            try:
                dt = datetime.strptime(dt_str, '%Y-%m-%d')
            except ValueError:
                continue

            if offset_days > 0:
                # —— T+X: 未来 offset_days 天，(T, T+X]
                if reference_date < dt <= threshold:
                    filtered.append(item)
            elif offset_days < 0:
                # —— T–X: 过去 abs(offset_days) 天，[T–X, T)
                if threshold <= dt < reference_date:
                    filtered.append(item)
            else:
                # offset_days == 0：仅筛选“严格大于”当日
                if dt > reference_date:
                    filtered.append(item)

        result[cat] = filtered

    return result


def 获取短信验证码(device):
    """
    通过ADB从连接的安卓设备获取验证码短信

    Args:
        device: 安卓设备的序列号，可通过adb devices查看

    Returns:
        提取到的验证码字符串，如果未找到则返回None
    """
    # 使用ADB命令获取最新短信
    adb_cmd = [
        "adb",
        "-s", device.device_id,  # 指定目标设备
        "shell",
        "content query --uri content://sms/inbox --projection body --sort 'date DESC LIMIT 1'"
    ]
    # 执行ADB命令，明确指定UTF-8编码
    result = subprocess.run(
        adb_cmd,
        capture_output=True,
        text=True,
        encoding='utf-8',  # 指定UTF-8编码
        errors='ignore',  # 忽略解码错误
        timeout=10
    )
    # 从输出中提取短信内容
    output = result.stdout

    match = re.search(r"body=(.*)", output)
    if match:
        sms_body = match.group(1)
    else:
        # 如果没有标准格式，直接使用全部输出
        sms_body = output

    # 提取4-6位数字验证码
    code_match = re.search(r"验证码\D*?(\d{4,6})", sms_body)
    if code_match:
        return code_match.group(1)

    device.log.info("未在短信中找到验证码")
    return None

def 徽商期货交易登录(device, 证券账户, 证卷账户密码):
    点击(device, '/徽商期货/安卓/交易')
    time.sleep(2)
    点击(device, '/徽商期货/安卓/交易/登录')
    time.sleep(2)
    if 是否存在控件(device, '/徽商期货/安卓/交易/登录/请输入账号'):
        输入文本(device, '/徽商期货/安卓/交易/登录/请输入账号', 证券账户)
    输入文本(device, '/徽商期货/安卓/交易/登录/密码框', 证卷账户密码)
    time.sleep(1)
    点击(device, '/徽商期货/安卓/交易/登录/登录交易')
    time.sleep(3)
    返回(device)
    if 是否存在控件(device, '/徽商期货/安卓/交易/实盘/交易结算单子/确认'):
        点击(device, '/徽商期货/安卓/交易/实盘/交易结算单子/确认')
        time.sleep(3)
        返回(device)

def 中国银河普通登录(device, 账号, 密码):
    点击(device, '/中国银河/交易/普通/登录')
    time.sleep(1)
    if 是否存在控件(device, '/中国银河/交易/普通/登录/今日公告关闭'):
        点击(device, '/中国银河/交易/普通/登录/今日公告关闭')
    点击(device, '/中国银河/交易/普通/登录/账号')
    输入文本(device, '/中国银河/交易/普通/登录/账号', 账号)
    点击(device, '/中国银河/交易/普通/登录/密码')
    time.sleep(1)
    device.appium.driver.tap([(200, 2320)], 100)  # 点击键盘ABC
    time.sleep(1)
    device.appium.driver.tap([(100, 2010)], 100)  # 点击a
    device.appium.driver.tap([(100, 2010)], 100)
    device.appium.driver.tap([(100, 2010)], 100)
    device.appium.driver.tap([(100, 2010)], 100)
    device.appium.driver.tap([(100, 2010)], 100)
    device.appium.driver.tap([(100, 2010)], 100)
    # 输入文本('/中国银河/交易/普通/登录/密码', 密码)
    坐标点击(device, 920 / 1080, 1688 / 2289)

    yzm = 新获取验证码(device, '/中国银河/交易/普通/登录/验证码图片')
    print(yzm)
    time.sleep(1)
    点击(device, '/中国银河/交易/普通/登录/验证码')
    输入文本(device, '/中国银河/交易/普通/登录/验证码', yzm)
    点击(device, '/中国银河/交易/普通/登录/登录')
    time.sleep(1)
    if 是否存在控件(device, '/中国银河/交易/普通/登录/验证码错误'):
        # 再试一次
        点击(device, '/中国银河/交易/普通/登录/验证码错误确定')
        device.appium.driver.tap([(912, 1703)], 100)
        yzm = 新获取验证码(device, '/中国银河/交易/普通/登录/验证码图片')
        点击(device, '/中国银河/交易/普通/登录/验证码')
        输入文本(device, '/中国银河/交易/普通/登录/验证码', yzm)
        点击(device, '/中国银河/交易/普通/登录/登录')
        time.sleep(1)
    if 是否存在控件(device, '/中国银河/推送关闭'):
        点击(device, '/中国银河/推送关闭')

def 中国银河信用登录(device, 账号, 密码):
    点击(device, '/中国银河/交易/普通/登录')
    time.sleep(1)
    if 是否存在控件(device, '/中国银河/交易/普通/登录/今日公告关闭'):
        点击(device, '/中国银河/交易/普通/登录/今日公告关闭')
    点击(device, '/中国银河/交易/普通/登录/账号')
    输入文本(device, '/中国银河/交易/普通/登录/账号', 账号)
    点击(device, '/中国银河/交易/普通/登录/密码')
    time.sleep(1)
    device.appium.driver.tap([(200, 2350)], 100)  # 点击键盘ABC
    time.sleep(1)
    device.appium.driver.tap([(100, 2010)], 100)  # 点击a
    device.appium.driver.tap([(100, 2010)], 100)
    device.appium.driver.tap([(100, 2010)], 100)
    device.appium.driver.tap([(100, 2010)], 100)
    device.appium.driver.tap([(100, 2010)], 100)
    device.appium.driver.tap([(100, 2010)], 100)
    # 输入文本('/中国银河/交易/普通/登录/密码', 密码)
    坐标点击(device, 920 / 1080, 1688 / 2289)

    yzm = 新获取验证码(device, '/中国银河/交易/普通/登录/验证码图片')
    print(yzm)
    time.sleep(1)
    点击(device, '/中国银河/交易/普通/登录/验证码')
    输入文本(device, '/中国银河/交易/普通/登录/验证码', yzm)
    点击(device, '/中国银河/交易/普通/登录/登录')
    time.sleep(1)
    if 是否存在控件(device, '/中国银河/交易/普通/登录/验证码错误'):
        # 再试一次
        点击(device, '/中国银河/交易/普通/登录/验证码错误确定')
        device.appium.driver.tap([(912, 1703)], 100)
        yzm = 新获取验证码(device, '/中国银河/交易/普通/登录/验证码图片')
        点击(device, '/中国银河/交易/普通/登录/验证码')
        输入文本(device, '/中国银河/交易/普通/登录/验证码', yzm)
        点击(device, '/中国银河/交易/普通/登录/登录')
        time.sleep(1)
    if 是否存在控件(device, '/中国银河/推送关闭'):
        点击(device, '/中国银河/推送关闭')


def 进入应用顶层主框架(device):
    if 是否存在控件(device, '/中国银河/推送关闭'):
        点击(device, '/中国银河/推送关闭')
    主体底部Tabs = ''
    while 主体底部Tabs == '':
        主体底部Tabs = 获取控件对象(device,'/底部导航栏/首页界面跳转按钮')
        
        if 主体底部Tabs:
            device.log.info('已返回应用顶层tabs')
        else:
            device.appium.driver.back()
            取消按钮 = 获取控件对象(device,'/中国银河/应用窗体/系统/提示窗/取消')
            if 取消按钮 is not None and 取消按钮 != "":
                取消按钮.click()
                time.sleep(1)
                device.log.info('已返回到顶层')
                break
            device.log.info('返回上一级')
    点击(device, '/中国银河/交易')


def 进入银河应用顶层主框架(device):
    device.appium.driver.tap([(540, 1495)], 100)
    device.appium.driver.tap([(540, 1495)], 100)
    # if 是否存在控件(device, '/中国银河/推送关闭'):
    #     点击(device, '/中国银河/推送关闭')
    if 是否存在控件(device, '/中国银河/首页'):
        device.log.info('已返回应用顶层tabs')
    else:
        device.appium.driver.back()
        while True:
            取消按钮 = 获取控件对象(device, '/中国银河/应用窗体/系统/提示窗/取消')
            if 取消按钮 is not None and 取消按钮 != "":
                取消按钮.click()
                time.sleep(1)
                device.log.info('已返回到顶层')
                break
            else:
                device.appium.driver.back()
                device.log.info('返回上一级')
                time.sleep(1)
    点击(device, '/中国银河/交易')

def 进入海通应用顶层主框架(device):
    if 是否存在控件(device, '/e海通财/系统维护弹窗/关闭'):
        点击(device, '/e海通财/系统维护弹窗/关闭')

    主体底部Tabs = ''
    while 主体底部Tabs == '':
        主体底部Tabs = 获取控件对象(device, '/e海通财/交易界面')

        if 主体底部Tabs:
            print('已返回应用顶层tabs')
        else:
            全局变量.Drive.back()
            取消按钮 = 获取控件对象(device, '/e海通财/交易/普通/登录/系统提示/忽略')
            if 取消按钮 is not None and 取消按钮 != "":
                取消按钮.click()
                time.sleep(1)
                print('已返回到顶层')
                break
            print('返回上一级')

def 银河验证普通登录(device):
    点击(device,'/中国银河/交易/普通')
    time.sleep(1)
    if 是否存在控件(device,'/中国银河/交易/欢迎来到银河证券'):
        # 存在欢迎登录提示为未登录状态
        return False
    else:
        # 这里可以再加一下登录后界面验证
        return True

def 银河验证信用登录(device):
    点击(device,'/中国银河/交易/两融')
    time.sleep(1)
    if 是否存在控件(device,'/中国银河/交易/欢迎来到银河证券'):
        # 存在欢迎登录提示为未登录状态
        return False
    else:
        # 这里可以再加一下登录后界面验证
        return True

def 徽商上滑固定元素(device, duration=2000, percentage=0.6):
    time.sleep(1)
    l = device.screen_width, device.screen_height
    x1 = int(l[0] * 0.18)
    x2 = int(l[0] * 0.18)
    y1 = int(l[1] * 0.85)
    y2 = int(l[1] * 0.8)
    device.appium.driver.swipe(x1, y1, x2, y2, duration)



def 徽商下滑固定元素(device,delaytime=3000):
    time.sleep(2)
    l = device.screen_width, device.screen_height
    x1 = int(l[0] * 0.18)
    x2 = int(l[0] * 0.18)
    y1 = int(l[1] * 0.75)
    y2 = int(l[1] * 0.95)
    device.appium.driver.swipe(x1, y1, x2, y2, delaytime)