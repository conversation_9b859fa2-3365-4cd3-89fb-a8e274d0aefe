from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('行情/自选股/深证成指/深证成指')
def _48591197404e813a03def56ff0d7b9db(device):
    登录前页面处理(device)
    print(device.appium.driver)
    点击(device, '/底部导航栏/行情界面跳转按钮')
    目录校验(device, '一', '行情', '/行情/选中状态')
    点击(device, '/行情/自选股/自选股标签')
    目录校验(device, '二', '自选股', '/行情/自选股')
    if 获取文本信息(device, '/行情/自选股/深圳成指/行情点数') == '':
        print('刷新页面_行情')
        点击(device, '/底部导航栏/交易界面跳转按钮')
        点击(device, '/底部导航栏/行情界面跳转按钮')
        sleep(5)
    深圳成指行情点数 = 获取文本信息(device, '/行情/自选股/深圳成指/行情点数')
    结果 = 深圳成指行情点数 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


