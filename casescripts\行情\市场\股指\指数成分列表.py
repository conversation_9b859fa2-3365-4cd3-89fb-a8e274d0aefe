from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('行情/市场/股指/指数成分列表/指数成分校验')
def _ce0d206d615550d64b38fe698e4c7907(device):
    登录前页面处理(device)
    print(device.appium.driver)
    点击(device, '/底部导航栏/行情界面跳转按钮')
    目录校验(device, '一', '行情', '/行情/选中状态')
    点击(device, '/行情/市场/市场标题栏')
    点击(device, '/行情/市场/股指/股指标签')
    目录校验(device, '二', '股指', '/行情/更多/选中状态')
    上证指数 = 获取控件对象(device, '/行情/市场/股指/国内指数/指数成分列表/上证指数')
    深证成指 = 获取控件对象(device, '/行情/市场/股指/国内指数/指数成分列表/创业板指')
    创业板指 = 获取控件对象(device, '/行情/市场/股指/国内指数/指数成分列表/深证成指')
    沪深300 = 获取控件对象(device, '/行情/市场/股指/国内指数/指数成分列表/沪深300')
    中小板指 = 获取控件对象(device, '/行情/市场/股指/国内指数/指数成分列表/中小板指')
    结果 = 上证指数 != '' and 深证成指 != '' and 创业板指 != '' and 沪深300 != '' and 中小板指 != ''
    错误校验点 = '列表中丢失部分指数'
    return 结果, 错误校验点


