from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('交易/融资融券/担保品买入/买券还券')
def _7de4b3836d7e749f432c907e4449103c(device, 账号='01066033', 交易密码='123123', 通信密码='123123', 证券代码='010303', 交易数量='100', 预期结果='成功'):
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/融资融券')
    点击(device, '/交易/融资融券/融资融券首页/担保品买入按钮')
    点击(device, '/交易/融资融券/融资融券首页/买券还券')
    目录校验(device, '二', '担保卖出', '/页面标题')
    点击(device, '/交易/融资融券/还款还券/买券还券页面/请输入股票代码框')
    输入文本(device, '/交易/融资融券/还款还券/买券还券页面/请输入股票代码框', 证券代码)
    点击(device, '/交易/融资融券/还款还券/买券还券页面/跌停价')
    点击(device, '/交易/融资融券/增加数量')
    # 点击('/交易/融资融券/还款还券/买券还券页面/买入数量输入框')
    # 输入文本('/交易/融资融券/还款还券/买券还券页面/买入数量输入框', 交易数量)
    点击(device, '/交易/融资融券/还款还券/买券还券页面/买入按钮')
    点击(device, '/交易/融资融券/还款还券/买券还券页面/买入确定按钮')
    实际结果 = 获取文本信息(device, '/交易/融资融券/还款还券/买券还券页面/系统提示')
    错误校验点 = 非空校验(device, 预期结果, 实际结果)
    结果 = 预期结果 in 实际结果
    点击(device, '/交易/融资融券/还款还券/买券还券页面/委托确定按钮')
    print(结果)
    return 结果, 错误校验点


@casescript('交易/融资融券/担保品买入/担保品买入')
def _dba0cf8d797484a99731dd529f033e06(device, 账号='30002187', 交易密码='123123', 通信密码='123123', 证券代码='600123', 交易数量='100', 预期结果='委托成功'):
    登录前页面处理(device)
    # time.sleep(15)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/融资融券')
    点击(device, '/交易/融资融券/融资融券首页/担保品买入按钮')
    点击(device, '/交易/融资融券/融资融券首页/担保品买入')
    目录校验(device, '二', '担保买入', '/页面标题')
    输入文本(device, '/交易/融资融券/担保品买入页面/请输入股票代码框', 证券代码)
    点击(device, '/交易/融资融券/还款还券/买券还券页面/跌停价')
    点击(device, '/交易/融资融券/增加数量')
    点击(device, '/交易/融资融券/担保品买入页面/买入按钮')
    点击(device, '/交易/融资融券/担保品买入页面/买入确定按钮')
    实际结果 = 获取文本信息(device, '/交易/融资融券/担保品买入页面/系统提示')
    错误校验点 = 非空校验(device, 预期结果, 实际结果)
    结果 = 预期结果 in 实际结果
    点击(device, '/交易/融资融券/担保品买入页面/委托确定按钮')
    return 结果, 错误校验点


@casescript('交易/融资融券/担保品买入/融资买入')
def _7f9c75fb0e92dd424e8484be357158d6(device, 账号='01004402', 交易密码='123123', 通信密码='123123', 证券代码='510180', 交易数量='100', 预期结果='融资融券委托成功'):
    # 初始化
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/融资融券')
    点击(device, '/交易/融资融券/融资融券首页/担保品买入按钮')
    点击(device, '/交易/融资融券/融资融券首页/融资买入')
    目录校验(device, '二', '融资买入', '/页面标题')
    点击(device, '/交易/融资融券/融资买入页面/请输入股票代码框')
    输入文本(device, '/交易/融资融券/融资买入页面/请输入股票代码框', 证券代码)
    点击(device, '/交易/融资融券/融资买入页面/跌停价')
    # 点击('/交易/融资融券/融资买入页面/输入买入数量')
    # 输入文本('/交易/融资融券/融资买入页面/输入买入数量', 交易数量)
    点击(device, '/交易/融资融券/增加数量')
    点击(device, '/交易/融资融券/融资买入页面/买入按钮')
    点击(device, '/交易/融资融券/融资买入页面/委托买入确定按钮')
    实际结果 = 获取文本信息(device, '/交易/融资融券/融资买入页面/委托提示')
    错误校验点 = 非空校验(device, 预期结果, 实际结果)
    结果 = 预期结果 in 实际结果
    点击(device, '/交易/融资融券/融资买入页面/系统提示确定按钮')
    print(结果)
    return 结果, 错误校验点


