from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('行情/市场/全球/全球重要指数/其他市场/南非')
def _10e948190be98577f30f19499a0630f2(device):
    登录前页面处理(device)
    点击(device, '/底部导航栏/行情界面跳转按钮')
    目录校验(device, '一', '行情', '/行情/选中状态')
    点击(device, '/行情/市场/市场标题栏')
    控件左滑(device, '/行情/市场/沪深按钮标签')
    点击(device, '/行情/市场/全球/全球标签')
    目录校验(device, '二', '全球', '/行情/更多/选中状态')
    点击(device, '/行情/市场/全球/全球重要指数/更多')
    sleep(2)
    滑动控件至屏幕内(device, '/行情/市场/全球/全球重要指数/更多/其他市场/南非')
    闭市 = 获取文本信息(device, '/行情/市场/全球/全球重要指数/更多/其他市场/南非/是否闭市')
    点击(device, '/行情/市场/全球/全球重要指数/更多/其他市场/南非')
    sleep(5)
    实际结果 = 获取文本信息(device, '/行情/市场/股指/股指期货/成分品种校验/价格')
    错误校验点 = 为空校验(device)
    # 结果 = 实际结果 != '' and 实际结果 != '--'
    结果 = 实际结果 != '' and (实际结果 != '--' or 闭市 == '闭市')
    return 结果, 错误校验点


@casescript('行情/市场/全球/全球重要指数/其他市场/富时地中海100')
def _fc5e5c4d859f59c358324774b0170aa9(device):
    登录前页面处理(device)
    点击(device, '/底部导航栏/行情界面跳转按钮')
    目录校验(device, '一', '行情', '/行情/选中状态')
    点击(device, '/行情/市场/市场标题栏')
    控件左滑(device, '/行情/市场/沪深按钮标签')
    点击(device, '/行情/市场/全球/全球标签')
    目录校验(device, '二', '全球', '/行情/更多/选中状态')
    点击(device, '/行情/市场/全球/全球重要指数/更多')
    sleep(2)
    滑动控件至屏幕内(device, '/行情/市场/全球/全球重要指数/更多/其他市场/富时地中海100')
    闭市 = 获取文本信息(device, '/行情/市场/全球/全球重要指数/更多/其他市场/富时地中海100/是否闭市')
    点击(device, '/行情/市场/全球/全球重要指数/更多/其他市场/富时地中海100')
    sleep(5)
    实际结果 = 获取文本信息(device, '/行情/市场/股指/股指期货/成分品种校验/价格')
    错误校验点 = 为空校验(device)
    # 结果 = 实际结果 != '' and 实际结果 != '--'
    结果 = 实际结果 != '' and (实际结果 != '--' or 闭市 == '闭市')
    return 结果, 错误校验点


