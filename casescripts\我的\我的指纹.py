from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('我的/我的指纹/我的指纹')
def _844a8a4de02369ec8137e2c55e340676(device, 账号='666630296771', 交易密码='124369', 通信密码='159753'):
    登录前页面处理(device)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    点击(device, '/我的/我的指纹')
    实际结果 = str(device['/页面标题'])
    错误校验点 = 为空校验(device)
    结果 = 实际结果 != ''
    return 结果, 错误校验点


