from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('我的/极速开户/开户首页/开户首页')
def _8c5d112f22d31146e337c2640368b1aa(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # 初始化
    登录前页面处理(device)
    # 理财登陆(账号, 交易密码, 通信密码)
    # 交易登录(账号, 交易密码, 通信密码)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    目录校验(device, '一', '我的', '/我的/选中状态')
    点击(device, '/我的/极速开户')
    目录校验(device, '二', '开户首页', '/页面标题')
    实际结果 = 获取控件对象(device, '/我的/极速开户/开户首页')
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


