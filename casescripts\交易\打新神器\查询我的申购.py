from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('交易/打新神器/查询我的申购/查询历史')
def _5a493240e17e76eadc5d84b1ae139def(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # hxf 生产账户
    # 初始化
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/打新神器')
    目录校验(device, '二', '打新神器', '/页面标题')
    sleep(2)
    点击(device, '/交易/普通交易/打新神器/打新神器页面/查询我的申购')
    sleep(5)
    点击(device, '/交易/普通交易/打新神器/我的申购页面/查询历史')
    sleep(2)
    try:
        点击(device, '/交易/普通交易/打新神器/我的申购页面/我的历史申购/查询')
    except:
        返回(device)
        sleep(3)
        点击(device, '/交易/普通交易/打新神器/我的申购页面/查询历史')
        sleep(3)
        点击(device, '/交易/普通交易/打新神器/我的申购页面/我的历史申购/查询')
    try:
        实际结果 = 获取文本信息(device, '/交易/普通交易/打新神器/申购/我的历史申购/第一条记录')
    except:
        实际结果 = 获取控件对象(device, '/交易/普通交易/打新神器/申购/我的历史申购/无记录')
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


