from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('行情/市场/股指/国内指数/沪深300/榜单/沪深300涨幅榜首只股票涨幅')
def _38d89581848bb1f6fa21ace8ba484421(device):
    登录前页面处理(device)
    print(device.appium.driver)
    点击(device, '/底部导航栏/行情界面跳转按钮')
    点击(device, '/行情/市场/市场标题栏')
    点击(device, '/行情/市场/股指/股指标签')
    点击(device, '/行情/市场/股指/国内指数/指数成分列表/沪深300')
    点击(device, '/股指/国内指数/沪深300/榜单/榜单标签')
    上涨家数 = 获取文本信息(device, '/股指/国内指数/沪深300/榜单/涨幅榜首只股票涨幅')
    结果 = 上涨家数 != '' or 上涨家数 != None
    错误校验点 = '上涨家数数据丢失'
    return 结果, 错误校验点


