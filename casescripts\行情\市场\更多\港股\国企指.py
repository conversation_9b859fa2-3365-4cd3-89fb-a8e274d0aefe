from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('行情/市场/更多/港股/国企指/获取首只国企指股票名称')
def _a2c68d96ee606fba25247424dd3d91ac(device):
    登录前页面处理(device)
    print(device.appium.driver)
    点击(device, '/底部导航栏/行情界面跳转按钮')
    点击(device, '/行情/市场/市场标题栏')
    控件左滑(device, '/行情/市场/沪深按钮标签')
    点击(device, '/行情/市场/更多/更多标签')
    滑动控件至屏幕内(device, '/行情/市场/更多/港股/国企指/国企指标签')
    点击(device, '/行情/市场/更多/港股/国企指/国企指标签')
    首只国企指股票名称 = 获取文本信息(device, '/行情/市场/更多/港股/国企指/首只国企指股票名称')
    结果 = 首只国企指股票名称 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


