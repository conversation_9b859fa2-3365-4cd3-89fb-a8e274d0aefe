from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('我的/我的业务/账户管理/三方存管新建/进入三方存管新建')
def _17d35291d70bfeb88be6530e36b21ebe(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # 初始化
    登录前页面处理(device)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    上滑(device)
    点击(device, '/底部导航栏/理财界面跳转按钮')
    点击(device, '/底部导航栏/我的界面跳转按钮')
    目录校验(device, '一', '我的', '/我的/选中状态')
    点击(device, '/我的/我的业务')
    目录校验(device, '二', '办业务', '/我的/我的业务/页面标题')
    # 点击('/底部导航栏/理财界面跳转按钮')
    # 点击('/底部导航栏/我的界面跳转按钮')
    上滑(device, 3000)
    点击(device, "/我的/我的业务/问一问")
    返回(device)
    sleep(2)
    点击(device, "/我的/我的业务/账户管理/三方存管新建")
    登录(device, 账号, 交易密码, 通信密码)
    source = device.appium.driver.page_source
    print("source::", source)
    sleep(3)
    if "业务受理时间：交易日09:00-15:30" in source:
        结果 = True
    else:
        结果 = False
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


