from datetime import datetime, timedelta
from random import random


class Caches(object):

    def __init__(self, expire=300):
        self.expire = expire
        self._CACHED = {}

    def get(self, name, default=None):
        _c = self._CACHED.get(name)
        if _c is None:
            return default
        if _c[1] < datetime.now():
            del self._CACHED[name]
            return default
        return _c[0]

    def set(self, name, value, expire=None):
        if expire is None:
            expire = self.expire
        if expire <= 0:
            return
        _t = datetime.now() + timedelta(seconds=expire)
        self._CACHED[name] = (value, _t)
        if random() < 0.05:
            self.remove_expire()

    def flush(self):
        self._CACHED = dict()

    def remove(self, name):
        _c = self._CACHED.get(name)
        if _c is None:
            return False
        del self._CACHED[name]
        return True

    def remove_expire(self):
        t = datetime.now()
        for k in list(self._CACHED.keys()):
            _, _t = self._CACHED[k]
            if _t < t:
                del self._CACHED[k]


caches = Caches()
