from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('首页/热点/我的资产/业务办理_特色业务办理_天天发_进入天天发')
def _0b6dadb209f240be436312134a68cfdd(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119', 预期开通结果='成功', 预期加载结果='我的天天发'):
    # 真实账号
    登录前页面处理(device)
    点击(device, '/底部导航栏/首页界面跳转按钮')
    目录校验(device, '一', '首页', '/首页/选中状态')
    点击(device, '/首页/热点')
    目录校验(device, '二', '热点', '/首页/热点/选中状态')
    点击(device, '/首页/热点/我的资产')
    登录(device, 账号, 交易密码, 通信密码)
    sleep(2)
    # 系统提示('/后台清算系统提示/系统提示', '/后台清算系统提示/确定')
    循环滑动(device, '/首页/热点/我的资产/业务办理', '上')
    点击(device, '/首页/热点/我的资产/业务办理')
    点击(device, '/首页/热点/我的资产/业务办理/天天发')
    开通结果 = 获取文本信息(device, '/首页/热点/我的资产/业务办理/天天发/开通结果')
    print('实际结果', 开通结果)
    点击(device, '/首页/热点/我的资产/业务办理/天天发/进入天天发')
    加载结果 = 获取文本信息(device, '/交易/普通交易/天天发/加载检查')
    print('加载结果：', 加载结果)
    结果 = (预期开通结果 in 开通结果) and (预期加载结果 in 加载结果)
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


@casescript('首页/热点/我的资产/业务办理_特色业务办理_柜台市场产品')
def _9c1af9450cc97b3c07953ea967643153(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119', 预期结果='已开通'):
    # 真实账号
    # # hxf 生产账户
    # 账号 =  '666621794831'
    # 交易密码 =  '110119'
    # 通信密码 =  '110119'
    # sleep(20)
    登录前页面处理(device)
    点击(device, '/底部导航栏/首页界面跳转按钮')
    目录校验(device, '一', '首页', '/首页/选中状态')
    点击(device, '/首页/热点')
    目录校验(device, '二', '热点', '/首页/热点/选中状态')
    点击(device, '/首页/热点/我的资产')
    登录(device, 账号, 交易密码, 通信密码)
    sleep(2)
    系统提示(device, '/后台清算系统提示/系统提示', '/后台清算系统提示/确定')
    循环滑动(device, '/首页/热点/我的资产/业务办理', '上')
    点击(device, '/首页/热点/我的资产/业务办理')
    点击(device, '/首页/热点/我的资产/业务办理/柜台市场产品')
    是否开通 = 获取文本信息(device, '/首页/热点/我的资产/业务办理/柜台市场产品/是否开通')
    print('是否开通', 是否开通)
    结果 = 预期结果 in 是否开通
    if 结果:
        开通结果 = 是否开通
        点击(device, '/首页/热点/我的资产/业务办理/柜台市场产品/确定')
    else:
        点击(device, '/首页/热点/我的资产/业务办理/柜台市场产品/一键开通')
        点击(device, '/首页/热点/我的资产/业务办理/柜台市场产品/一键开通/适当性确认')
        点击(device, '/交易/普通交易/公用页面/适当性确认书页面/确认按钮')
        点击(device, '/首页/热点/我的资产/业务办理/柜台市场产品/一键开通/合同签署/逐条阅读')
        sleep(1)
        点击(device, '/首页/热点/我的资产/业务办理/柜台市场产品/一键开通/合同签署/下一份')
        sleep(1)
        点击(device, '/首页/热点/我的资产/业务办理/柜台市场产品/一键开通/合同签署/下一份')
        sleep(1)
        点击(device, '/首页/热点/我的资产/业务办理/柜台市场产品/一键开通/合同签署/下一份')
        点击(device, '/交易/普通交易/公用页面/协议告知及合同签署页面/复选框')
        点击(device, '/交易/普通交易/公用页面/协议告知及合同签署页面/确定按钮')
        点击(device, '/交易/普通交易/公用页面/协议告知及合同签署页面/下一步按钮')
        点击(device, '/首页/热点/我的资产/业务办理/柜台市场产品/一键开通/问卷调查/第一题')
        点击(device, '/首页/热点/我的资产/业务办理/柜台市场产品/一键开通/问卷调查/第二题')
        上滑(device)
        点击(device, '/首页/热点/我的资产/业务办理/柜台市场产品/一键开通/问卷调查/第三题')
        点击(device, '/首页/热点/我的资产/业务办理/柜台市场产品/一键开通/问卷调查/第四题')
        上滑(device)
        点击(device, '/首页/热点/我的资产/业务办理/柜台市场产品/一键开通/问卷调查/第五题')
        # 提交按钮点击无效
        点击(device, '/首页/热点/我的资产/业务办理/柜台市场产品/一键开通/问卷调查/提交')
        # sleep(10)
        开通结果 = 获取文本信息(device, '/首页/热点/我的资产/业务办理/柜台市场产品/一键开通/提示信息/结果')
        结果 = 预期结果 in 开通结果
        点击(device, '/首页/热点/我的资产/业务办理/柜台市场产品/一键开通/提示信息/确定')
    错误校验点 = 非空校验(device, 预期结果, 开通结果)
    return 结果, 错误校验点


@casescript('首页/热点/我的资产/业务办理_特色业务办理_理财快捷购买')
def _8275b2f16640f4a63f6c717477e6e3f8(device, 账号='666630296771', 交易密码='124369', 通信密码='159753'):
    # hxf 生产账户
    登录前页面处理(device)
    点击(device, '/底部导航栏/首页界面跳转按钮')
    目录校验(device, '一', '首页', '/首页/选中状态')
    点击(device, '/首页/热点')
    目录校验(device, '二', '热点', '/首页/热点/选中状态')
    点击(device, '/首页/热点/我的资产')
    登录(device, 账号, 交易密码, 通信密码)
    sleep(2)
    循环滑动(device, '/首页/热点/我的资产/业务办理', '上')
    上滑(device)
    点击(device, '/首页/热点/我的资产/业务办理')
    上滑(device)
    点击(device, '/首页/热点/我的资产/业务办理/理财快捷购买')
    关闭 = 获取控件对象(device, '/首页/热点/我的资产/业务办理/理财快速购买/关闭快捷购买')
    结果 = False
    if 关闭 != '':
        点击(device, '/首页/热点/我的资产/业务办理/理财快速购买/关闭快捷购买')
        点击(device, '/首页/热点/我的资产/业务办理/理财快速购买/关闭快捷购买/确认关闭')
        sleep(2)
        结果 = 获取控件对象(device, '/首页/热点/我的资产/业务办理/理财快速购买/立即开通') != ''
    else:
        点击(device, '/首页/热点/我的资产/业务办理/理财快速购买/勾选框')
        点击(device, '/首页/热点/我的资产/业务办理/理财快速购买/立即开通')
        sleep(2)
        结果 = 获取控件对象(device, '/首页/热点/我的资产/业务办理/理财快速购买/关闭快捷购买') != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


@casescript('首页/热点/我的资产/业务办理_特色业务办理_网上开户')
def _dc8d4416ebfcde66ae85d2344952c0b0(device, 账号='666630296771', 交易密码='124369', 通信密码='159753'):
    # hxf 生产账户
    登录前页面处理(device)
    点击(device, '/底部导航栏/首页界面跳转按钮')
    目录校验(device, '一', '首页', '/首页/选中状态')
    点击(device, '/首页/热点')
    目录校验(device, '二', '热点', '/首页/热点/选中状态')
    点击(device, '/首页/热点/我的资产')
    登录(device, 账号, 交易密码, 通信密码)
    sleep(2)
    循环滑动(device, '/首页/热点/我的资产/业务办理', '上')
    上滑(device)
    点击(device, '/首页/热点/我的资产/业务办理')
    # 上滑()
    点击(device, '/首页/热点/我的资产/业务办理/网上开户')
    点击(device, '/首页/热点/开户/立即开户')
    实际结果 = 获取文本信息(device, '/页面标题')
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


@casescript('首页/热点/我的资产/业务办理_通用业务办理_开通相关交易权限')
def _d5a99ef1beb5a2587fe1269c632d12c9(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # # hxf 生产账户
    # 账号 =  '666621794831'
    # 交易密码 =  '110119'
    # 通信密码 =  '110119'
    # fsc
    # sleep(20)
    登录前页面处理(device)
    点击(device, '/底部导航栏/首页界面跳转按钮')
    目录校验(device, '一', '首页', '/首页/选中状态')
    点击(device, '/首页/热点')
    目录校验(device, '二', '热点', '/首页/热点/选中状态')
    点击(device, '/首页/热点/我的资产')
    登录(device, 账号, 交易密码, 通信密码)
    sleep(2)
    系统提示(device, '/后台清算系统提示/系统提示', '/后台清算系统提示/确定')
    循环滑动(device, '/首页/热点/我的资产/业务办理', '上')
    点击(device, '/首页/热点/我的资产/业务办理')
    上滑(device)
    点击(device, '/首页/热点/我的资产/业务办理/开通相关交易权限')
    是否签署 = 获取文本信息(device, '/首页/热点/我的资产/业务办理/开通相关交易权限/上交所风险警示股票交易')
    # 结果 = False
    实际开通结果 = ''
    if '未签署' in 是否签署:
        点击(device, '/首页/热点/我的资产/业务办理/开通相关交易权限/上交所风险警示股票交易')
        点击(device, '/首页/热点/我的资产/业务办理/开通相关交易权限/上交所风险警示股票交易/我要开通')
        点击(device, '/交易/普通交易/公用页面/适当性确认书页面/我已悉知按钮')
        点击(device, '/交易/普通交易/公用页面/适当性确认书页面/确认按钮')
        点击(device, '/交易/普通交易/公用页面/协议告知及合同签署页面/确定按钮')  # 点击将逐条阅读上述文本
        sleep(1)
        点击(device, '/首页/热点/我的资产/业务办理/柜台市场产品/一键开通/合同签署/下一份')
        sleep(3)
        点击(device, '/首页/热点/我的资产/业务办理/柜台市场产品/一键开通/合同签署/下一份')  # 阅读完成
        点击(device, '/交易/普通交易/公用页面/协议告知及合同签署页面/复选框')
        点击(device, '/交易/普通交易/公用页面/协议告知及合同签署页面/确定按钮')
        实际开通结果 = 获取文本信息(device, '/首页/热点/我的资产/业务办理/开通相关交易权限/上交所风险警示股票交易/我要开通/开通结果')
        预期结果 = '成功'
        结果 = 预期结果 in 实际开通结果
        点击(device, '/底部确定/确定按钮')
    else:
        print('交易权限已经开通！')
        预期结果 = '已签署'
        结果 = 预期结果 in 是否签署
        # 结果 = '该交易权限已经开通！！'
    错误校验点 = 非空校验(device, 预期结果, 实际开通结果)
    return 结果, 错误校验点


@casescript('首页/热点/我的资产/业务开通_特色业务办理_开通股东账户')
def _bf36d140fa3b91dcd0a9a65bdd15e8ff(device, 账号='666630296771', 交易密码='124369', 通信密码='159753'):
    错误校验点 = ''
    # hxf 生产账户
    登录前页面处理(device)
    点击(device, '/底部导航栏/首页界面跳转按钮')
    目录校验(device, '一', '首页', '/首页/选中状态')
    点击(device, '/首页/热点')
    目录校验(device, '二', '热点', '/首页/热点/选中状态')
    点击(device, '/首页/热点/我的资产')
    登录(device, 账号, 交易密码, 通信密码)
    sleep(2)
    循环滑动(device, '/首页/热点/我的资产/业务办理', '上')
    上滑(device)
    点击(device, '/首页/热点/我的资产/业务办理')
    # 上滑()
    点击(device, '/首页/热点/我的资产/业务办理/开通股东账户')
    if 获取控件对象(device, '/页面/弹出框内容') != '':
        错误校验点 = 获取文本信息(device, '/页面/弹出框内容')
        点击(device, '/风险提示/知道了')
    点击(device, '/首页/热点/我的资产/业务办理/开通相关交易权限/上交所风险警示股票交易/我要开通')
    # 添加验收手机号界面
    实际结果 = 获取文本信息(device, '/页面标题')
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


@casescript('首页/热点/我的资产/业务开通_特色业务办理_港股通')
def _28099e9f8702567b8bf52c1f0b40c4c6(device, 账号='666630296771', 交易密码='124369', 通信密码='159753'):
    # hxf 生产账户
    登录前页面处理(device)
    点击(device, '/底部导航栏/首页界面跳转按钮')
    目录校验(device, '一', '首页', '/首页/选中状态')
    点击(device, '/首页/热点')
    目录校验(device, '二', '热点', '/首页/热点/选中状态')
    点击(device, '/首页/热点/我的资产')
    登录(device, 账号, 交易密码, 通信密码)
    sleep(2)
    循环滑动(device, '/首页/热点/我的资产/业务办理', '上')
    上滑(device)
    点击(device, '/首页/热点/我的资产/业务办理')
    # 上滑()
    点击(device, '/首页/热点/我的资产/业务办理/港股通')
    # 点击('/首页/热点/开户/立即开户')
    实际结果 = 获取文本信息(device, '/页面标题')
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


@casescript('首页/热点/我的资产/业务开通_特色业务办理_科创板权限开通')
def _4be7981fa0b52fd6b6f6c08a74ab35aa(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # 真实账号
    # # hxf 生产账户
    # 账号 =  '666621794831'
    # 交易密码 =  '110119'
    # 通信密码 =  '110119'
    # sleep(20)
    登录前页面处理(device)
    点击(device, '/底部导航栏/首页界面跳转按钮')
    目录校验(device, '一', '首页', '/首页/选中状态')
    点击(device, '/首页/热点')
    目录校验(device, '二', '热点', '/首页/热点/选中状态')
    点击(device, '/首页/热点/我的资产')
    登录(device, 账号, 交易密码, 通信密码)
    sleep(2)
    系统提示(device, '/后台清算系统提示/系统提示', '/后台清算系统提示/确定')
    循环滑动(device, '/首页/热点/我的资产/业务办理', '上')
    点击(device, '/首页/热点/我的资产/业务办理')
    点击(device, '/首页/热点/我的资产/业务办理/科创板权限开通')
    实际结果 = 获取文本信息(device, '/页面标题')
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


@casescript('首页/热点/我的资产/业务开通_特色业务办理_证券账户下挂')
def _0d6bb9b563f61b6d7cbf6265d75d6285(device, 账号='666630296771', 交易密码='124369', 通信密码='159753'):
    # hxf 生产账户
    登录前页面处理(device)
    点击(device, '/底部导航栏/首页界面跳转按钮')
    目录校验(device, '一', '首页', '/首页/选中状态')
    点击(device, '/首页/热点')
    目录校验(device, '二', '热点', '/首页/热点/选中状态')
    点击(device, '/首页/热点/我的资产')
    登录(device, 账号, 交易密码, 通信密码)
    sleep(2)
    循环滑动(device, '/首页/热点/我的资产/业务办理', '上')
    上滑(device)
    点击(device, '/首页/热点/我的资产/业务办理')
    # 上滑()
    点击(device, '/首页/热点/我的资产/业务办理/证券账户下挂')
    # 点击('/首页/热点/开户/立即开户')
    实际结果 = 获取文本信息(device, '/页面标题')
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


@casescript('首页/热点/我的资产/业务开通_通用业务办理_三方存管新建')
def _4738e685bd0307f2600a1f8a91f0fe12(device, 账号='666630296771', 交易密码='124369', 通信密码='159753'):
    # hxf 生产账户
    登录前页面处理(device)
    点击(device, '/底部导航栏/首页界面跳转按钮')
    目录校验(device, '一', '首页', '/首页/选中状态')
    点击(device, '/首页/热点')
    目录校验(device, '二', '热点', '/首页/热点/选中状态')
    点击(device, '/首页/热点/我的资产')
    登录(device, 账号, 交易密码, 通信密码)
    sleep(2)
    循环滑动(device, '/首页/热点/我的资产/业务办理', '上')
    上滑(device)
    点击(device, '/首页/热点/我的资产/业务办理')
    上滑(device)
    上滑(device)
    点击(device, '/首页/热点/我的资产/业务办理/三方存管新建')
    返回(device)
    点击(device, '/首页/热点/我的资产/业务办理/三方存管新建')
    # 点击('/首页/热点/开户/立即开户')
    实际结果 = 获取文本信息(device, '/页面标题')
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


@casescript('首页/热点/我的资产/业务开通_通用业务办理_专业投资者的认定与取消')
def _a4d61f0dd47b508d9a3916c4d3e71a48(device, 账号='666630296771', 交易密码='124369', 通信密码='159753'):
    # hxf 生产账户
    登录前页面处理(device)
    点击(device, '/底部导航栏/首页界面跳转按钮')
    目录校验(device, '一', '首页', '/首页/选中状态')
    点击(device, '/首页/热点')
    目录校验(device, '二', '热点', '/首页/热点/选中状态')
    点击(device, '/首页/热点/我的资产')
    登录(device, 账号, 交易密码, 通信密码)
    sleep(2)
    循环滑动(device, '/首页/热点/我的资产/业务办理', '上')
    上滑(device)
    点击(device, '/首页/热点/我的资产/业务办理')
    上滑(device)
    上滑(device)
    点击(device, '/首页/热点/我的资产/业务办理/专业投资者的认定与取消')
    返回(device)
    点击(device, '/首页/热点/我的资产/业务办理/专业投资者的认定与取消')
    # 点击('/首页/热点/开户/立即开户')
    实际结果 = 获取文本信息(device, '/页面标题')
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


@casescript('首页/热点/我的资产/业务开通_通用业务办理_创业板转签_我要办理')
def _f29bd475954c4e35b2c5c4f1c40620af(device, 账号='666630296771', 交易密码='124369', 通信密码='159753'):
    # hxf 生产账户
    登录前页面处理(device)
    点击(device, '/底部导航栏/首页界面跳转按钮')
    目录校验(device, '一', '首页', '/首页/选中状态')
    点击(device, '/首页/热点')
    目录校验(device, '二', '热点', '/首页/热点/选中状态')
    点击(device, '/首页/热点/我的资产')
    登录(device, 账号, 交易密码, 通信密码)
    sleep(2)
    循环滑动(device, '/首页/热点/我的资产/业务办理', '上')
    上滑(device)
    点击(device, '/首页/热点/我的资产/业务办理')
    上滑(device)
    上滑(device)
    点击(device, '/首页/热点/我的资产/业务办理/创业板转签')
    返回(device)
    点击(device, '/首页/热点/我的资产/业务办理/创业板转签')
    # 点击('/首页/热点/开户/立即开户')
    实际结果 = 获取文本信息(device, '/页面标题')
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


@casescript('首页/热点/我的资产/业务开通_通用业务办理_新增辅助资金账户')
def _ef5be5370bb69006fc83b064495ea223(device, 账号='666630296771', 交易密码='124369', 通信密码='159753'):
    # hxf 生产账户
    登录前页面处理(device)
    点击(device, '/底部导航栏/首页界面跳转按钮')
    目录校验(device, '一', '首页', '/首页/选中状态')
    点击(device, '/首页/热点')
    目录校验(device, '二', '热点', '/首页/热点/选中状态')
    点击(device, '/首页/热点/我的资产')
    登录(device, 账号, 交易密码, 通信密码)
    sleep(2)
    循环滑动(device, '/首页/热点/我的资产/业务办理', '上')
    上滑(device)
    点击(device, '/首页/热点/我的资产/业务办理')
    上滑(device)
    上滑(device)
    点击(device, '/首页/热点/我的资产/业务办理/新增辅助资金账户')
    返回(device)
    点击(device, '/首页/热点/我的资产/业务办理/新增辅助资金账户')
    # 点击('/首页/热点/开户/立即开户')
    实际结果 = 获取文本信息(device, '/页面标题')
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


@casescript('首页/热点/我的资产/业务开通_通用业务办理_身份证信息更新')
def _d23c6a5c1379826fb32584751c8c6dd5(device, 账号='666630296771', 交易密码='124369', 通信密码='159753'):
    # hxf 生产账户
    登录前页面处理(device)
    点击(device, '/底部导航栏/首页界面跳转按钮')
    目录校验(device, '一', '首页', '/首页/选中状态')
    点击(device, '/首页/热点')
    目录校验(device, '二', '热点', '/首页/热点/选中状态')
    点击(device, '/首页/热点/我的资产')
    登录(device, 账号, 交易密码, 通信密码)
    sleep(2)
    循环滑动(device, '/首页/热点/我的资产/业务办理', '上')
    上滑(device)
    点击(device, '/首页/热点/我的资产/业务办理')
    上滑(device)
    上滑(device)
    点击(device, '/首页/热点/我的资产/业务办理/身份证信息更新')
    返回(device)
    点击(device, '/首页/热点/我的资产/业务办理/身份证信息更新')
    # 点击('/首页/热点/开户/立即开户')
    实际结果 = 获取文本信息(device, '/页面标题')
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


@casescript('首页/热点/我的资产/个人信息_个人信息更新')
def _fe39ad657998b0fa9f8566fd8849bb4d(device, 账号='666630296771', 交易密码='124369', 通信密码='159753', 居住地址='kkkkkkkkk', 手机号码='17749532309'):
    错误校验点 = ''
    # hxf 生产账户
    # 初始化
    登录前页面处理(device)
    点击(device, '/底部导航栏/首页界面跳转按钮')
    目录校验(device, '一', '首页', '/首页/选中状态')
    点击(device, '/首页/热点')
    目录校验(device, '二', '热点', '/首页/热点/选中状态')
    点击(device, '/首页/热点/我的资产')
    登录(device, 账号, 交易密码, 通信密码)
    循环滑动(device, '/首页/热点/我的资产/个人信息', '上')
    点击(device, '/首页/热点/我的资产/个人信息')
    # 循环滑动('/首页/热点/我的资产/个人信息/个人信息更新', '上')
    上滑(device)
    上滑(device)
    上滑(device)
    sleep(2)
    点击(device, '/我的/极速开户/在线客服')
    返回(device)
    点击(device, '/首页/热点/我的资产/个人信息/个人信息更新')
    输入文本(device, '/首页/热点/我的资产/个人信息/个人信息更新/居住地址', 居住地址)
    点击(device, '/首页/热点/我的资产/个人信息/个人信息更新/下一步')
    try:
        获取文本信息(device, '/首页/热点/我的资产/个人信息/个人信息更新/系统提示/加载检查')
    except:
        print('无系统提示')
        pass
    else:
        错误校验点 = 获取文本信息(device, '/首页/热点/我的资产/个人信息/个人信息更新/系统提示/提示信息')
        return False, 错误校验点
        raise NotFindError
    try:
        获取文本信息(device, '/公用/温馨提示/加载检查')
    except:
        print('无温馨提示！')
        pass
    else:
        点击(device, '/公用/温馨提示/我已确认')
        点击(device, '/首页/热点/我的资产/个人信息/个人信息更新/下一步')
    结果 = 获取控件对象(device, '/首页/热点/我的资产/个人信息/个人信息更新/验证/获取验证码') != ''
    错误校验点 = 为空校验(device)
    # 输入文本('/首页/热点/我的资产/个人信息/个人信息更新/验证/手机号码', 手机号码)
    # 点击('/首页/热点/我的资产/个人信息/个人信息更新/验证/获取验证码')
    #
    # time.sleep(5)
    # 验证码 = 获取短信验证码()
    # print('验证码：；', 验证码)
    # 输入文本('/首页/热点/我的资产/个人信息/个人信息更新/验证/输入验证码', 验证码)
    # 点击('/首页/热点/我的资产/个人信息/个人信息更新/验证/确认修改')
    # 预期结果 = '修改成功'
    # 实际结果 = 获取文本信息('/首页/热点/我的资产/个人信息/个人信息更新/结果/修改结果')
    # 点击('/首页/热点/我的资产/个人信息/个人信息更新/结果/确定')
    # 结果 = 预期结果 in 实际结果
    # 全局变量.错误校验点 = 非空校验(预期结果, 实际结果)
    return 结果, 错误校验点


@casescript('首页/热点/我的资产/个人信息_手机号码修改')
def _87737f621d61650cdc8b62ee29365b17(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119', 预期结果='手机号码修改'):
    # 业务交易数据 = 获取业务交易信息('交易/普通交易/场内基金/场内申购')
    # 证券代码 = 业务交易数据['证券代码']
    # 初始化
    登录前页面处理(device)
    sleep(15)
    # 交易登录(账号, 交易密码, 通信密码 )
    # ---------------------------------------------------------#
    点击(device, '/底部导航栏/首页界面跳转按钮')
    目录校验(device, '一', '首页', '/首页/选中状态')
    点击(device, '/首页/热点')
    目录校验(device, '二', '热点', '/首页/热点/选中状态')
    点击(device, '/首页/热点/我的资产')
    登录(device, 账号, 交易密码, 通信密码)
    # time.sleep(2)
    # 上滑()
    # time.sleep(2)
    # 上滑()
    # time.sleep(2)
    滑动控件至屏幕内(device, '/首页/热点/我的资产/个人信息')
    点击(device, '/首页/热点/我的资产/个人信息')
    # time.sleep(2)
    上滑(device)
    # time.sleep(2)
    上滑(device)
    上滑(device)
    # time.sleep(2)
    # 点击('/首页/热点/我的资产/个人信息/账户信息')
    点击(device, '/我的/极速开户/在线客服')
    返回(device)
    点击(device, '/首页/热点/我的资产/个人信息/手机号码修改')
    实际结果 = 获取文本信息(device, '/首页/热点/我的资产/个人信息/提示信息')
    结果 = 预期结果 in 实际结果
    错误校验点 = 非空校验(device, 预期结果, 实际结果)
    return 结果, 错误校验点


@casescript('首页/热点/我的资产/个人信息_手机号码重置')
def _160c23a408eb6b47b5841a638593b24c(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # 业务交易数据 = 获取业务交易信息('交易/普通交易/场内基金/场内申购')
    # 证券代码 = 业务交易数据['证券代码']
    # 初始化
    登录前页面处理(device)
    sleep(15)
    # 交易登录(账号, 交易密码, 通信密码 )
    # ---------------------------------------------------------#
    点击(device, '/底部导航栏/首页界面跳转按钮')
    目录校验(device, '一', '首页', '/首页/选中状态')
    点击(device, '/首页/热点')
    目录校验(device, '二', '热点', '/首页/热点/选中状态')
    点击(device, '/首页/热点/我的资产')
    登录(device, 账号, 交易密码, 通信密码)
    # time.sleep(2)
    # 上滑()
    # time.sleep(2)
    # 上滑()
    # time.sleep(2)
    滑动控件至屏幕内(device, '/首页/热点/我的资产/个人信息')
    点击(device, '/首页/热点/我的资产/个人信息')
    # time.sleep(2)
    上滑(device)
    # time.sleep(2)
    上滑(device)
    上滑(device)
    # time.sleep(2)
    # 点击('/首页/热点/我的资产/个人信息/账户信息')
    点击(device, '/我的/极速开户/在线客服')
    返回(device)
    点击(device, '/首页/热点/我的资产/个人信息/手机号码重置')
    实际结果 = 获取文本信息(device, '/首页/热点/我的资产/个人信息/提示信息')
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


@casescript('首页/热点/我的资产/个人信息_账户信息')
def _fcabc461dc414bfbe497eaab05a8d03a(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # 业务交易数据 = 获取业务交易信息('交易/普通交易/场内基金/场内申购')
    # 证券代码 = 业务交易数据['证券代码']
    # 初始化
    登录前页面处理(device)
    点击(device, '/底部导航栏/首页界面跳转按钮')
    目录校验(device, '一', '首页', '/首页/选中状态')
    点击(device, '/首页/热点')
    目录校验(device, '二', '热点', '/首页/热点/选中状态')
    点击(device, '/首页/热点/我的资产')
    登录(device, 账号, 交易密码, 通信密码)
    循环滑动(device, '/首页/热点/我的资产/个人信息', '上')
    点击(device, '/首页/热点/我的资产/个人信息')
    上滑(device)
    上滑(device)
    上滑(device)
    sleep(3)
    点击(device, '/我的/极速开户/在线客服')
    返回(device)
    点击(device, '/首页/热点/我的资产/个人信息/账户信息')
    实际结果 = 获取文本信息(device, '/首页/热点/我的资产/个人信息/账户信息/资金账户')
    结果 = 实际结果 == 账号
    错误校验点 = 非空校验(device, 账号, 实际结果)
    return 结果, 错误校验点


@casescript('首页/热点/我的资产/密码修改_修改交易密码')
def _425c70386a2d666707d216f8d93b2c33(device, 账号='666630297341', 旧交易密码='968892', 通信密码='715394', 新交易密码='142569', 第一次预期结果='成功', 第二次预期结果='成功'):
    # hxf 生产账户
    # 账号 = '666630296771'
    # 旧交易密码 = '124369'
    # 通信密码 = '159753'
    # 新交易密码 = '159753'
    # fsc
    登录前页面处理(device)
    点击(device, '/底部导航栏/首页界面跳转按钮')
    目录校验(device, '一', '首页', '/首页/选中状态')
    点击(device, '/首页/热点')
    目录校验(device, '二', '热点', '/首页/热点/选中状态')
    点击(device, '/首页/热点/我的资产')
    登录(device, 账号, 旧交易密码, 通信密码)
    sleep(2)
    # 系统提示('/后台清算系统提示/系统提示', '/后台清算系统提示/确定')
    循环滑动(device, '/首页/热点/我的资产/密码修改', '上')
    点击(device, '/首页/热点/我的资产/密码修改')
    点击(device, '/首页/热点/我的资产/密码修改/修改交易密码')
    # 点击( '/首页/热点/我的资产/密码修改/修改交易密码/输入当前密码')
    输入文本(device, '/首页/热点/我的资产/密码修改/修改交易密码/输入当前密码', 旧交易密码)
    输入文本(device, '/首页/热点/我的资产/密码修改/修改交易密码/输入新密码', 新交易密码)
    输入文本(device, '/首页/热点/我的资产/密码修改/修改交易密码/再次输入新密码', 新交易密码)
    点击(device, '/首页/热点/我的资产/密码修改/修改交易密码/显示密码字样')
    点击(device, '/首页/热点/我的资产/密码修改/修改交易密码/确认修改')
    点击(device, '/首页/热点/我的资产/密码修改/修改交易密码/修改密码/确定')
    第一次修改结果 = 获取文本信息(device, '/首页/热点/我的资产/密码修改/修改交易密码/系统提示/结果')
    print('第一次修改结果', 第一次修改结果)
    第一次结果 = 第一次预期结果 in 第一次修改结果
    # 全局变量.错误校验点 = 非空校验(第一次预期结果, 第一次修改结果)
    # print('结果', 结果)
    # 结果校验(第一次结果)
    点击(device, '/首页/热点/我的资产/密码修改/修改交易密码/系统提示/确定')
    # 将密码改回来
    输入文本(device, '/首页/热点/我的资产/密码修改/修改交易密码/输入当前密码', 新交易密码)
    输入文本(device, '/首页/热点/我的资产/密码修改/修改交易密码/输入新密码', 旧交易密码)
    输入文本(device, '/首页/热点/我的资产/密码修改/修改交易密码/再次输入新密码', 旧交易密码)
    点击(device, '/首页/热点/我的资产/密码修改/修改交易密码/显示密码字样')
    点击(device, '/首页/热点/我的资产/密码修改/修改交易密码/确认修改')
    点击(device, '/首页/热点/我的资产/密码修改/修改交易密码/修改密码/确定')
    第二次修改结果 = 获取文本信息(device, '/首页/热点/我的资产/密码修改/修改交易密码/系统提示/结果')
    print('第二次修改结果', 第二次修改结果)
    第二次结果 = 第一次预期结果 in 第一次修改结果 and 第二次预期结果 in 第二次修改结果
    # print('结果', 结果)
    结果 = 第一次结果 and 第二次结果
    错误校验点 = 两次校验(device, 第一次预期结果, 第一次修改结果, 第二次预期结果, 第二次修改结果)
    return 结果, 错误校验点
    点击(device, '/首页/热点/我的资产/密码修改/修改交易密码/系统提示/确定')


@casescript('首页/热点/我的资产/密码修改_修改资金密码')
def _2cd4d730056ff771c41ecc35a0038613(device, 账号='666630297341', 交易密码='968892', 通信密码='715394', 旧资金密码='684268', 新资金密码='137950', 第一次预期结果='成功', 第二次预期结果='成功'):
    # hxf 生产账户
    # 账号 = '666630296771'
    # 交易密码 = '124369'
    # 通信密码 = '159753'
    # 旧资金密码 = '124369'
    # 新资金密码 = '159753'
    # fsc
    登录前页面处理(device)
    点击(device, '/底部导航栏/首页界面跳转按钮')
    目录校验(device, '一', '首页', '/首页/选中状态')
    点击(device, '/首页/热点')
    目录校验(device, '二', '热点', '/首页/热点/选中状态')
    点击(device, '/首页/热点/我的资产')
    登录(device, 账号, 交易密码, 通信密码)
    sleep(2)
    # 系统提示('/后台清算系统提示/系统提示', '/后台清算系统提示/确定')
    循环滑动(device, '/首页/热点/我的资产/密码修改', '上')
    点击(device, '/首页/热点/我的资产/密码修改')
    点击(device, '/首页/热点/我的资产/密码修改/修改资金密码')
    # 点击( '/首页/热点/我的资产/密码修改/修改交易密码/输入当前密码')
    输入文本(device, '/首页/热点/我的资产/密码修改/修改交易密码/输入当前密码', 旧资金密码)
    输入文本(device, '/首页/热点/我的资产/密码修改/修改交易密码/输入新密码', 新资金密码)
    输入文本(device, '/首页/热点/我的资产/密码修改/修改交易密码/再次输入新密码', 新资金密码)
    点击(device, '/首页/热点/我的资产/密码修改/修改交易密码/显示密码字样')
    点击(device, '/首页/热点/我的资产/密码修改/修改交易密码/确认修改')
    点击(device, '/首页/热点/我的资产/密码修改/修改交易密码/修改密码/确定')
    第一次实际结果 = 获取文本信息(device, '/首页/热点/我的资产/密码修改/修改交易密码/系统提示/结果')
    # print('实际结果', 第一次实际结果)
    第一次结果 = 第一次预期结果 in 第一次实际结果
    # print('结果', 结果)
    # 全局变量.错误校验点 = 非空校验(第一次预期结果,第一次实际结果)
    # 结果校验(第一次结果)
    点击(device, '/首页/热点/我的资产/密码修改/修改交易密码/系统提示/确定')
    输入文本(device, '/首页/热点/我的资产/密码修改/修改交易密码/输入当前密码', 新资金密码)
    输入文本(device, '/首页/热点/我的资产/密码修改/修改交易密码/输入新密码', 旧资金密码)
    输入文本(device, '/首页/热点/我的资产/密码修改/修改交易密码/再次输入新密码', 旧资金密码)
    点击(device, '/首页/热点/我的资产/密码修改/修改交易密码/显示密码字样')
    点击(device, '/首页/热点/我的资产/密码修改/修改交易密码/确认修改')
    点击(device, '/首页/热点/我的资产/密码修改/修改交易密码/修改密码/确定')
    第二次实际结果 = 获取文本信息(device, '/首页/热点/我的资产/密码修改/修改交易密码/系统提示/结果')
    第二次结果 = 第二次预期结果 in 第二次实际结果
    结果 = 第一次结果 and 第二次结果
    错误校验点 = 两次校验(device, 第一次预期结果, 第一次实际结果, 第二次预期结果, 第二次实际结果)
    return 结果, 错误校验点
    点击(device, '/首页/热点/我的资产/密码修改/修改交易密码/系统提示/确定')


@casescript('首页/热点/我的资产/密码修改_修改通讯密码')
def _e9a60c170b274021e96c7741ef1b0775(device, 账号='666630297341', 交易密码='968892', 旧通信密码='715394', 新通信密码='298869', 第一次预期结果='成功', 第二次预期结果='成功'):
    # hxf 生产账户
    # 账号 = '666630296771'
    # 交易密码 = '124369'
    # 旧通讯密码 = '159753'
    # 新通讯密码 = '124369'
    # fsc
    登录前页面处理(device)
    点击(device, '/底部导航栏/首页界面跳转按钮')
    目录校验(device, '一', '首页', '/首页/选中状态')
    点击(device, '/首页/热点')
    目录校验(device, '二', '热点', '/首页/热点/选中状态')
    点击(device, '/首页/热点/我的资产')
    登录(device, 账号, 交易密码, 旧通信密码)
    循环滑动(device, '/首页/热点/我的资产/密码修改', '上')
    点击(device, '/首页/热点/我的资产/密码修改')
    点击(device, '/首页/热点/我的资产/密码修改/修改通讯密码')
    # 点击( '/首页/热点/我的资产/密码修改/修改交易密码/输入当前密码')
    输入文本(device, '/首页/热点/我的资产/密码修改/修改交易密码/输入当前密码', 旧通信密码)
    输入文本(device, '/首页/热点/我的资产/密码修改/修改交易密码/输入新密码', 新通信密码)
    输入文本(device, '/首页/热点/我的资产/密码修改/修改交易密码/再次输入新密码', 新通信密码)
    点击(device, '/首页/热点/我的资产/密码修改/修改交易密码/显示密码字样')
    点击(device, '/首页/热点/我的资产/密码修改/修改交易密码/确认修改')
    点击(device, '/首页/热点/我的资产/密码修改/修改交易密码/修改密码/确定')
    第一次实际结果 = 获取文本信息(device, '/首页/热点/我的资产/密码修改/修改交易密码/系统提示/结果')
    print('实际结果', 第一次实际结果)
    第一次结果 = 第一次预期结果 in 第一次实际结果
    # print('结果', 第一次结果)
    # 全局变量.错误校验点 = 非空校验(第一次预期结果, 第一次实际结果)
    # 结果校验(第一次结果)
    点击(device, '/首页/热点/我的资产/密码修改/修改交易密码/系统提示/确定')
    # 把密码改回来
    输入文本(device, '/首页/热点/我的资产/密码修改/修改交易密码/输入当前密码', 新通信密码)
    输入文本(device, '/首页/热点/我的资产/密码修改/修改交易密码/输入新密码', 旧通信密码)
    输入文本(device, '/首页/热点/我的资产/密码修改/修改交易密码/再次输入新密码', 旧通信密码)
    点击(device, '/首页/热点/我的资产/密码修改/修改交易密码/显示密码字样')
    点击(device, '/首页/热点/我的资产/密码修改/修改交易密码/确认修改')
    点击(device, '/首页/热点/我的资产/密码修改/修改交易密码/修改密码/确定')
    第二次实际结果 = 获取文本信息(device, '/首页/热点/我的资产/密码修改/修改交易密码/系统提示/结果')
    # print('实际结果', 第二次实际结果)
    第二次结果 = 第二次预期结果 in 第二次实际结果
    # print('结果', 第一次结果)
    结果 = 第一次结果 and 第二次结果
    错误校验点 = 两次校验(device, 第一次预期结果, 第一次实际结果, 第二次预期结果, 第二次实际结果)
    return 结果, 错误校验点
    点击(device, '/首页/热点/我的资产/密码修改/修改交易密码/系统提示/确定')


@casescript('首页/热点/我的资产/密码修改_重置通讯密码')
def _7805926fbbca01897d8785dbf4095046(device, 客户号='666630296771', 交易密码='124369', 通信密码='159753', 身份证号='320802199506012511', 预期结果='获取验证码'):
    # 关闭系统提示(用例名称)
    点击(device, '/底部导航栏/首页界面跳转按钮')
    目录校验(device, '一', '首页', '/首页/选中状态')
    点击(device, '/首页/热点')
    目录校验(device, '二', '热点', '/首页/热点/选中状态')
    点击(device, '/首页/热点/我的资产')
    登录(device, 客户号, 交易密码, 通信密码)
    循环滑动(device, '/首页/热点/我的资产/密码修改', '上')
    点击(device, '/首页/热点/我的资产/密码修改')
    点击(device, '/首页/热点/我的资产/密码修改/重置通讯密码')
    输入文本(device, '/首页/热点/我的资产/密码修改/重置通讯密码/客户号', 客户号)
    输入文本(device, '/首页/热点/我的资产/密码修改/重置通讯密码/身份证号', 身份证号)
    点击(device, '/首页/热点/我的资产/密码修改/重置通讯密码/温馨提示')
    点击(device, '/首页/热点/我的资产/密码修改/重置通讯密码/下一步')
    实际结果 = 获取文本信息(device, '/首页/热点/我的资产/密码修改/重置通讯密码/获取验证码')
    结果 = 预期结果 in 实际结果
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


@casescript('首页/热点/我的资产/我的特权')
def _a7cebed4bc3177762ac9eca31ea3bc46(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    点击(device, '/底部导航栏/首页界面跳转按钮')
    目录校验(device, '一', '首页', '/首页/选中状态')
    点击(device, '/首页/热点')
    目录校验(device, '二', '热点', '/首页/热点/选中状态')
    点击(device, '/首页/热点/我的资产')
    登录(device, 账号, 交易密码, 通信密码)
    循环滑动(device, '/首页/热点/我的资产/我的特权', '上')
    点击(device, '/首页/热点/我的资产/我的特权')
    实际结果 = 获取文本信息(device, '/首页/热点/我的资产/我的特权/特权获得个数')
    # = 获取文本信息( '/首页/热点/我的资产/我的账单/总收益')
    结果 = float(实际结果) != 0
    错误校验点 = 数字校验(device, 实际结果)
    return 结果, 错误校验点


@casescript('首页/热点/我的资产/我的订阅_在线客服')
def _a52a472429acd9a304ae1f4feb99b607(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    点击(device, '/底部导航栏/首页界面跳转按钮')
    目录校验(device, '一', '首页', '/首页/选中状态')
    点击(device, '/首页/热点')
    目录校验(device, '二', '热点', '/首页/热点/选中状态')
    点击(device, '/首页/热点/我的资产')
    登录(device, 账号, 交易密码, 通信密码)
    循环滑动(device, '/首页/热点/我的资产/我的订阅', '上')
    点击(device, '/首页/热点/我的资产/我的订阅')
    # 实际结果 = 获取文本信息('/首页/热点/我的资产/我的订阅/第一条记录')
    点击(device, '/我的/极速开户/在线客服')
    sleep(3)
    点击(device, '我的/我的业务/问一问')
    点击(device, '/我的/极速开户/在线客服/输入消息')
    输入文本(device, '/我的/极速开户/在线客服/输入消息', '1')
    sleep(2)
    点击(device, '/我的/极速开户/在线客服/发送消息')
    实际结果 = 获取控件对象(device, '/我的/极速开户/在线客服/消息')
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


@casescript('首页/热点/我的资产/我的订阅_查看可订阅')
def _1becb9fdc2b7cb433f5c4876847e5db4(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    点击(device, '/底部导航栏/首页界面跳转按钮')
    目录校验(device, '一', '首页', '/首页/选中状态')
    点击(device, '/首页/热点')
    目录校验(device, '二', '热点', '/首页/热点/选中状态')
    点击(device, '/首页/热点/我的资产')
    登录(device, 账号, 交易密码, 通信密码)
    循环滑动(device, '/首页/热点/我的资产/我的订阅', '上')
    点击(device, '/首页/热点/我的资产/我的订阅')
    实际结果 = 获取文本信息(device, '/首页/热点/我的资产/我的订阅/第一条记录')
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


@casescript('首页/热点/我的资产/我的账单')
def _e4889dabcb0cb3ca08193265f2972ca5(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    点击(device, '/底部导航栏/首页界面跳转按钮')
    目录校验(device, '一', '首页', '/首页/选中状态')
    点击(device, '/首页/热点')
    目录校验(device, '二', '热点', '/首页/热点/选中状态')
    点击(device, '/首页/热点/我的资产')
    登录(device, 账号, 交易密码, 通信密码)
    循环滑动(device, '/首页/热点/我的资产/我的账单', '上')
    点击(device, '/首页/热点/我的资产/我的账单')
    上滑(device)
    点击(device, '/首页/热点/我的资产/我的账单/详情')
    实际结果 = 获取文本信息(device, '/首页/热点/我的资产/我的账单/总收益')
    结果 = float(实际结果) != 0
    错误校验点 = 数字校验(device, 实际结果)
    return 结果, 错误校验点


