from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('交易/普通交易/网络投票/我要投票_沪市投票_沪A累计')
def _77049351b299819fe54279e48c0c9d56(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    错误校验点 = ''
    # 初始化
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/普通交易')
    点击(device, '/交易/普通交易/普通交易首页/更多')
    目录校验(device, '二', '股票交易', '/页面标题')
    点击(device, '/交易/普通交易/更多/更多交易页面/网络投票')
    点击(device, '/交易/普通交易/更多/网络投票/网络投票页面/我要投票')
    点击(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/沪市投票')
    import re
    sleep(8)
    source = device.appium.driver.page_source
    print(source)
    codeList = source.split('公司代码')
    codeList = codeList
    codeListTemp = []
    for icodeList in codeList:
        if codeList.index(icodeList) == 0:
            continue
        x_index = icodeList.find('text="60') + len('text="')
        code = icodeList[x_index:x_index + 6]
        print('code ::::::::', code)
        string = re.search(r"\d{6}", code)
        print('str ::::::::', string)
        if string:
            code = string.group(0)
            codeListTemp.append(str(code))
            print('newCode :::', code)
    print('codeList:::', codeListTemp)
    结果 = False
    if codeListTemp:
        for icode in codeListTemp:
            证券代码 = icode
            输入文本(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/股票代码搜索输入框', 证券代码)
            点击(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/搜索按钮')
            点击(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/沪市投票/搜索页面/进入投票')
            sleep(3)
            source = device.appium.driver.page_source
            if '投票数' not in source:
                返回(device)
            else:
                break
        点击(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/我要投票/全部同意按钮')
        输入文本(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/我要投票/累积投票文本框', '10')
        点击(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/我要投票/提交按钮')
        点击(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/我要投票/投票确认/确定按钮')
        实际结果 = 获取文本信息(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/沪市投票/进入投票/提交确定/提示')
        预期结果 = '成功'
        错误校验点 = 非空校验(device, 预期结果, 实际结果)
        点击(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/沪市投票/进入投票/提交确定/提示确定')
        结果 = 预期结果 in 实际结果
    else:
        证券代码 = '600001'
        输入文本(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/股票代码搜索输入框', 证券代码)
        点击(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/搜索按钮')
        预期结果 = '今日无股东大会'
        实际结果 = 获取文本信息(device, '/系统提示')
        错误校验点 = 非空校验(device, 预期结果, 实际结果)
        点击(device, '/提示确认/确认按钮')
        结果 = 预期结果 in 实际结果
    return 结果, 错误校验点


@casescript('交易/普通交易/网络投票/我要投票_沪市投票_沪A非累计')
def _2b791b42075e340db8e65e50c8685f1a(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    错误校验点 = ''
    # 初始化
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/普通交易')
    点击(device, '/交易/普通交易/普通交易首页/更多')
    目录校验(device, '二', '股票交易', '/页面标题')
    点击(device, '/交易/普通交易/更多/更多交易页面/网络投票')
    点击(device, '/交易/普通交易/更多/网络投票/网络投票页面/我要投票')
    点击(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/沪市投票')
    import re
    sleep(8)
    source = device.appium.driver.page_source
    print(source)
    codeList = source.split('公司代码')
    codeList = codeList
    codeListTemp = []
    for icodeList in codeList:
        if codeList.index(icodeList) == 0:
            continue
        x_index = icodeList.find('text="60') + len('text="')
        code = icodeList[x_index:x_index + 6]
        print('code ::::::::', code)
        string = re.search(r"\d{6}", code)
        print('str ::::::::', string)
        if string:
            code = string.group(0)
            codeListTemp.append(str(code))
            print('newCode :::', code)
    结果 = False
    if codeListTemp:
        for icode in codeListTemp:
            证券代码 = icode
            输入文本(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/股票代码搜索输入框', 证券代码)
            点击(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/搜索按钮')
            点击(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/沪市投票/搜索页面/进入投票')
            sleep(3)
            source = device.appium.driver.page_source
            if '投票数' in source:
                返回(device)
            else:
                break
        点击(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/我要投票/全部同意按钮')
        点击(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/我要投票/提交按钮')
        点击(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/我要投票/投票确认/确定按钮')
        实际结果 = 获取文本信息(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/沪市投票/进入投票/提交确定/提示')
        预期结果 = '成功'
        错误校验点 = 非空校验(device, 预期结果, 实际结果)
        点击(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/沪市投票/进入投票/提交确定/提示确定')
        结果 = 预期结果 in 实际结果
    else:
        证券代码 = '600001'
        输入文本(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/股票代码搜索输入框', 证券代码)
        点击(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/搜索按钮')
        预期结果 = '今日无股东大会'
        实际结果 = 获取文本信息(device, '/系统提示')
        错误校验点 = 非空校验(device, 预期结果, 实际结果)
        点击(device, '/提示确认/确认按钮')
        结果 = 预期结果 in 实际结果
    return 结果, 错误校验点


@casescript('交易/普通交易/网络投票/我要投票_沪市投票_沪B累计')
def _259f468a516ea1e3e97bffb9f4481a5b(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    错误校验点 = ''
    # 初始化
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/普通交易')
    点击(device, '/交易/普通交易/普通交易首页/更多')
    目录校验(device, '二', '股票交易', '/页面标题')
    点击(device, '/交易/普通交易/更多/更多交易页面/网络投票')
    点击(device, '/交易/普通交易/更多/网络投票/网络投票页面/我要投票')
    点击(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/沪市投票')
    import re
    sleep(8)
    source = device.appium.driver.page_source
    print(source)
    codeList = source.split('公司代码')
    codeList = codeList
    codeListTemp = []
    for icodeList in codeList:
        if codeList.index(icodeList) == 0:
            continue
        x_index = icodeList.find('text="900') + len('text="')
        code = icodeList[x_index:x_index + 6]
        print('code ::::::::', code)
        string = re.search(r"\d{6}", code)
        print('str ::::::::', string)
        if string:
            code = string.group(0)
            codeListTemp.append(str(code))
            print('newCode :::', code)
    print('codeList:::', codeListTemp)
    结果 = False
    if codeListTemp:
        for icode in codeListTemp:
            证券代码 = icode
            输入文本(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/股票代码搜索输入框', 证券代码)
            点击(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/搜索按钮')
            点击(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/沪市投票/搜索页面/进入投票')
            sleep(3)
            source = device.appium.driver.page_source
            if '投票数' not in source:
                返回(device)
            else:
                break
        点击(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/我要投票/全部同意按钮')
        输入文本(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/我要投票/累积投票文本框', '10')
        点击(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/我要投票/提交按钮')
        点击(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/我要投票/投票确认/确定按钮')
        实际结果 = 获取文本信息(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/沪市投票/进入投票/提交确定/提示')
        预期结果 = '成功'
        错误校验点 = 非空校验(device, 预期结果, 实际结果)
        点击(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/沪市投票/进入投票/提交确定/提示确定')
        结果 = 预期结果 in 实际结果
    else:
        证券代码 = '900001'
        输入文本(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/股票代码搜索输入框', 证券代码)
        点击(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/搜索按钮')
        预期结果 = '今日无股东大会'
        实际结果 = 获取文本信息(device, '/系统提示')
        错误校验点 = 非空校验(device, 预期结果, 实际结果)
        点击(device, '/提示确认/确认按钮')
        结果 = 预期结果 in 实际结果
    return 结果, 错误校验点


@casescript('交易/普通交易/网络投票/我要投票_沪市投票_沪B非累计')
def _db63870a7d522d6b75dd837fa808f463(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    错误校验点 = ''
    # 初始化
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/普通交易')
    点击(device, '/交易/普通交易/普通交易首页/更多')
    目录校验(device, '二', '股票交易', '/页面标题')
    点击(device, '/交易/普通交易/更多/更多交易页面/网络投票')
    点击(device, '/交易/普通交易/更多/网络投票/网络投票页面/我要投票')
    点击(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/沪市投票')
    import re
    sleep(8)
    source = device.appium.driver.page_source
    print(source)
    codeList = source.split('公司代码')
    codeList = codeList
    codeListTemp = []
    for icodeList in codeList:
        if codeList.index(icodeList) == 0:
            continue
        x_index = icodeList.find('text="900') + len('text="')
        code = icodeList[x_index:x_index + 6]
        print('code ::::::::', code)
        string = re.search(r"\d{6}", code)
        print('str ::::::::', string)
        if string:
            code = string.group(0)
            codeListTemp.append(str(code))
            print('newCode :::', code)
    结果 = False
    if codeListTemp:
        for icode in codeListTemp:
            证券代码 = icode
            输入文本(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/股票代码搜索输入框', 证券代码)
            点击(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/搜索按钮')
            点击(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/沪市投票/搜索页面/进入投票')
            sleep(3)
            source = device.appium.driver.page_source
            if '投票数' in source:
                返回(device)
            else:
                break
        点击(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/我要投票/全部同意按钮')
        点击(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/我要投票/提交按钮')
        点击(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/我要投票/投票确认/确定按钮')
        实际结果 = 获取文本信息(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/沪市投票/进入投票/提交确定/提示')
        预期结果 = '成功'
        错误校验点 = 非空校验(device, 预期结果, 实际结果)
        点击(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/沪市投票/进入投票/提交确定/提示确定')
        结果 = 预期结果 in 实际结果
    else:
        证券代码 = '900001'
        输入文本(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/股票代码搜索输入框', 证券代码)
        点击(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/搜索按钮')
        预期结果 = '今日无股东大会'
        实际结果 = 获取文本信息(device, '/系统提示')
        错误校验点 = 非空校验(device, 预期结果, 实际结果)
        点击(device, '/提示确认/确认按钮')
        结果 = 预期结果 in 实际结果
    return 结果, 错误校验点


@casescript('交易/普通交易/网络投票/我要投票_深市投票_信用深A累计')
def _fb0d031ec26849ccc805c26cdf384cce(device, 账号='30002187', 交易密码='123123', 通信密码='123123'):
    错误校验点 = ''
    # 初始化
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/普通交易')
    点击(device, '/交易/普通交易/普通交易首页/更多')
    目录校验(device, '二', '股票交易', '/页面标题')
    #
    # 点击('/交易/普通交易/普通交易首页/融资融券')
    # 上滑()
    # 点击('/底部导航栏/首页界面跳转按钮')
    # 点击('/底部导航栏/交易界面跳转按钮')
    # 点击('/交易/融资融券/融资融券首页/更多')
    点击(device, '/交易/普通交易/更多/更多交易页面/网络投票')
    点击(device, '/交易/普通交易/更多/网络投票/网络投票页面/我要投票')
    点击(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/深市投票')
    import re
    sleep(8)
    source = device.appium.driver.page_source
    print(source)
    codeList = source.split('公司代码')
    codeList = codeList
    codeListTemp = []
    for icodeList in codeList:
        if codeList.index(icodeList) == 0:
            continue
        x_index = icodeList.find('text="00') + len('text="')
        code = icodeList[x_index:x_index + 6]
        print('code ::::::::', code)
        string = re.search(r"\d{6}", code)
        print('str ::::::::', string)
        if string:
            code = string.group(0)
            codeListTemp.append(str(code))
            print('newCode :::', code)
    print('codeList:::', codeListTemp)
    结果 = False
    if codeListTemp:
        for icode in codeListTemp:
            证券代码 = icode
            输入文本(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/股票代码搜索输入框', 证券代码)
            点击(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/搜索按钮')
            点击(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/沪市投票/搜索页面/进入投票')
            sleep(3)
            source = device.appium.driver.page_source
            if '投票数' not in source:
                返回(device)
            else:
                break
        点击(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/我要投票/全部同意按钮')
        输入文本(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/我要投票/累积投票文本框', '10')
        点击(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/我要投票/提交按钮')
        点击(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/我要投票/投票确认/确定按钮')
        实际结果 = 获取文本信息(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/沪市投票/进入投票/提交确定/提示')
        预期结果 = '成功'
        错误校验点 = 非空校验(device, 预期结果, 实际结果)
        点击(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/沪市投票/进入投票/提交确定/提示确定')
        结果 = 预期结果 in 实际结果
    else:
        证券代码 = '000001'
        输入文本(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/股票代码搜索输入框', 证券代码)
        点击(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/搜索按钮')
        预期结果 = '今日无股东大会'
        实际结果 = 获取文本信息(device, '/系统提示')
        错误校验点 = 非空校验(device, 预期结果, 实际结果)
        点击(device, '/提示确认/确认按钮')
        结果 = 预期结果 in 实际结果
    return 结果, 错误校验点


@casescript('交易/普通交易/网络投票/我要投票_深市投票_信用深A非累计')
def _5358086cd9ab1de308a328ce7b9da880(device, 账号='30002187', 交易密码='123123', 通信密码='123123'):
    错误校验点 = ''
    # 初始化
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/普通交易')
    点击(device, '/交易/普通交易/普通交易首页/更多')
    目录校验(device, '二', '股票交易', '/页面标题')
    #
    # 点击('/交易/普通交易/普通交易首页/融资融券')
    # 上滑()
    # 点击('/底部导航栏/首页界面跳转按钮')
    # 点击('/底部导航栏/交易界面跳转按钮')
    # 点击('/交易/融资融券/融资融券首页/更多')
    点击(device, '/交易/普通交易/更多/更多交易页面/网络投票')
    点击(device, '/交易/普通交易/更多/网络投票/网络投票页面/我要投票')
    点击(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/深市投票')
    import re
    sleep(8)
    source = device.appium.driver.page_source
    print(source)
    codeList = source.split('公司代码')
    codeList = codeList
    codeListTemp = []
    for icodeList in codeList:
        if codeList.index(icodeList) == 0:
            continue
        x_index = icodeList.find('text="00') + len('text="')
        code = icodeList[x_index:x_index + 6]
        print('code ::::::::', code)
        string = re.search(r"\d{6}", code)
        print('str ::::::::', string)
        if string:
            code = string.group(0)
            codeListTemp.append(str(code))
            print('newCode :::', code)
    结果 = False
    if codeListTemp:
        for icode in codeListTemp:
            证券代码 = icode
            输入文本(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/股票代码搜索输入框', 证券代码)
            点击(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/搜索按钮')
            点击(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/沪市投票/搜索页面/进入投票')
            sleep(3)
            source = device.appium.driver.page_source
            if '投票数' in source:
                返回(device)
            else:
                break
        点击(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/我要投票/全部同意按钮')
        点击(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/我要投票/提交按钮')
        点击(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/我要投票/投票确认/确定按钮')
        实际结果 = 获取文本信息(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/沪市投票/进入投票/提交确定/提示')
        预期结果 = '成功'
        错误校验点 = 非空校验(device, 预期结果, 实际结果)
        点击(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/沪市投票/进入投票/提交确定/提示确定')
        结果 = 预期结果 in 实际结果
    else:
        证券代码 = '000001'
        输入文本(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/股票代码搜索输入框', 证券代码)
        点击(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/搜索按钮')
        预期结果 = '今日无股东大会'
        实际结果 = 获取文本信息(device, '/系统提示')
        错误校验点 = 非空校验(device, 预期结果, 实际结果)
        点击(device, '/提示确认/确认按钮')
        结果 = 预期结果 in 实际结果
    return 结果, 错误校验点


@casescript('交易/普通交易/网络投票/我要投票_深市投票_普通深A累计')
def _faec72903a271b175586283904de1cf7(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    错误校验点 = ''
    # 初始化
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/普通交易')
    点击(device, '/交易/普通交易/普通交易首页/更多')
    目录校验(device, '二', '股票交易', '/页面标题')
    点击(device, '/交易/普通交易/更多/更多交易页面/网络投票')
    点击(device, '/交易/普通交易/更多/网络投票/网络投票页面/我要投票')
    点击(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/深市投票')
    import re
    sleep(8)
    source = device.appium.driver.page_source
    print(source)
    codeList = source.split('公司代码')
    codeList = codeList
    codeListTemp = []
    for icodeList in codeList:
        if codeList.index(icodeList) == 0:
            continue
        x_index = icodeList.find('text="00') + len('text="')
        code = icodeList[x_index:x_index + 6]
        print('code ::::::::', code)
        string = re.search(r"\d{6}", code)
        print('str ::::::::', string)
        if string:
            code = string.group(0)
            codeListTemp.append(str(code))
            print('newCode :::', code)
    print('codeList:::', codeListTemp)
    结果 = False
    if codeListTemp:
        for icode in codeListTemp:
            证券代码 = icode
            输入文本(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/股票代码搜索输入框', 证券代码)
            点击(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/搜索按钮')
            点击(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/沪市投票/搜索页面/进入投票')
            sleep(3)
            source = device.appium.driver.page_source
            if '投票数' not in source:
                返回(device)
            else:
                break
        点击(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/我要投票/全部同意按钮')
        输入文本(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/我要投票/累积投票文本框', '10')
        点击(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/我要投票/提交按钮')
        点击(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/我要投票/投票确认/确定按钮')
        实际结果 = 获取文本信息(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/沪市投票/进入投票/提交确定/提示')
        预期结果 = '成功'
        错误校验点 = 非空校验(device, 预期结果, 实际结果)
        点击(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/沪市投票/进入投票/提交确定/提示确定')
        结果 = 预期结果 in 实际结果
    else:
        证券代码 = '000001'
        输入文本(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/股票代码搜索输入框', 证券代码)
        点击(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/搜索按钮')
        预期结果 = '今日无股东大会'
        实际结果 = 获取文本信息(device, '/系统提示')
        错误校验点 = 非空校验(device, 预期结果, 实际结果)
        点击(device, '/提示确认/确认按钮')
        结果 = 预期结果 in 实际结果
    return 结果, 错误校验点


@casescript('交易/普通交易/网络投票/我要投票_深市投票_普通深A非累计')
def _4c765f065300775cd6ddeda68d3355f0(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    错误校验点 = ''
    # 初始化
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/普通交易')
    点击(device, '/交易/普通交易/普通交易首页/更多')
    目录校验(device, '二', '股票交易', '/页面标题')
    点击(device, '/交易/普通交易/更多/更多交易页面/网络投票')
    点击(device, '/交易/普通交易/更多/网络投票/网络投票页面/我要投票')
    点击(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/深市投票')
    import re
    sleep(8)
    source = device.appium.driver.page_source
    print(source)
    codeList = source.split('公司代码')
    codeList = codeList
    codeListTemp = []
    for icodeList in codeList:
        if codeList.index(icodeList) == 0:
            continue
        x_index = icodeList.find('text="00') + len('text="')
        code = icodeList[x_index:x_index + 6]
        print('code ::::::::', code)
        string = re.search(r"\d{6}", code)
        print('str ::::::::', string)
        if string:
            code = string.group(0)
            codeListTemp.append(str(code))
            print('newCode :::', code)
    结果 = False
    if codeListTemp:
        for icode in codeListTemp:
            证券代码 = icode
            输入文本(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/股票代码搜索输入框', 证券代码)
            点击(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/搜索按钮')
            点击(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/沪市投票/搜索页面/进入投票')
            sleep(3)
            source = device.appium.driver.page_source
            if '投票数' in source:
                返回(device)
            else:
                break
        点击(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/我要投票/全部同意按钮')
        点击(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/我要投票/提交按钮')
        点击(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/我要投票/投票确认/确定按钮')
        实际结果 = 获取文本信息(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/沪市投票/进入投票/提交确定/提示')
        预期结果 = '成功'
        错误校验点 = 非空校验(device, 预期结果, 实际结果)
        点击(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/沪市投票/进入投票/提交确定/提示确定')
        结果 = 预期结果 in 实际结果
    else:
        证券代码 = '000001'
        输入文本(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/股票代码搜索输入框', 证券代码)
        点击(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/搜索按钮')
        预期结果 = '今日无股东大会'
        实际结果 = 获取文本信息(device, '/系统提示')
        错误校验点 = 非空校验(device, 预期结果, 实际结果)
        点击(device, '/提示确认/确认按钮')
        结果 = 预期结果 in 实际结果
    return 结果, 错误校验点


@casescript('交易/普通交易/网络投票/我要投票_深市投票_深B累计')
def _1c9e9672755cd96894cde3cb88c9e248(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    错误校验点 = ''
    # 初始化
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/普通交易')
    点击(device, '/交易/普通交易/普通交易首页/更多')
    目录校验(device, '二', '股票交易', '/页面标题')
    点击(device, '/交易/普通交易/更多/更多交易页面/网络投票')
    点击(device, '/交易/普通交易/更多/网络投票/网络投票页面/我要投票')
    点击(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/深市投票')
    import re
    sleep(8)
    source = device.appium.driver.page_source
    print(source)
    codeList = source.split('公司代码')
    codeList = codeList
    codeListTemp = []
    for icodeList in codeList:
        if codeList.index(icodeList) == 0:
            continue
        x_index = icodeList.find('text="200') + len('text="')
        code = icodeList[x_index:x_index + 6]
        print('code ::::::::', code)
        string = re.search(r"\d{6}", code)
        print('str ::::::::', string)
        if string:
            code = string.group(0)
            codeListTemp.append(str(code))
            print('newCode :::', code)
    print('codeList:::', codeListTemp)
    结果 = False
    if codeListTemp:
        for icode in codeListTemp:
            证券代码 = icode
            输入文本(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/股票代码搜索输入框', 证券代码)
            点击(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/搜索按钮')
            点击(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/沪市投票/搜索页面/进入投票')
            sleep(3)
            source = device.appium.driver.page_source
            if '投票数' not in source:
                返回(device)
            else:
                break
        点击(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/我要投票/全部同意按钮')
        输入文本(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/我要投票/累积投票文本框', '10')
        点击(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/我要投票/提交按钮')
        点击(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/我要投票/投票确认/确定按钮')
        实际结果 = 获取文本信息(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/沪市投票/进入投票/提交确定/提示')
        预期结果 = '成功'
        错误校验点 = 非空校验(device, 预期结果, 实际结果)
        点击(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/沪市投票/进入投票/提交确定/提示确定')
        结果 = 预期结果 in 实际结果
    else:
        证券代码 = '200001'
        输入文本(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/股票代码搜索输入框', 证券代码)
        点击(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/搜索按钮')
        预期结果 = '今日无股东大会'
        实际结果 = 获取文本信息(device, '/系统提示')
        错误校验点 = 非空校验(device, 预期结果, 实际结果)
        点击(device, '/提示确认/确认按钮')
        结果 = 预期结果 in 实际结果
    return 结果, 错误校验点


@casescript('交易/普通交易/网络投票/我要投票_深市投票_深B非累计')
def _4222d06ceecc3cbe697529810389155b(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    错误校验点 = ''
    # 初始化
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/普通交易')
    点击(device, '/交易/普通交易/普通交易首页/更多')
    目录校验(device, '二', '股票交易', '/页面标题')
    点击(device, '/交易/普通交易/更多/更多交易页面/网络投票')
    点击(device, '/交易/普通交易/更多/网络投票/网络投票页面/我要投票')
    点击(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/深市投票')
    import re
    sleep(8)
    source = device.appium.driver.page_source
    print(source)
    codeList = source.split('公司代码')
    codeList = codeList
    codeListTemp = []
    for icodeList in codeList:
        if codeList.index(icodeList) == 0:
            continue
        x_index = icodeList.find('text="200') + len('text="')
        code = icodeList[x_index:x_index + 6]
        print('code ::::::::', code)
        string = re.search(r"\d{6}", code)
        print('str ::::::::', string)
        if string:
            code = string.group(0)
            codeListTemp.append(str(code))
            print('newCode :::', code)
    结果 = False
    if codeListTemp:
        for icode in codeListTemp:
            证券代码 = icode
            输入文本(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/股票代码搜索输入框', 证券代码)
            点击(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/搜索按钮')
            点击(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/沪市投票/搜索页面/进入投票')
            sleep(3)
            source = device.appium.driver.page_source
            if '投票数' in source:
                返回(device)
            else:
                break
        点击(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/我要投票/全部同意按钮')
        点击(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/我要投票/提交按钮')
        点击(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/我要投票/投票确认/确定按钮')
        实际结果 = 获取文本信息(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/沪市投票/进入投票/提交确定/提示')
        预期结果 = '成功'
        错误校验点 = 非空校验(device, 预期结果, 实际结果)
        点击(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/沪市投票/进入投票/提交确定/提示确定')
        结果 = 预期结果 in 实际结果
    else:
        证券代码 = '200001'
        输入文本(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/股票代码搜索输入框', 证券代码)
        点击(device, '/交易/普通交易/更多/网络投票/我要投票/投票首页/搜索按钮')
        预期结果 = '今日无股东大会'
        实际结果 = 获取文本信息(device, '/系统提示')
        错误校验点 = 非空校验(device, 预期结果, 实际结果)
        点击(device, '/提示确认/确认按钮')
        结果 = 预期结果 in 实际结果
    return 结果, 错误校验点


@casescript('交易/普通交易/网络投票/投票查询_查询今日投票_查已投票信息')
def _00277d9305a0a77c3ba230f602ca7399(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119', 股票代码='601009'):
    错误校验点 = ''
    # 初始化
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/普通交易')
    点击(device, '/交易/普通交易/普通交易首页/更多')
    目录校验(device, '二', '股票交易', '/页面标题')
    点击(device, '/交易/普通交易/更多/更多交易页面/网络投票')
    点击(device, '/交易/普通交易/更多/网络投票/网络投票页面/投票查询')
    点击(device, '/交易/普通交易/更多/网络投票/投票查询/今日投票页面/查询今日投票')
    # 点击('/交易/普通交易/更多/网络投票/投票查询/今日投票页面/查询历史投票')
    try:
        实际结果 = 获取文本信息(device, '/交易/普通交易/更多/网络投票/投票查询/今日投票页面/查询今日投票/无记录')
        错误校验点 = 为空校验(device)
        结果 = 实际结果 != ''
    except:
        实际结果 = 获取文本信息(device, '/交易/普通交易/更多/网络投票/投票查询/今日投票页面/查询今日投票/第一条')
        错误校验点 = 为空校验(device)
        结果 = 实际结果 != ''
    return 结果, 错误校验点


@casescript('交易/普通交易/网络投票/投票查询_查询历史投票_查历史投票')
def _721b0229b49f962d32eee5cf1a8c8c84(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119', 证券代码='601009'):
    # 初始化
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/普通交易')
    点击(device, '/交易/普通交易/普通交易首页/更多')
    目录校验(device, '二', '股票交易', '/页面标题')
    点击(device, '/交易/普通交易/更多/更多交易页面/网络投票')
    点击(device, '/交易/普通交易/更多/网络投票/网络投票页面/投票查询')
    点击(device, '/交易/普通交易/更多/网络投票/投票查询/今日投票页面/查询历史投票')
    实际结果 = ''
    try:
        点击(device, '/交易/普通交易/更多/网络投票/投票查询/历史投票页面/查询历史投票/第一条')
        点击(device, '/交易/普通交易/更多/网络投票/投票查询/历史投票/第一个投票/第一条')
        股东大会名称 = 获取文本信息(device, '/交易/普通交易/更多/网络投票/投票查询/历史投票/第一个投票/第一条/投票结果/股东大会名称')
        实际结果 = 股东大会名称
    except:
        实际结果 = 获取控件对象(device, '/交易/普通交易/更多/网络投票/投票查询/历史投票页面/查询历史投票/无记录')
    错误校验点 = 为空校验(device)
    结果 = 实际结果 != ''
    return 结果, 错误校验点


@casescript('交易/普通交易/网络投票/网络投票密码管理_密码激活')
def _c0f644e9a2d165c15bb39c80476198a3(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # 初始化
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/普通交易')
    点击(device, '/交易/普通交易/普通交易首页/更多')
    目录校验(device, '二', '股票交易', '/页面标题')
    点击(device, '/交易/普通交易/更多/更多交易页面/网络投票')
    点击(device, '/交易/普通交易/更多/网络投票/网络投票页面/网络投票密码管理')
    点击(device, '/交易/普通交易/更多/网络投票/网络投票密码管理/服务密码管理页面/去激活')
    结果 = 获取控件对象(device, '/交易/普通交易/更多/网络投票/网络投票密码管理/密码激活页面/提交') != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


