from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('首页/学习/课程中心/推荐')
def _4b6b1ccee86ac6c1b353f2c63732c31a(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # 初始化
    登录前页面处理(device)
    点击(device, '/底部导航栏/首页界面跳转按钮')
    目录校验(device, '一', '首页', '/首页/选中状态')
    点击(device, '/首页/学习')
    目录校验(device, '二', '学习', '/首页/学习/选中状态')
    点击(device, '/首页/学习/课程中心')
    点击(device, '/首页/学习/课程中心/推荐')
    sleep(3)
    实际结果 = 获取文本信息(device, '/首页/学习/课程中心/推荐/第一条记录')
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


@casescript('首页/学习/课程中心/理财')
def _aa32494257925713070abcb20b0fd4d7(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # 初始化
    登录前页面处理(device)
    点击(device, '/底部导航栏/首页界面跳转按钮')
    目录校验(device, '一', '首页', '/首页/选中状态')
    点击(device, '/首页/学习')
    目录校验(device, '二', '学习', '/首页/学习/选中状态')
    点击(device, '/首页/学习/课程中心')
    # time.sleep(3)
    点击(device, '/首页/学习/课程中心/理财')
    sleep(3)
    实际结果 = 获取控件对象(device, '/首页/学习/课程中心/理财/第一条记录')
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


@casescript('首页/学习/课程中心/股票')
def _c73af2434862a449e9c2b6e8f94e98c7(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # 初始化
    登录前页面处理(device)
    点击(device, '/底部导航栏/首页界面跳转按钮')
    目录校验(device, '一', '首页', '/首页/选中状态')
    点击(device, '/首页/学习')
    目录校验(device, '二', '学习', '/首页/学习/选中状态')
    点击(device, '/首页/学习/课程中心')
    点击(device, '/首页/学习/课程中心/股票')
    sleep(3)
    实际结果 = 获取文本信息(device, '/首页/学习/课程中心/股票/第一条记录')
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


@casescript('首页/学习/课程中心/财经')
def _84a6cfb95f30f77c173e1c11f8b99bda(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # 初始化
    登录前页面处理(device)
    点击(device, '/底部导航栏/首页界面跳转按钮')
    目录校验(device, '一', '首页', '/首页/选中状态')
    点击(device, '/首页/学习')
    目录校验(device, '二', '学习', '/首页/学习/选中状态')
    点击(device, '/首页/学习/课程中心')
    sleep(2)
    点击(device, '/首页/学习/课程中心/财经')
    # time.sleep(3)
    # 点击('/首页/学习/课程中心/财经/第一条记录')
    实际结果 = 获取控件对象(device, '/首页/学习/课程中心/财经/第一条记录')
    结果 = 实际结果!=''
    错误校验点 = 为空校验(device)
    # 实际结果 = 获取文本信息('/首页/学习/课程中心/财经/第一条记录/详情')
    # 预期结果 = '课程简介'
    # 结果 = 预期结果 in 实际结果
    # 全局变量.错误校验点 = 非空校验(预期结果, 实际结果)
    return 结果, 错误校验点


