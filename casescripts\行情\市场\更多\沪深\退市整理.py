from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('行情/市场/更多/沪深/退市整理/退市整理')
def _9bf16ae834c1def2feda4d5cac0582d4(device):
    登录前页面处理(device)
    print(device.appium.driver)
    点击(device, '/底部导航栏/行情界面跳转按钮')
    点击(device, '/行情/市场/市场标题栏')
    控件左滑(device, '/行情/市场/沪深按钮标签')
    点击(device, '/行情/市场/更多/更多标签')
    点击(device, '/行情/市场/更多/沪深/退市整理/退市整理标签')
    结果 = ''
    try:
        首只退市名称 = 获取文本信息(device, '/行情/市场/更多/沪深/退市整理/首只退市股票名称')
        结果 = 首只退市名称 != ''
    except:
        暂无排名 = 获取文本信息(device, '/行情/市场/更多/沪深/退市整理/无退市股票提示')
        结果 = '无相应的排名信息' in 暂无排名
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


