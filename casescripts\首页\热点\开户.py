from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('首页/热点/开户/开户')
def _0bf671799e3147efe771c98e78edce19(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119', 预期结果='手机号码验证'):
    # 初始化
    登录前页面处理(device)
    点击(device, '/底部导航栏/首页界面跳转按钮')
    目录校验(device, '一', '首页', '/首页/选中状态')
    点击(device, '/首页/热点')
    目录校验(device, '二', '热点', '/首页/热点/选中状态')
    点击(device, '/首页/热点/向右点击按钮')
    点击(device, '/首页/热点/开户')
    点击(device, '/首页/热点/开户/立即开户')
    实际结果 = 获取文本信息(device, '/页面标题')
    # 预期结果 = '开户首页'
    # print(实际结果)
    # 预期结果 = '成功'
    结果 = 预期结果 in 实际结果
    错误校验点 = 非空校验(device, 预期结果, 实际结果)
    return 结果, 错误校验点


