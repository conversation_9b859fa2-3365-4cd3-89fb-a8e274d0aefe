from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('行情/市场/更多/沪深/大盘指数/获取大盘指数名称')
def _ae2d61c61b24947a943f034d91460ea3(device):
    登录前页面处理(device)
    print(device.appium.driver)
    点击(device, '/底部导航栏/行情界面跳转按钮')
    点击(device, '/行情/市场/市场标题栏')
    控件左滑(device, '/行情/市场/沪深按钮标签')
    点击(device, '/行情/市场/更多/更多标签')
    点击(device, '/行情/市场/更多/沪深/大盘指数/大盘指数标签')
    首只风险警示股名称 = 获取文本信息(device, '/行情/市场/更多/沪深/大盘指数/大盘指数名称')
    结果 = 首只风险警示股名称 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


