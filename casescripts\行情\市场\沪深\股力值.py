from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('行情/市场/沪深/股力值/超高股力值收益')
def _fb406f086801465e8ebfe7c5105d9bee(device):
    登录前页面处理(device)
    点击(device, '/底部导航栏/行情界面跳转按钮')
    目录校验(device, '一', '行情', '/行情/选中状态')
    点击(device, '/行情/市场/市场标题栏')
    目录校验(device, '二', '沪深', '/行情/更多/选中状态')
    滑动控件至屏幕内(device, '/行情/市场/沪深/市场热度/市场热度标签')
    sleep(2)
    控件左滑(device, '/行情/市场/沪深/市场热度/市场热度标签')
    # 刷新页面_行情()
    点击(device, '/行情/市场/沪深/股力值/股力值标签')
    超高股力值收益 = 获取文本信息(device, '/行情/市场/沪深/股力值/超高股力值收益')
    结果 = 超高股力值收益 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


