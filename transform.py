import os
import re


def decode_file(path):
    if not os.path.isfile(path) or path[-3:] != '.py':
        return None, None, None, False
    _p = '涨乐测试脚本/'
    case_name = path[path.find(_p) + len(_p):].replace('\\', '/').strip('/')[:-3]
    content = []
    transactional = False
    args = {}
    _hidden_value = ['开始录像', '停止录像', '用例名称', '文件路径', 'getMobileInfo']
    _function_map = [
        # (r"str(device['\1'])", r'获取文本信息\([\'|"]([^\'"]+?)[\'|"]\)'),
        # (r"device['\1']()", r'点击\([\'|"]([^\'"]+?)[\'|"]\)'),
        # (r"device['\1'] = \2", r'输入文本\([\'|"]([^\'"]+?)[\'|"]\,\s?([^\'"]+?)\)'),
        (r"sleep(\1)", r'time.sleep\((\d+)\)'),
        # (r"device.appium.slide_up()", r'上滑\(\)'),
        # (r" device.appium.slide_left()", r'\s左滑\(\)'),
        (r"\1(device)", r'([\u4e00-\u9fa5]+)\(\)'),
        (r"\1(device, ", r'([\u4e00-\u9fa5]+)\('),
        (r"device.appium.driver", r'全局变量\.Drive'),
    ]

    with open(path, 'r', encoding='utf-8') as f:
        start = False
        has_message = False
        while True:
            line = f.readline()
            if not line:
                break
            line = line.rstrip('\n')
            _line = line.strip()
            _left = line[0: line.find(_line)]

            if not line or not _line:
                continue
            if line[0] == '#' or 'import' in line and not start or _line[0] == '#':
                if start and _line[0] == '#':
                    content.append(line)
                continue
            if 'print(' in line and '事务型' in line:
                transactional = True
                continue
            flag = False
            for _h in _hidden_value:
                if _h in line:
                    flag = True
                    break
            if flag:
                continue

            if _line.find('业务交易数据') == 0:
                continue
            if _line == "证券代码 = 业务交易数据['证券代码']":
                args['证券代码'] = ''
                continue
            if line == 'try:':
                start = True
                continue

            if line.find('except') == 0:
                start = False
            is_set = re.match(r'^\s{4}([^.\ ]+)\s?=\s[\'|"]([\w\.]+)[\'|"]', line)
            if is_set is not None:
                args[is_set.group(1)] = is_set.group(2)
                continue

            if start:
                if '结果校验(' in line:
                    if has_message:
                        line2 = re.sub('结果校验\((.+)\)', r"return \1, 错误校验点", line)
                    else:
                        line2 = re.sub('结果校验\((.+)\)', r"return \1, None", line)
                    content.append(line2)
                    continue
                flag = False
                if line.find('except') == 0:
                    start = False

                if '全局变量.错误校验点' in line:
                    has_message = True
                    line = line.replace('全局变量.错误校验点', '错误校验点')
                    _line = _line.replace('全局变量.错误校验点', '错误校验点')
                    if len(content) > 0 and '错误校验点' not in content[0] and line[0:8] == ' ' * 8:
                        content.insert(0, "    错误校验点 = ''")

                for _s in _function_map:
                    s, r = _s
                    line2 = re.sub(r, s, line)
                    if line2 != line:
                        content.append(line2)
                        flag = True
                        break
                if flag:
                    continue
                # r"错误校验点": r'全局变量.错误校验点',
                # r"return ": r'结果校验\((.+)\)',

                content.append(line)

    return case_name, content, args, transactional


def decode_script(path):
    if not os.path.exists(path):
        return None
    if os.path.isdir(path):
        return None
    case_name, content, args, transactional = decode_file(path)
    if not content:
        return ''
    import hashlib
    script = []
    _md5 = hashlib.md5()
    _md5.update(case_name.encode(encoding='utf-8'))
    _case_id = str(_md5.hexdigest())
    script.append("@casescript('%s')" % case_name)
    fun_name = '_%s' % _case_id.replace('-', '_')
    args_str = ['device']
    for k in args:
        args_str.append("%s='%s'" % (k, args[k]))
    script.append('def %s(%s):' % (fun_name, ', '.join(args_str)))
    script += content
    script.append('')
    script.append('')
    script.append('')
    return '\n'.join(script)


def dirs_map(path, root_path=None):
    if not root_path:
        root_path = path
    if os.path.isfile(path):
        return
    d = os.listdir(path)
    if len(d) <= 0:
        return
    if not sum(os.path.isdir(os.path.join(path, _d)) for _d in d):
        data = '\n'.join(['from libs.adb import LocalDevice', 'from libs.sdk import casescript', 'from time import sleep', 'from casescripts.public import *', '', '', ''])
        newpath = path.replace(root_path, 'casescripts')
        newpath_dir = os.path.dirname(newpath)
        if not os.path.exists(newpath_dir):
            os.makedirs(newpath_dir)
        for f in os.listdir(path):
            path4 = os.path.join(path, f)
            _data = decode_script(path4.replace('\\', '/'))
            if _data:
                data += _data
        with open(newpath + '.py', 'w', encoding='utf-8') as fa:
            fa.write(data)
    else:
        for _d in d:
            dirs_map(os.path.join(path, _d), root_path)


def git_download():
    if os.path.exists('.casescript'):
        os.system('cd .casescript && git pull')
        print(os.getcwd())
    else:
        user = input('用户名:')
        pwd = input('密码:')
        u = 'https://%s:%<EMAIL>/whale/axTestCase.git' % (user, pwd)
        os.system('git clone %s .casescript' % u)
    dirs_map('.casescript/涨乐测试脚本')
    for dir_name, _, _ in os.walk('casescripts'):
        with open(dir_name + '/__init__.py', 'a') as f:
            f.close()
    os.system('git add casescripts')


if __name__ == '__main__':
    git_download()
