from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('交易/普通交易/银证转账/我的资金')
def _d8c9023699cffb3124b6825a911eb71b(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # 真实账号
    # 初始化
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/普通交易')
    点击(device, '/交易/普通交易/普通交易首页/银证转账')
    目录校验(device, '二', '银证转账', '/页面标题')
    点击(device, '/交易/普通交易/普通交易_银证转账页面/我的资金')
    点击(device, '/交易/普通交易/银证转账/我的资金页面/查看资金归集按钮')
    实际结果 = 获取文本信息(device, '/交易/普通交易/银证转账/资金归集页面/可用现金')
    # 可用资金=获取文本信息('/交易/普通交易/银证转账/我的资金页面/人民币全部可用资金')
    错误校验点 = 数字校验(device, 实际结果)
    结果 = float(实际结果) > 0
    return 结果, 错误校验点


@casescript('交易/普通交易/银证转账/调拨记录')
def _c864f8c2cc1edeeb0bc94af172612320(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    错误校验点 = ''
    # 真实账号
    # 初始化
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/普通交易')
    点击(device, '/交易/普通交易/普通交易首页/银证转账')
    目录校验(device, '二', '银证转账', '/页面标题')
    点击(device, '/交易/普通交易/普通交易_银证转账页面/调拨记录')
    结果 = ''
    try:
        点击(device, '/交易/普通交易/普通交易_银证转账/调拨记录/刷新')
    except:
        暂无记录 = 获取文本信息(device, '/交易/普通交易/普通交易_银证转账/调拨记录/暂无记录')
        错误校验点 = 为空校验(device)
        结果 = 暂无记录 == '暂无调拨记录'
    else:
        点击(device, '/交易/普通交易/普通交易_银证转账/调拨记录/第一条记录')
        # 预期结果 = '未报'
        实际结果 = 获取文本信息(device, '/交易/普通交易/普通交易_银证转账/调拨记录/调拨明细/状态')
        结果 = 实际结果!=''
        # 全局变量.错误校验点 = 非空校验(预期结果, 实际结果)
        错误校验点 = 为空校验(device)
        # 结果 = 预期结果 in 实际结果
    return 结果, 错误校验点


@casescript('交易/普通交易/银证转账/资金归集')
def _e960626f26eda58f0fa5b8c3ede60c71(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119', 预期结果='成功'):
    错误校验点 = ''
    # 真实账号
    # 初始化
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/普通交易')
    点击(device, '/交易/普通交易/普通交易首页/银证转账')
    目录校验(device, '二', '银证转账', '/页面标题')
    点击(device, '/交易/普通交易/普通交易_银证转账页面/资金归集')
    主账号可用资金 = 获取文本信息(device, '/交易/银证转账/资金归集/主账号可用资金')
    print('主账号可用资金：%s' % float(主账号可用资金))
    点击(device, '/交易/普通交易/银证转账/资金归集/勾选辅账号')
    辅账号可用资金 = 截取合同号(device, 获取文本信息(device, '/交易/银证转账/资金归集/辅账号可用金额'))
    print('辅账号可用资金：%s' % float(辅账号可用资金))
    点击(device, '/交易/普通交易/银证转账/资金归集页面/归结至主账号按钮')
    实际结果 = 获取文本信息(device, '/交易/普通交易/银证转账/资金归集/提交页面/结果')
    结果 = 预期结果 in 实际结果
    错误校验点 = 非空校验(device, 预期结果, 实际结果)
    # 如果是辅账号资金不足则不算失败
    if not 结果:
        结果 = '0.00元' in 实际结果
    点击(device, '/交易/普通交易/银证转账/资金归集/提交页面/返回')
    if 结果:
        点击(device, '/交易/普通交易/普通交易_银证转账页面/资金归集')
        可用资金 = 获取文本信息(device, '/交易/银证转账/资金归集/主账号可用资金')
        print('可用资金：%s' % float(可用资金))
        结果 = float(可用资金) == float(主账号可用资金) + float(辅账号可用资金)
        错误校验点 = '归集前主账号资金为：%s，辅账号资金为：%s；归集后主账号资金为：%s' % \
                     (float(主账号可用资金), float(辅账号可用资金), float(可用资金))
    return 结果, 错误校验点


@casescript('交易/普通交易/银证转账/资金调拨')
def _2e25e964b41afbb838fd809a748fd3d0(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119', 转入金额='1'):
    错误校验点 = ''
    # 真实账号
    # 初始化
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/普通交易')
    点击(device, '/交易/普通交易/普通交易首页/银证转账')
    目录校验(device, '二', '银证转账', '/页面标题')
    点击(device, '/交易/普通交易/普通交易_银证转账页面/资金调拨')
    点击(device, '/交易/普通交易/普通交易_银证转账/资金调拨页面/选择辅助账号')
    点击(device, '/交易/普通交易/普通交易_银证转账/资金调拨页面/选择辅助账号/第一个账户')
    sleep(3)
    点击(device, '/交易/普通交易/普通交易_银证转账/资金调拨页面/主辅切换')
    输入文本(device, '/交易/普通交易/普通交易_银证转账/资金调拨页面/转入金额', 转入金额)
    点击(device, '/交易/普通交易/普通交易_银证转账/资金调拨页面/可用资金')
    点击(device, '/交易/普通交易/普通交易_银证转账/资金调拨页面/确定')
    try:
        获取文本信息(device, '/交易/普通交易/普通交易_银证转账/资金调拨页面/提示信息')
    except:
        预期结果 = '成功'
        实际结果 = 获取文本信息(device, '/交易/普通交易/普通交易_银证转账/资金调拨页面/调拨结果页面/结果')
        错误校验点 = 非空校验(device, 预期结果, 实际结果)
        结果 = 预期结果 in 实际结果
        return 结果, 错误校验点
    else:
        错误校验点 = 获取文本信息(device, '/交易/普通交易/普通交易_银证转账/资金调拨页面/提示信息')
        截屏(device)
        点击(device, '/提示确认/确认按钮')
        return False, 错误校验点


@casescript('交易/普通交易/银证转账/转入')
def _55021a31bbdde589803949de1b8b174e(device, 账号='666630296771', 交易密码='124369', 通信密码='159753', 资金密码='124369', 转入金额='1', 预期结果='成功'):
    错误校验点 = ''
    # 初始化
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/普通交易')
    点击(device, '/交易/普通交易/普通交易首页/银证转账')
    目录校验(device, '二', '银证转账', '/页面标题')
    点击(device, '/交易/普通交易/普通交易_银证转账页面/转入')
    点击(device, '/交易/普通交易/银证转账/转入/从银行转入页面/查询银行卡余额')
    sleep(5)
    转入前银行卡余额 = 获取文本信息(device, '/交易/普通交易/银证转账/转入/从银行转入页面/查询银行卡余额')
    输入文本(device, '/交易/普通交易/银证转账/转入/从银行转入页面/请输入转入金额', 转入金额)
    点击(device, '/交易/普通交易/银证转账/转入/从银行转入页面/银行信息')
    点击(device, '/交易/普通交易/银证转账/转入/从银行转入页面/确定按钮')
    sleep(3)
    实际结果 = 获取文本信息(device, '/交易/普通交易/银证转账/转入/从银行转入页面/提示信息')
    结果 = 预期结果 in 实际结果
    if 结果:
        点击(device, '/交易/普通交易/银证转账/转入/从银行转入页面/完成按钮')
        # 转入完后验证银行余额
        # 返回()
        # 点击('/交易/普通交易/普通交易首页/普通交易')
        # 点击('/交易/普通交易/普通交易首页/银证转账')
        点击(device, '/交易/普通交易/普通交易_银证转账页面/转入')
        点击(device, '/交易/普通交易/银证转账/转入/从银行转入页面/查询银行卡余额')
        sleep(5)
        转入后银行卡余额 = 获取文本信息(device, '/交易/普通交易/银证转账/转入/从银行转入页面/查询银行卡余额')
        结果 = 转入后银行卡余额 < 转入前银行卡余额
        错误校验点 = '转入前银行余额为：%s，转入后银行卡余额为：%s' % (转入前银行卡余额, 转入后银行卡余额)
        if 结果:
            返回(device)
            点击(device, '/交易/普通交易/普通交易_银证转账页面/转出')
            try:
                获取文本信息(device, '/交易/普通交易/普通交易_银证转账/转出/选择银行卡/加载检查')
                print('选择第一张卡')
                点击(device, '/交易/普通交易/普通交易_银证转账/转出/选择银行卡/第一张卡')
            except:
                pass
            sleep(3)
            转出前可取金额 = 获取文本信息(device, '/交易/普通交易/银证转账/转出/转出至银行卡页面/当前可取金额')
            print('转出前可取金额：', 转出前可取金额)
            输入文本(device, '/交易/普通交易/银证转账/转出/转出至银行卡页面/请输入转入金额对话框', 转入金额)
            点击(device, '/交易/普通交易/银证转账/转出/转出至银行卡页面/确认转出按钮')
            点击(device, '/交易/普通交易/银证转账/转出/转出至银行卡页面/确认转出按钮')
            输入文本(device, '/交易/融资融券/银证转账/转出至银行页面/请输入六位资金密码', 资金密码)
            if 获取控件对象(device, '/底部确定/确定按钮') != '':
                点击(device, '/底部确定/确定按钮')
            sleep(2)  # 等待两秒，转出成功页面自动关闭
            try:
                获取文本信息(device, '/交易/普通交易/银证转账/转出/转出至银行卡页面/转出失败/加载检查')
            except:
                点击(device, '/交易/普通交易/普通交易_银证转账页面/转出')
                try:
                    获取文本信息(device, '/交易/普通交易/普通交易_银证转账/转出/选择银行卡/加载检查')
                except:
                    pass
                else:
                    print('选择第一张卡')
                    点击(device, '/交易/普通交易/普通交易_银证转账/转出/选择银行卡/第一张卡')
                转出后可取金额 = 获取文本信息(device, '/交易/普通交易/银证转账/转出/转出至银行卡页面/当前可取金额')
                print('转出后可取金额：', 转出后可取金额)
                错误校验点 = 非空校验(device, '转出:' + 转入金额, '转出:' + str(float(转出前可取金额) - float(转出后可取金额)))
                结果 = float(转出前可取金额) - float(转出后可取金额) == float(转入金额)
                print('结果：', 结果)
    else:
        详细信息 = 获取文本信息(device, '/交易/普通交易/银证转账/转入/从银行转入页面/详细信息')
        错误校验点 = 非空校验(device, 预期结果, 详细信息)
    return 结果, 错误校验点


@casescript('交易/普通交易/银证转账/转出')
def _52d6aaff29f273e9a243cdbc81f0b2a0(device, 账号='666630296771', 交易密码='124369', 通信密码='159753', 资金密码='124369', 转出金额='1'):
    错误校验点 = ''
    # 初始化
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/普通交易')
    点击(device, '/交易/普通交易/普通交易首页/银证转账')
    目录校验(device, '二', '银证转账', '/页面标题')
    点击(device, '/交易/普通交易/普通交易_银证转账页面/转出')
    try:
        获取文本信息(device, '/交易/普通交易/普通交易_银证转账/转出/选择银行卡/加载检查')
        print('选择第一张卡')
        点击(device, '/交易/普通交易/普通交易_银证转账/转出/选择银行卡/第一张卡')
    except:
        pass
    sleep(3)
    转出前可取金额 = 获取文本信息(device, '/交易/普通交易/银证转账/转出/转出至银行卡页面/当前可取金额')
    print('转出前可取金额：', 转出前可取金额)
    输入文本(device, '/交易/普通交易/银证转账/转出/转出至银行卡页面/请输入转入金额对话框', 转出金额)
    点击(device, '/交易/普通交易/银证转账/转出/转出至银行卡页面/确认转出按钮')
    点击(device, '/交易/普通交易/银证转账/转出/转出至银行卡页面/确认转出按钮')
    输入文本(device, '/交易/融资融券/银证转账/转出至银行页面/请输入六位资金密码', 资金密码)
    if 获取控件对象(device, '/底部确定/确定按钮') != '':
        点击(device, '/底部确定/确定按钮')
    sleep(2)  # 等待两秒，转出成功页面自动关闭
    try:
        获取文本信息(device, '/交易/普通交易/银证转账/转出/转出至银行卡页面/转出失败/加载检查')
    except:
        点击(device, '/交易/普通交易/普通交易_银证转账页面/转出')
        try:
            获取文本信息(device, '/交易/普通交易/普通交易_银证转账/转出/选择银行卡/加载检查')
        except:
            pass
        else:
            print('选择第一张卡')
            点击(device, '/交易/普通交易/普通交易_银证转账/转出/选择银行卡/第一张卡')
        转出后可取金额 = 获取文本信息(device, '/交易/普通交易/银证转账/转出/转出至银行卡页面/当前可取金额')
        print('转出后可取金额：', 转出后可取金额)
        错误校验点 = 非空校验(device, '转出:' + 转出金额, '转出:' + str(float(转出前可取金额) - float(转出后可取金额)))
        结果 = float(转出前可取金额) - float(转出后可取金额) == float(转出金额)
        print('结果：', 结果)
        #
        if 结果:
            返回(device)
            点击(device, '/交易/普通交易/普通交易_银证转账页面/转入')
            点击(device, '/交易/普通交易/银证转账/转入/从银行转入页面/查询银行卡余额')
            sleep(5)
            转入前银行卡余额 = 获取文本信息(device, '/交易/普通交易/银证转账/转入/从银行转入页面/查询银行卡余额')
            输入文本(device, '/交易/普通交易/银证转账/转入/从银行转入页面/请输入转入金额', 转出金额)
            点击(device, '/交易/普通交易/银证转账/转入/从银行转入页面/银行信息')
            点击(device, '/交易/普通交易/银证转账/转入/从银行转入页面/确定按钮')
            sleep(3)
            预期结果 = '成功'
            实际结果 = 获取文本信息(device, '/交易/普通交易/银证转账/转入/从银行转入页面/提示信息')
            结果 = 预期结果 in 实际结果
            if 结果:
                点击(device, '/交易/普通交易/银证转账/转入/从银行转入页面/完成按钮')
                点击(device, '/交易/普通交易/普通交易_银证转账页面/转入')
                点击(device, '/交易/普通交易/银证转账/转入/从银行转入页面/查询银行卡余额')
                sleep(5)
                转入后银行卡余额 = 获取文本信息(device, '/交易/普通交易/银证转账/转入/从银行转入页面/查询银行卡余额')
                结果 = 转入后银行卡余额 < 转入前银行卡余额
                错误校验点 = '转入前银行余额为：%s，转入后银行卡余额为：%s' % (转入前银行卡余额, 转入后银行卡余额)
            else:
                详细信息 = 获取文本信息(device, '/交易/普通交易/银证转账/转入/从银行转入页面/详细信息')
                错误校验点 = 非空校验(device, 预期结果, 详细信息)
                点击(device, '/交易/普通交易/银证转账/转入/从银行转入页面/完成按钮')
        return 结果, 错误校验点
    else:
        失败原因 = 获取文本信息(device, '/交易/普通交易/银证转账/转出/转出至银行卡页面/转出失败/失败原因')
        错误校验点 = 失败原因
        点击(device, '/交易/普通交易/银证转账/转出/转出至银行卡页面/转出失败/确定按钮')
        return False, 错误校验点


@casescript('交易/普通交易/银证转账/转账流水_今日明细')
def _6d7b62f1be4d0f6230025393ec34d4f7(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # 初始化
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/普通交易')
    点击(device, '/交易/普通交易/普通交易首页/银证转账')
    目录校验(device, '二', '银证转账', '/页面标题')
    点击(device, '/交易/普通交易/普通交易_银证转账页面/转账流水')
    实际结果 = ''
    try:
        点击(device, '/交易/普通交易/银证转账/转账流水/转账流水页面/第一条转账记录')
        实际结果 = 获取文本信息(device, '/交易/普通交易/银证转账/转账流水/转账明细/处理状态')
    except:
        实际结果 = 获取文本信息(device, '/交易/普通交易/银证转账/转账流水/转账明细/暂无记录')
    错误校验点 = 为空校验(device)
    结果 = 实际结果 != ''
    return 结果, 错误校验点


@casescript('交易/普通交易/银证转账/转账流水_历史明细')
def _f703c9048a4baf048528be5be84cbc01(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # hxf 生产账户
    # 初始化
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/普通交易')
    点击(device, '/交易/普通交易/普通交易首页/银证转账')
    目录校验(device, '二', '银证转账', '/页面标题')
    点击(device, '/交易/普通交易/普通交易_银证转账页面/转账流水')
    点击(device, '/交易/普通交易/银证转账/转账流水/转账流水页面/历史明细')
    预期结果 = ''
    实际结果 = ''
    try:
        点击(device, '/交易/普通交易/银证转账/转账流水/转账流水页面/第一条转账记录')
        预期结果 = '成功'
        实际结果 = 获取文本信息(device, '/交易/普通交易/银证转账/转账流水/转账明细/处理状态')
    except:
        预期结果 = '没有记录'
        实际结果 = 获取文本信息(device, '/交易/普通交易/银证转账/转账流水/转账明细/暂无记录')
    错误校验点 = 非空校验(device, 预期结果, 实际结果)
    结果 = 预期结果 in 实际结果
    return 结果, 错误校验点


@casescript('交易/普通交易/银证转账/银行余额')
def _c1591a84791e920e19fdccc5c036c8eb(device, 账号='666630296771', 交易密码='124369', 通信密码='159753', 资金密码='124369'):
    错误校验点 = ''
    # 真实账号
    # 账号 = '666621794831'
    # 交易密码 = '110119'
    # 通信密码 = 'rf110119'
    # 资金密码不知道，可能报错
    # hxf 生产账户
    # 初始化
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/普通交易')
    点击(device, '/交易/普通交易/普通交易首页/银证转账')
    目录校验(device, '二', '银证转账', '/页面标题')
    点击(device, '/交易/普通交易/普通交易_银证转账页面/银行余额')
    sleep(3)
    点击(device, '/交易/普通交易/银证转账/普通交易_银证转账_银行余额页面/选择银行卡')
    点击(device, '/交易/普通交易/银证转账/普通交易_银证转账_选择银行/银行列表')
    结果 = False
    if 获取控件对象(device, '/登陆/系统提示') != '':
        实际结果 = 获取文本信息(device, '/提示信息/消息内容')
        预期结果 = '银行非工作时间'
        结果 = 预期结果 in 实际结果
        错误校验点 = 非空校验(device, 预期结果, 实际结果)
    else:
        try:
            点击(device, '/交易/融资融券/银证转账/转出至银行页面/请输入六位资金密码')
        except:
            try:
                余额 = 获取文本信息(device, '/交易/普通交易/银证转账/银行余额页面/当前余额')
                print('余额：', 余额)
            except:
                余额 = 获取文本信息(device, '/交易/普通交易/银证转账/银行余额页面/查询中')
            错误校验点 = 数字校验(device, 余额)
            点击(device, '/交易/普通交易/银证转账/银行余额页面/完成')
            结果 = 余额 != '' and 余额 != '--'
        else:
            输入文本(device, '/交易/融资融券/银证转账/转出至银行页面/请输入六位资金密码', 资金密码)
            点击(device, '/交易/普通交易/普通交易_银证转账/资金调拨页面/确定')
            try:
                点击(device, '/交易/普通交易/买入页面/系统提示')
            except:
                余额 = 获取文本信息(device, '/交易/普通交易/银证转账/银行余额页面/当前余额')
                print('余额：', 余额)
                错误校验点 = 数字校验(device, 余额)
                点击(device, '/交易/普通交易/银证转账/银行余额页面/完成')
                结果 = float(余额) > 0
            else:
                错误校验点 = 获取文本信息(device, '/交易/普通交易/买入页面/系统提示')
                结果 = False
                点击(device, '/交易/普通交易/买入页面/买入确定按钮')
    return 结果, 错误校验点


