from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('行情/市场/更多/国内期货/中金所/获取中金所列表首只股指期货合约名称')
def _08a843f881da101bb582fcbf9666daf3(device):
    登录前页面处理(device)
    点击(device, '/底部导航栏/行情界面跳转按钮')
    目录校验(device, '一', '行情', '/行情/选中状态')
    点击(device, '/行情/市场/市场标题栏')
    控件左滑(device, '/行情/市场/沪深按钮标签')
    点击(device, '/行情/市场/更多/更多标签')
    目录校验(device, '二', '更多', '/行情/更多/选中状态')
    滑动控件至屏幕内(device, '/行情/市场/更多/国内期货/中金所/中金所标签')
    点击(device, '/行情/市场/更多/国内期货/中金所/中金所标签')
    获取中金所列表首只股指期货合约名称 = 获取文本信息(device, '/行情/市场/更多/国内期货/中金所/获取中金所列表首只股指期货合约名称')
    结果 = 获取中金所列表首只股指期货合约名称 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


