import os
import sys
import threading

import requests

from casescripts.public import *
from libs.adb import get_adb_devices, LocalDevice


def download_file(url, path=None):
    if path is None:
        local_filename = os.path.join('./log/', url.split('/')[-1])
    elif os.path.isdir(path):
        local_filename = os.path.join(path, url.split('/')[-1])
    else:
        local_filename = path
    if os.path.exists(local_filename):
        return local_filename
    # NOTE the stream=True parameter below
    with requests.get(url, stream=True) as r:
        r.raise_for_status()
        with open(local_filename, 'wb') as f:
            for chunk in r.iter_content(chunk_size=8192):
                if chunk:  # filter out keep-alive new chunks
                    f.write(chunk)
                    # f.flush()
    return local_filename


def is_install(device, app_id):
    d_id = device.device_id
    try:
        with open('log/package/%s.txt' % d_id) as f:
            all = f.read()
            return all.find(app_id) >= 0
    except:
        device.shell('pm list package|sort >log/package/%s.txt' % d_id)
        return is_install(device, app_id)


def install_url(device, url, app_id):
    path = download_file(url)
    if not is_install(device, app_id):
        try:
            device.adb_command('install %s' % path.replace('\\', '/'))
        except:
            pass


# 涨乐第一次启动需要用弱账号登录一次
def init_app(d, phone, code):
    device = LocalDevice(d)
    处理权限通知(device)
    for i in range(4):
        device.appium.slide_left(500, 0.8)

    点击(device, '/引导页/立刻开启')
    点击(device, '/底部导航栏/交易界面跳转按钮')
    点击(device, '/交易/普通交易/普通交易首页/登陆按钮')
    输入文本(device, '/登录/手机号码', phone)
    # 点击(device, '/登录/获取验证码')
    输入文本(device, '/登录/验证码', code)
    点击(device, '/登录/登录按钮')
    print('弱账号登录完成！')
    time.sleep(1000)


def install(d):
    print(d)
    device = LocalDevice(d)
    if not os.path.exists('log/package'):
        os.makedirs('log/package')

    urls_map = {
        'io.appium.settings': 'https://plmt-files.oss-cn-shanghai.aliyuncs.com/apk/io.appium.settings.apk',
        # 'com.mmbox.xbrowser': 'https://plmt-files.oss-cn-shanghai.aliyuncs.com/apk/xbrowser.apk',
        'io.appium.uiautomator2.server': 'https://plmt-files.oss-cn-shanghai.aliyuncs.com/apk/io.appium.uiautomator2.server.apk',
        'io.appium.uiautomator2.server.test': 'https://plmt-files.oss-cn-shanghai.aliyuncs.com/apk/io.appium.uiautomator2.server.test.apk',
        'io.appium.unlock': 'https://plmt-files.oss-cn-shanghai.aliyuncs.com/apk/Unlock_2.0.0.apk',
        'com.lphtsccft': 'https://plmt-files.oss-cn-shanghai.aliyuncs.com/apk/com.lphtsccft_6.5.1.apk',
        'com.zfkj.ybc.screenrecord': 'https://plmt-files.oss-cn-shanghai.aliyuncs.com/apk/screenrecord.apk',
    }
    disable_package = [
        'com.huawei.android.hwouc', 'com.huawei.appmarket', 'com.huawei.hifolder', 'com.vmall.client',
        'com.xunmeng.pinduoduo',
        'com.baidu.searchbox', 'com.baidu.haokan', 'com.baidu.BaiduMap', 'com.sina.weibo', 'com.smile.gifmaker',
        'com.huawei.fans', 'com.huawei.himovie',
        'com.jingdong.app.mall', 'com.UCMobile', 'com.sankuai.meituan', 'com.jifen.qukan', 'com.Qunar',
        'cn.wps.moffice_eng',
        'com.ss.android.ugc.aweme', 'com.ss.android.article.news', 'com.wuba', 'com.taobao.taobao', 'cn.honor.qinxuan',
        'com.tencent.news', 'com.eg.android.AlipayGphone', 'com.huawei.gamebox', 'com.huawei.android.tips',
        'com.huawei.hwvplayer.youku',
        'com.huawei.phoneservice', 'com.sina.weibolite', 'com.huawei.hwread.al',
    ]

    [device.shell('pm disable-user %s' % package) for package in disable_package]

    for k in urls_map:
        install_url(device, urls_map[k], k)

    device.shell('pm list package|sort >log/package/%s.txt' % d)


if __name__ == '__main__':
    argv = sys.argv
    print(argv)
    if len(argv) > 1:
        if argv[1] == 'init' and len(argv) > 3:
            phone = argv[2]
            code = argv[3]
            [threading.Thread(target=init_app, args=(d, phone, code)).start() for d in get_adb_devices()]
        if argv[1] == 'id':
            [threading.Thread(target=LocalDevice(d).shell, args=('ime set com.baidu.input_huawei/.ImeService',)).start() for d in get_adb_devices()]
            [threading.Thread(target=LocalDevice(d).shell, args=('am start -a android.intent.action.VIEW -d http://*************:8000/update-device/%s' % d,)).start() for d in get_adb_devices()]
    else:
        [threading.Thread(target=install, args=(d,)).start() for d in get_adb_devices()]
