from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('交易/融资融券/合约延期/合约延期')
def _32c2a2af5eac8cfc53075f5f10d89de5(device, 账号='30002187', 交易密码='123123', 通信密码='123123', 预期结果='目前没有可延期的合约'):
    登录前页面处理(device)
    目录校验(device, '一', '交易', '/交易/选中状态')
    交易登录(device, 账号, 交易密码, 通信密码)
    点击(device, '/交易/普通交易/普通交易首页/融资融券')
    点击(device, '/交易/融资融券/融资融券首页/合约延期按钮')
    目录校验(device, '二', '合约延期', '/页面标题')
    实际结果 = 获取文本信息(device, '/交易/融资融券/合约延期/合约延期页面/提示信息')
    print(实际结果)
    错误校验点 = 非空校验(device, 预期结果, 实际结果)
    结果 = 预期结果 in 实际结果
    return 结果, 错误校验点


