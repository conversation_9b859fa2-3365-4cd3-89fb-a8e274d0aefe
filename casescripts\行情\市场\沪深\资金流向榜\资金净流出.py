from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('行情/市场/沪深/资金流向榜/资金净流出/首只股票最新价')
def _a48d979ba939f10770fc32df1ab5efdd(device):
    登录前页面处理(device)
    点击(device, '/底部导航栏/行情界面跳转按钮')
    目录校验(device, '一', '行情', '/行情/选中状态')
    点击(device, '/行情/市场/市场标题栏')
    目录校验(device, '二', '沪深', '/行情/更多/选中状态')
    # 上滑(5000)
    滑动控件至屏幕内(device, '/行情/市场/沪深/资金流向榜/资金流向榜标签')
    点击(device, '/行情/市场/沪深/资金流向榜/资金流向榜标签')
    点击(device, '/行情/市场/沪深/资金流向榜_资金净流出/资金净流出标签')
    首只个股最新价 = 获取文本信息(device, '/行情/市场/沪深/资金流向榜_资金净流出/首只股票最新价')
    结果 = 首只个股最新价 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


