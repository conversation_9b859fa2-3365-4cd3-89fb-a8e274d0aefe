from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('我的/我的乐米/乐米可干啥/乐米可干啥')
def _979fb6fbf1870dc0ae8bbf8ec09ecb3f(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119', 预期结果='什么是乐米'):
    登录前页面处理(device)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    上滑(device)
    点击(device, '/底部导航栏/理财界面跳转按钮')
    点击(device, '/底部导航栏/我的界面跳转按钮')
    目录校验(device, '一', '我的', '/我的/选中状态')
    点击(device, '/我的/我的乐米')
    目录校验(device, '二', '我的乐米', '/我的/我的乐米/页面标题')
    上滑(device)
    sleep(3)
    点击(device, '/我的/我的乐米/乐米可干啥')
    实际结果 = 获取文本信息(device, '/我的/我的乐米/乐米可干啥/什么是乐米')
    错误校验点 = 非空校验(device, 预期结果, 实际结果)
    结果 = 预期结果 in 实际结果
    return 结果, 错误校验点


