usage: appium
       [-h] [-v] [--shell] [--allow-cors] [--reboot] [--ipa IPA] [-a ADDRESS]
       [-p PORT] [-ca CALLBACKADDRESS] [-cp CALLBACKPORT] [-bp BOOTSTRAPPORT]
       [-r BACKENDRETRIES] [--session-override] [-l] [-g LOGFILE]
       [--log-level {info,info:debug,info:info,info:warn,info:error,warn,warn:debug,warn:info,warn:warn,warn:error,error,error:debug,error:info,error:warn,error:error,debug,debug:debug,debug:info,debug:warn,debug:error}]
       [--log-timestamp] [--local-timezone] [--log-no-colors] [-G WEBHOOK]
       [--safari] [--default-device] [--force-iphone] [--force-ipad]
       [--tracetemplate AUTOMATIONTRACETEMPLATEPATH]
       [--instruments INSTRUMENTSPATH] [--nodeconfig NODECONFIG]
       [-ra ROBOTADDRESS] [-rp ROBOTPORT] [--selendroid-port <PERSON><PERSON><PERSON><PERSON>IDPORT]
       [--chromedriver-port CHROMEDRIVERPORT]
       [--chromedriver-executable CHROMEDRIVEREXECUTABLE] [--show-config]
       [--no-perms-check] [--strict-caps] [--isolate-sim-device]
       [--tmp TMPDIR] [--trace-dir TRACEDIR] [--debug-log-spacing]
       [--suppress-adb-kill-server] [--long-stacktrace]
       [--webkit-debug-proxy-port WEBKITDEBUGPROXYPORT]
       [--webdriveragent-port WDALOCALPORT] [-dc DEFAULTCAPABILITIES]
       [--relaxed-security] [--allow-insecure ALLOWINSECURE]
       [--deny-insecure DENYINSECURE]
       [--command-timeout DEFAULTCOMMANDTIMEOUT] [-k]
       [--platform-name PLATFORMNAME] [--platform-version PLATFORMVERSION]
       [--automation-name AUTOMATIONNAME] [--device-name DEVICENAME]
       [--browser-name BROWSERNAME] [--app APP] [-lt LAUNCHTIMEOUT]
       [--language LANGUAGE] [--locale LOCALE] [-U UDID]
       [--orientation ORIENTATION] [--no-reset] [--full-reset]
       [--app-pkg APPPACKAGE] [--app-activity APPACTIVITY]
       [--app-wait-package APPWAITPACKAGE]
       [--app-wait-activity APPWAITACTIVITY]
       [--device-ready-timeout DEVICEREADYTIMEOUT]
       [--android-coverage ANDROIDCOVERAGE] [--avd AVD] [--avd-args AVDARGS]
       [--use-keystore] [--keystore-path KEYSTOREPATH]
       [--keystore-password KEYSTOREPASSWORD] [--key-alias KEYALIAS]
       [--key-password KEYPASSWORD] [--intent-action INTENTACTION]
       [--intent-category INTENTCATEGORY] [--intent-flags INTENTFLAGS]
       [--intent-args OPTIONALINTENTARGUMENTS] [--dont-stop-app-on-reset]
       [--calendar-format CALENDARFORMAT] [--native-instruments-lib]
       [--keep-keychains] [--localizable-strings-dir LOCALIZABLESTRINGSDIR]
       [--show-ios-log] [--async-trace]
       

A webdriver-compatible server for use with native and hybrid iOS and Android 
applications.

Optional arguments:
  -h, --help            显示此帮助消息并退出。
  -v, --version         显示程序的版本号并退出。
  --shell               Enter REPL mode
  --allow-cors          Appium服务器是否应允许来自任何主机的Web浏览器连接
  --reboot              (Android-only) 每次会话后重启模拟器并在结束时将其终止
  --ipa IPA             (IOS-only) abs path to compiled .ipa file
  -a ADDRESS, --address ADDRESS
                        IP Address to listen on
  -p PORT, --port PORT  port to listen on
  -ca CALLBACKADDRESS, --callback-address CALLBACKADDRESS
                        callback IP Address (default: same as --address)
  -cp CALLBACKPORT, --callback-port CALLBACKPORT
                        callback port (default: same as port)
  -bp BOOTSTRAPPORT, --bootstrap-port BOOTSTRAPPORT
                        (Android-only) 设备上与Appium通信使用的端口
  -r BACKENDRETRIES, --backend-retries BACKENDRETRIES
                        (iOS-only) 重试启动次数
                        Instruments before saying it crashed or timed out
  --session-override    启用会话覆盖 (clobbering)
  -l, --pre-launch      Pre-launch the application before allowing the first 
                        session (Requires --app and, for Android, --app-pkg 
                        and --app-activity)
  -g LOGFILE, --log LOGFILE
                        Also send log output to this file
  --log-level {info,info:debug,info:info,info:warn,info:error,warn,warn:debug,warn:info,warn:warn,warn:error,error,error:debug,error:info,error:warn,error:error,debug,debug:debug,debug:info,debug:warn,debug:error}
                        log level; default (console[:file]): debug[:debug]
  --log-timestamp       Show timestamps in console output
  --local-timezone      Use local timezone for timestamps
  --log-no-colors       Do not use colors in console output
  -G WEBHOOK, --webhook WEBHOOK
                        Also send log output to this HTTP listener
  --safari              (IOS-Only) Use the safari app
  --default-device, -dd
                        (IOS-Simulator-only) use the default simulator that 
                        instruments launches on its own
  --force-iphone        (IOS-only) Use the iPhone Simulator no matter what 
                        the app wants
  --force-ipad          (IOS-only) Use the iPad Simulator no matter what the 
                        app wants
  --tracetemplate AUTOMATIONTRACETEMPLATEPATH
                        (IOS-only) .tracetemplate file to use with Instruments
  --instruments INSTRUMENTSPATH
                        (IOS-only) path to instruments binary
  --nodeconfig NODECONFIG
                        Configuration JSON file to register appium with 
                        selenium grid
  -ra ROBOTADDRESS, --robot-address ROBOTADDRESS
                        IP Address of robot
  -rp ROBOTPORT, --robot-port ROBOTPORT
                        port for robot
  --selendroid-port SELENDROIDPORT
                        Local port used for communication with Selendroid
  --chromedriver-port CHROMEDRIVERPORT
                        Port upon which ChromeDriver will run. If not given, 
                        Android driver will pick a random available port.
  --chromedriver-executable CHROMEDRIVEREXECUTABLE
                        ChromeDriver executable full path
  --show-config         Show info about the appium server configuration and 
                        exit
  --no-perms-check      Bypass Appium's checks to ensure we can read/write 
                        necessary files
  --strict-caps         Cause sessions to fail if desired caps are sent in 
                        that Appium does not recognize as valid for the 
                        selected device
  --isolate-sim-device  Xcode 6在某些平台上有一个错误，如果首先删除所有其他模拟器设备，
                        某些模拟器只能在没有错误的情况下启动。 此选项使Appium删除除
                        Appium使用的设备之外的所有设备。 请注意，这是永久删除，您负责
                        使用simctl或xcode管理与Appium一起使用的设备类别。
  --tmp TMPDIR          Absolute path to directory Appium can use to manage 
                        temporary files, like built-in iOS apps it needs to 
                        move around. On *nix/Mac defaults to /tmp, on Windows 
                        defaults to C:\Windows\Temp
  --trace-dir TRACEDIR  Absolute path to directory Appium use to save ios 
                        instruments traces, defaults to <tmp 
                        dir>/appium-instruments
  --debug-log-spacing   Add exaggerated spacing in logs to help with visual 
                        inspection
  --suppress-adb-kill-server
                        (Android-only) If set, prevents Appium from killing 
                        the adb server instance
  --long-stacktrace     Add long stack traces to log entries. Recommended for 
                        debugging only.
  --webkit-debug-proxy-port WEBKITDEBUGPROXYPORT
                        (IOS-only) Local port used for communication with 
                        ios-webkit-debug-proxy
  --webdriveragent-port WDALOCALPORT
                        (IOS-only, XCUITest-only) Local port used for 
                        communication with WebDriverAgent
  -dc DEFAULTCAPABILITIES, --default-capabilities DEFAULTCAPABILITIES
                        Set the default desired capabilities, which will be 
                        set on each session unless overridden by received 
                        capabilities.
  --relaxed-security    Disable additional security checks, so it is possible 
                        to use some advanced features, provided by drivers 
                        supporting this option. Only enable it if all the 
                        clients are in the trusted network and it's not the 
                        case if a client could potentially break out of the 
                        session sandbox. Specific features can be overridden 
                        by using the --deny-insecure flag
  --allow-insecure ALLOWINSECURE
                        Set which insecure features are allowed to run in 
                        this server's sessions. Features are defined on a 
                        driver level; see documentation for more details. 
                        This should be either a comma-separated list of 
                        feature names, or a path to a file where each feature 
                        name is on a line. Note that features defined via 
                        --deny-insecure will be disabled, even if also listed 
                        here.
  --deny-insecure DENYINSECURE
                        Set which insecure features are not allowed to run in 
                        this server's sessions. Features are defined on a 
                        driver level; see documentation for more details. 
                        This should be either a comma-separated list of 
                        feature names, or a path to a file where each feature 
                        name is on a line. Features listed here will not be 
                        enabled even if also listed in --allow-insecure, and 
                        even if --relaxed-security is turned on.
  --command-timeout DEFAULTCOMMANDTIMEOUT
                        [弃用] No effect. This used to be the default 
                        command timeout for the server to use for all 
                        sessions (in seconds and should be less than 2147483).
                         Use newCommandTimeout cap instead
  -k, --keep-artifacts  [弃用] - no effect, trace is now in tmp dir by 
                        default and is cleared before each run. Please also 
                        refer to the --trace-dir flag.
  --platform-name PLATFORMNAME
                        [弃用] - Name of the mobile platform: iOS, 
                        Android, or FirefoxOS
  --platform-version PLATFORMVERSION
                        [弃用] - Version of the mobile platform
  --automation-name AUTOMATIONNAME
                        [弃用] - Name of the automation tool: Appium or 
                        Selendroid
  --device-name DEVICENAME
                        [弃用] - Name of the mobile device to use
  --browser-name BROWSERNAME
                        [弃用] - Name of the mobile browser: Safari or 
                        Chrome
  --app APP             [弃用] - IOS: abs path to simulator-compiled .
                        app file or the bundle_id of the desired target on 
                        device; Android: abs path to .apk file
  -lt LAUNCHTIMEOUT, --launch-timeout LAUNCHTIMEOUT
                        [弃用] - (iOS-only) how long in ms to wait for 
                        Instruments to launch
  --language LANGUAGE   [弃用] - Language for the iOS simulator / 
                        Android Emulator
  --locale LOCALE       [弃用] - Locale for the iOS simulator / Android 
                        Emulator
  -U UDID, --udid UDID  [弃用] - 连接的物理设备的唯一设备标识符
  --orientation ORIENTATION
                        [弃用] - (IOS-only) use LANDSCAPE or PORTRAIT 
                        to initialize all requests to this orientation
  --no-reset            [弃用] - Do not reset app state between 
                        sessions (IOS: do not delete app plist files; 
                        Android: do not uninstall app before new session)
  --full-reset          [弃用] - (iOS) Delete the entire simulator 
                        folder. (Android) Reset app state by uninstalling app 
                        instead of clearing app data. On Android, this will 
                        also remove the app after the session is complete.
  --app-pkg APPPACKAGE  [弃用] - (Android-only) Java package of the 
                        Android app you want to run (e.g., com.example.
                        android.myApp)
  --app-activity APPACTIVITY
                        [弃用] - (Android-only) Activity name for the 
                        Android activity you want to launch from your package 
                        (e.g., MainActivity)
  --app-wait-package APPWAITPACKAGE
                        [弃用] - (Android-only) Package name for the 
                        Android activity you want to wait for (e.g., com.
                        example.android.myApp)
  --app-wait-activity APPWAITACTIVITY
                        [弃用] - (Android-only) Activity name for the 
                        Android activity you want to wait for (e.g., 
                        SplashActivity)
  --device-ready-timeout DEVICEREADYTIMEOUT
                        [弃用] - (Android-only) Timeout in seconds 
                        while waiting for device to become ready
  --android-coverage ANDROIDCOVERAGE
                        [弃用] - (Android-only) Fully qualified 
                        instrumentation class. Passed to -w in adb shell am 
                        instrument -e coverage true -w
  --avd AVD             [弃用] - (Android-only) Name of the avd to 
                        launch
  --avd-args AVDARGS    [弃用] - (Android-only) Additional emulator 
                        arguments to launch the avd
  --use-keystore        [弃用] - (Android-only) When set the keystore 
                        will be used to sign apks.
  --keystore-path KEYSTOREPATH
                        [弃用] - (Android-only) Path to keystore
  --keystore-password KEYSTOREPASSWORD
                        [弃用] - (Android-only) Password to keystore
  --key-alias KEYALIAS  [弃用] - (Android-only) Key alias
  --key-password KEYPASSWORD
                        [弃用] - (Android-only) Key password
  --intent-action INTENTACTION
                        [弃用] - (Android-only) Intent action which 
                        will be used to start activity
  --intent-category INTENTCATEGORY
                        [弃用] - (Android-only) Intent category which 
                        will be used to start activity
  --intent-flags INTENTFLAGS
                        [弃用] - (Android-only) Flags that will be used 
                        to start activity
  --intent-args OPTIONALINTENTARGUMENTS
                        [弃用] - (Android-only) Additional intent 
                        arguments that will be used to start activity
  --dont-stop-app-on-reset
                        [弃用] - (Android-only) When included, refrains 
                        from stopping the app before restart
  --calendar-format CALENDARFORMAT
                        [弃用] - (IOS-only) calendar format for the iOS 
                        simulator
  --native-instruments-lib
                        [弃用] - (IOS-only) IOS has a weird built-in 
                        unavoidable delay. We patch this in appium. If you do 
                        not want it patched, pass in this flag.
  --keep-keychains      [弃用] - (iOS-only) Whether to keep keychains 
                        (Library/Keychains) when reset app between sessions
  --localizable-strings-dir LOCALIZABLESTRINGSDIR
                        [弃用] - (IOS-only) the relative path of the 
                        dir where Localizable.strings file resides
  --show-ios-log        [弃用] - (IOS-only) if set, the iOS system log 
                        will be written to the console
  --async-trace         [弃用] - Add long stack traces to log entries. 
                        Recommended for debugging only.
