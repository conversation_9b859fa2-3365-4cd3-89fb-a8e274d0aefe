from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('理财/理财卡券/理财卡券/理财卡券')
def _69307ea041e79f4bae5ec6b32a26ac00(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # 初始化
    登录前页面处理(device)
    理财登陆(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '理财', '/理财/选中状态')
    # time.sleep(2)
    # 上滑()
    # time.sleep(2)
    # 上滑()
    # # 循环滑动( '/理财/快捷功能/华泰专属理财', '上')
    滑动控件至屏幕内_理财(device, '/理财/首发基金')
    控件左滑_理财(device, '/理财/首发基金')
    点击(device, '/理财/自定义添加')
    # 循环滑动('/理财/理财卡券', '上')
    上滑(device)
    上滑(device)
    device.appium.driver.background_app(3)
    点击(device, '/理财/理财卡券')
    目录校验(device, '二', '我的理财券', '/页面标题')
    实际结果 = 获取文本信息(device, '/理财/理财卡券/第一个卡券名称')
    print(实际结果)
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


