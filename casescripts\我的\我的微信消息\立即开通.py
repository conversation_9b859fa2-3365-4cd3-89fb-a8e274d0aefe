from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('我的/我的微信消息/立即开通/立即开通')
def _a34769e8a51762474b73e64d1d22585f(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    登录前页面处理(device)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    上滑(device)
    点击(device, '/底部导航栏/理财界面跳转按钮')
    点击(device, '/底部导航栏/我的界面跳转按钮')
    目录校验(device, '一', '我的', '/我的/选中状态')
    点击(device, '/我的/我的微信消息')
    目录校验(device, '二', '我的微信消息', '/页面标题')
    点击(device, '/我的/我的微信消息/详细流程')
    实际结果 = 获取控件对象(device, '/我的/我的微信消息/详细流程/详细')
    错误校验点 = 为空校验(device)
    结果 = 实际结果 != ''
    return 结果, 错误校验点


