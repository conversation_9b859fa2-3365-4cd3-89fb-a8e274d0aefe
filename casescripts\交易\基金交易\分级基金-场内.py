from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('交易/基金交易/分级基金-场内/买入')
def _c3d5867a0871f5345148eebe3ac9b55c(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119', 证券代码='502053', 交易数量='100', 预期结果='成功'):
    错误校验点 = ''
    # 证券代码 = '150152'
    # 账号 = '08026567'
    # 交易密码 = '123123'
    # 通信密码 = '123123'
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    上滑(device)
    sleep(5)
    点击(device, '/交易/普通交易/普通交易首页/基金交易')
    点击(device, '/交易/普通交易/普通交易首页/分级基金场内')
    目录校验(device, '二', '分级基金', '/页面标题')
    点击(device, '/交易/普通交易/基金交易/分级基金场内/分级基金页面/买入')
    输入文本(device, '/交易/普通交易/基金交易/分级基金场内/买入/基金买入页面/请输入股票代码框', 证券代码)
    try:
        点击(device, '/交易/普通交易/基金交易/分级基金场内/卖出/基金卖出页面/第一个基金')
    except:
        pass
    点击(device, '/交易/普通交易/买入页面/跌停价')
    最大可买卖数量1 = 获取文本信息(device, '/交易/普通交易/最大可买卖数')
    输入文本(device, '/交易/普通交易/基金交易/分级基金场内/买入/基金买入页面/买入数量', 交易数量)
    输入价格 = 获取文本信息(device, '/交易/普通交易/买入页面/买入价格输入框')
    点击(device, '/交易/普通交易/基金交易/分级基金场内/买入/基金买入页面/买入按钮')
    点击(device, '/交易/普通交易/基金交易/分级基金场内/买入/基金买入页面/确定按钮')
    实际结果 = 获取文本信息(device, '/交易/普通交易/更多/网络投票/投票查询/投票记录详细页面/提示框内容')
    print(实际结果)
    结果 = 预期结果 in 实际结果
    if 结果:
        委托合同号 = 截取合同号(device, 实际结果)
        点击(device, '/交易/普通交易/基金交易/分级基金场内/买入/基金买入页面/系统提示确认按钮')
        输入文本(device, '/交易/普通交易/基金交易/分级基金场内/买入/基金买入页面/请输入股票代码框', 证券代码)
        点击(device, '/交易/普通交易/买入页面/跌停价')
        最大可买卖数量2 = 获取文本信息(device, '/交易/普通交易/最大可买卖数')
        错误校验点 = '之前可买卖数量为%s,买卖后可买卖数量为%s' % (最大可买卖数量1, 最大可买卖数量2)
        结果 = 最大可买卖数量2<最大可买卖数量1
        if 结果:
            点击(device, '/交易/普通交易/交易页面/委托')
            sleep(3)
            点击(device, '/交易/基金交易/查询委托/当日委托/委托列表第一条记录的业务名称')
            while 结果:
                合同编号 = 获取文本信息(device, '/交易/普通交易/交易/委托/第一条记录/委托明细/合同编号')
                if 合同编号 != 委托合同号:
                    点击(device, '/交易/普通交易/交易/委托/第一条记录/委托明细/下一条')
                    try:
                        最后提示 = 获取文本信息(device, '/交易/普通交易/买入页面/系统提示')
                        print(最后提示)
                        结果 = False
                        错误校验点 = 非空校验(device, '合同编号：%s' % 委托合同号, '获取不到')
                        点击(device, '/提示确认/确认按钮')
                    except:
                        pass
                else:
                    交易代码 = 获取文本信息(device, '/交易/普通交易/交易/委托/第一条记录/委托明细/证券代码')
                    委托价格 = 获取文本信息(device, '/交易/普通交易/交易/委托/第一条记录/委托明细/委托价格')
                    委托数量 = 获取文本信息(device, '/交易/普通交易/交易/委托/第一条记录/委托明细/委托数量')
                    结果 = 交易代码 == 证券代码 and float(委托价格) == float(输入价格) and 委托数量 == 交易数量
                    print('输入的 代码：%s，价格：%s，数量：%s' % (证券代码, 输入价格, 交易数量),
                          '  获取到的 代码：%s，价格：%s，数量：%s' % (交易代码, 委托价格, 委托数量))
                    错误校验点 = 非空校验(device, '代码：%s，价格：%s，数量：%s' % (证券代码, 输入价格, 交易数量),
                                      '代码：%s，价格：%s，数量：%s' % (交易代码, 委托价格, 委托数量))
                    点击(device, '/交易/普通交易/撤单/撤单页面/撤单按钮')
                    点击(device, '/交易/普通交易/撤单/撤单页面/确定按钮')
                    break
        else:
            错误校验点 = 非空校验(device, 预期结果, 实际结果)
    return 结果, 错误校验点


@casescript('交易/基金交易/分级基金-场内/卖出')
def _975afa6a8790b1bacf82ba4adf64e085(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119', 证券代码='160217', 交易数量='100', 预期结果='委托提交成功'):
    错误校验点 = ''
    # time.sleep(15)
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    上滑(device)
    sleep(5)
    点击(device, '/交易/普通交易/普通交易首页/基金交易')
    点击(device, '/交易/普通交易/普通交易首页/分级基金场内')
    目录校验(device, '二', '分级基金', '/页面标题')
    点击(device, '/交易/普通交易/基金交易/分级基金场内/分级基金页面/卖出')
    输入文本(device, '/交易/普通交易/基金交易/分级基金场内/卖出/基金卖出页面/请输入股票代码框', 证券代码)
    # 点击('/交易/普通交易/基金交易/分级基金场内/卖出/基金卖出页面/第一个基金')
    输入价格 = 获取文本信息(device, '/交易/普通交易/卖出页面/涨停价')
    点击(device, '/交易/普通交易/卖出页面/涨停价')
    点击(device, '/交易/普通交易/卖出页面/#卖出数量输入框')
    最大可买卖数量1 = 获取文本信息(device, '/交易/普通交易/最大可买卖数')
    输入文本(device, '/交易/普通交易/卖出页面/#卖出数量输入框', 交易数量)
    点击(device, '/交易/普通交易/卖出页面/卖出按钮')
    点击(device, '/交易/普通交易/卖出页面/卖出确定按钮')
    实际结果 = 获取文本信息(device, '/交易/普通交易/卖出页面/系统提示')
    结果 = 预期结果 in 实际结果
    if not 结果:
        错误校验点 = 非空校验(device, 预期结果, 实际结果)
        点击(device, '/交易/普通交易/买入页面/委托确定按钮')
    else:
        # 切换到委托页面，查询第一条委托的证券代码是否等于预期代码
        委托合同号 = 截取合同号(device, 实际结果)
        点击(device, '/交易/普通交易/买入页面/委托确定按钮')
        输入文本(device, '/交易/普通交易/基金交易/分级基金场内/卖出/基金卖出页面/请输入股票代码框', 证券代码)
        点击(device, '/交易/普通交易/卖出页面/涨停价')
        最大可买卖数量2 = 获取文本信息(device, '/交易/普通交易/最大可买卖数')
        错误校验点 = '之前可买卖数量为%s,买卖后可买卖数量为%s' % (最大可买卖数量1, 最大可买卖数量2)
        结果 = 最大可买卖数量2 < 最大可买卖数量1
        if 结果:
            点击(device, '/交易/普通交易/交易页面/委托')
            sleep(3)
            点击(device, '/交易/基金交易/查询委托/当日委托/委托列表第一条记录的业务名称')
            while 结果:
                合同编号 = 获取文本信息(device, '/交易/普通交易/交易/委托/第一条记录/委托明细/合同编号')
                if 合同编号 != 委托合同号:
                    点击(device, '/交易/普通交易/交易/委托/第一条记录/委托明细/下一条')
                    try:
                        最后提示 = 获取文本信息(device, '/交易/普通交易/买入页面/系统提示')
                        print(最后提示)
                        结果 = False
                        错误校验点 = 非空校验(device, '合同编号：%s' % 委托合同号, '获取不到')
                        点击(device, '/提示确认/确认按钮')
                    except:
                        pass
                else:
                    交易代码 = 获取文本信息(device, '/交易/普通交易/交易/委托/第一条记录/委托明细/证券代码')
                    委托价格 = 获取文本信息(device, '/交易/普通交易/交易/委托/第一条记录/委托明细/委托价格')
                    委托数量 = 获取文本信息(device, '/交易/普通交易/交易/委托/第一条记录/委托明细/委托数量')
                    结果 = 交易代码 == 证券代码 and float(委托价格) == float(输入价格) and 委托数量 == 交易数量
                    错误校验点 = 非空校验(device, '代码：%s，价格：%s，数量：%s' % (证券代码, 输入价格, 交易数量),
                                      '代码：%s，价格：%s，数量：%s' % (交易代码, 委托价格, 委托数量))
                    点击(device, '/交易/普通交易/撤单/撤单页面/撤单按钮')
                    点击(device, '/交易/普通交易/撤单/撤单页面/确定按钮')
                    break
    return 结果, 错误校验点


@casescript('交易/基金交易/分级基金-场内/基金赎回')
def _1a020f3eb1d6d3549c6ed3b0256d6208(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119', 证券代码='160217', 交易数量='1', 预期结果='已提交'):
    错误校验点 = ''
    # time.sleep(15)
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    上滑(device)
    sleep(5)
    点击(device, '/交易/普通交易/普通交易首页/基金交易')
    点击(device, '/交易/普通交易/普通交易首页/分级基金场内')
    目录校验(device, '二', '分级基金', '/页面标题')
    点击(device, '/交易/普通交易/基金交易/分级基金场内/分级基金页面/基金赎回')
    输入文本(device, '/交易/普通交易/基金交易/分级基金场内/基金赎回/基金赎回页面/请选择需要赎回的基金框', 证券代码)
    输入文本(device, '/交易/普通交易/基金交易/分级基金场内/基金赎回/基金赎回页面/请输入赎回数量框', 交易数量)
    可用数量1 = 获取文本信息(device, '/交易/普通交易/基金交易/分级基金场内/基金赎回/可用数量')
    点击(device, '/交易/普通交易/基金交易/分级基金场内/基金赎回/基金赎回页面/确定按钮')
    点击(device, '/交易/普通交易/基金交易/分级基金场内/基金赎回/基金赎回页面/赎回确定')
    实际结果 = 获取文本信息(device, '/交易/普通交易/基金交易/分级基金场内/基金赎回/基金赎回页面/提示信息')
    print("实际结果为", 实际结果)
    错误校验点 = 非空校验(device, 预期结果, 实际结果)
    结果 = 预期结果 in 实际结果
    if 结果:
        # 返回()
        点击(device, '/交易/普通交易/基金交易/分级基金场内/认购申购/场内认购页面/提示信息_确定')
        输入文本(device, '/交易/普通交易/基金交易/分级基金场内/基金赎回/基金赎回页面/请选择需要赎回的基金框', 证券代码)
        输入文本(device, '/交易/普通交易/基金交易/分级基金场内/基金赎回/基金赎回页面/请输入赎回数量框', 交易数量)
        可用数量2 = 获取文本信息(device, '/交易/普通交易/基金交易/分级基金场内/基金赎回/可用数量')
        结果 = 可用数量2 < 可用数量1
        错误校验点 = '之前可买卖数量为%s,买卖后可买卖数量为%s' % (可用数量1, 可用数量2)
        if 结果:
            返回(device)
            返回(device)
            循环滑动(device, '/交易/普通交易/普通交易首页/撤单按钮', '下')
            点击(device, '/交易/普通交易/普通交易首页/撤单按钮')
            点击(device, '/交易/普通交易/撤单/撤单页面/第一条')
            while True:
                委托代码 = 获取文本信息(device, '/交易/普通交易/交易/委托/第一条记录/委托明细/证券代码')
                备注 = 获取文本信息(device, '/交易/普通交易/交易/委托/第一条记录/委托明细/备注')
                结果 = '申赎' in 备注 and 证券代码 == 委托代码
                print('委托代码:%s----------备注:%s-------------结果：%s' % (委托代码, 备注, 结果))
                if not 结果:
                    点击(device, '/交易/普通交易/交易/委托/第一条记录/委托明细/下一条')
                    try:
                        最后提示 = 获取文本信息(device, '/交易/普通交易/买入页面/系统提示')
                        print(最后提示)
                        # 结果 = False
                        错误校验点 = 非空校验(device, '申购：%s' % 证券代码, '获取不到')
                        点击(device, '/提示确认/确认按钮')
                    except:
                        pass
                else:
                    点击(device, '/交易/普通交易/撤单/撤单页面/撤单按钮')
                    点击(device, '/交易/普通交易/撤单/撤单页面/确定按钮')
                    break
    return 结果, 错误校验点


@casescript('交易/基金交易/分级基金-场内/子基金合并')
def _0d45349817aac16239d2e6e4f21316ec(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119', 证券代码='150239', 交易数量='100', 预期结果='成功'):
    # time.sleep(15)
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    上滑(device)
    sleep(5)
    点击(device, '/交易/普通交易/普通交易首页/基金交易')
    点击(device, '/交易/普通交易/普通交易首页/分级基金场内')
    目录校验(device, '二', '分级基金', '/页面标题')
    点击(device, '/交易/普通交易/基金交易/分级基金场内/分级基金页面/子基金合并')
    输入文本(device, '/交易/普通交易/基金交易/分级基金场内/子基金合并/基金合并页面/请输入合并的母基金代码框', 证券代码)
    输入文本(device, '/交易/普通交易/基金交易/分级基金场内/子基金合并/基金合并页面/合并数量', 交易数量)
    点击(device, '/交易/普通交易/基金交易/分级基金场内/子基金合并/基金合并页面/确认按钮')
    点击(device, '/交易/普通交易/基金交易/分级基金场内/子基金合并/基金合并页面/合并确认按钮')
    实际结果 = 获取文本信息(device, '/交易/普通交易/基金交易/分级基金场内/子基金合并/基金合并页面/提交页面信息')
    print("实际结果为", 实际结果)
    错误校验点 = 非空校验(device, 预期结果, 实际结果)
    点击(device, '/交易/普通交易/基金交易/分级基金场内/子基金合并/基金合并页面/提交页面信息确定')
    结果 = 预期结果 in 实际结果
    return 结果, 错误校验点


@casescript('交易/基金交易/分级基金-场内/我的分级基金持仓')
def _2a5b37436ee1fb802119f39e14286790(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    上滑(device)
    sleep(5)
    点击(device, '/交易/普通交易/普通交易首页/基金交易')
    点击(device, '/交易/普通交易/普通交易首页/分级基金场内')
    目录校验(device, '二', '分级基金', '/页面标题')
    点击(device, '/交易/普通交易/基金交易/分级基金场内/分级基金页面/我的分级持仓')
    try:
        实际结果 = 获取文本信息(device, '/交易/普通交易/基金交易/分级基金场内/分级基金页面/我的分级持仓/第一条记录')
    except:
        实际结果 = 获取文本信息(device, '/交易/普通交易/基金交易/分级基金场内/分级基金页面/我的分级持仓/无记录')
    错误校验点 = 为空校验(device)
    结果 = 实际结果 != ''
    return 结果, 错误校验点


@casescript('交易/基金交易/分级基金-场内/撤单')
def _58cf3a95bb705c6ab9bf8214595c1160(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119', 证券代码='600123', 基金代码='10234', 交易数量='100', 买卖价格='6.25', 联系人='123', 联系方式='178'):
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    上滑(device)
    sleep(5)
    点击(device, '/交易/普通交易/普通交易首页/基金交易')
    点击(device, '/交易/普通交易/普通交易首页/分级基金场内')
    目录校验(device, '二', '分级基金', '/页面标题')
    点击(device, '/交易/普通交易/基金交易/分级基金场内/分级基金页面/撤单')
    实际结果 = 获取文本信息(device, '/交易/普通交易/基金交易/分级基金场内/分级基金页面/撤单/无记录')
    错误校验点 = 为空校验(device)
    结果 = 实际结果 != ''
    return 结果, 错误校验点


@casescript('交易/基金交易/分级基金-场内/查询历史交割')
def _db3a1d09caaeaee63256ce3d9b519eef(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    错误校验点 = ''
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    上滑(device)
    sleep(5)
    点击(device, '/交易/普通交易/普通交易首页/基金交易')
    点击(device, '/交易/普通交易/普通交易首页/分级基金场内')
    目录校验(device, '二', '分级基金', '/页面标题')
    点击(device, '/交易/普通交易/基金交易/分级基金场内/分级基金页面/查询历史交割')
    点击(device, '/交易/普通交易/基金交易/分级基金场内/分级基金页面/查询历史交割/查询按钮')
    结果 = False
    if 获取控件对象(device, '/系统提示'):
        实际结果 = 获取文本信息(device, '/系统提示')
        预期结果 = '该功能禁止在目前系统状态下运行'
        结果 = 预期结果 in 实际结果
        错误校验点 = 非空校验(device, 预期结果, 实际结果)
    else:
        try:
            实际结果 = 获取文本信息(device, '/交易/普通交易/基金交易/分级基金场内/分级基金页面/查询历史交割/第一条记录')
            错误校验点 = 为空校验(device)
            结果 = 实际结果 != ''
        except:
            实际结果 = 获取控件对象(device, '/交易/普通交易/基金交易/分级基金场内/分级基金页面/查询历史交割/无记录')
            错误校验点 = 为空校验(device)
            结果 = 实际结果 != ''
    return 结果, 错误校验点


@casescript('交易/基金交易/分级基金-场内/查询当日委托')
def _76e33158d0f9b12bd9d7281d70c2893f(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    错误校验点 = ''
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    上滑(device)
    sleep(5)
    点击(device, '/交易/普通交易/普通交易首页/基金交易')
    点击(device, '/交易/普通交易/普通交易首页/分级基金场内')
    目录校验(device, '二', '分级基金', '/页面标题')
    点击(device, '/交易/普通交易/基金交易/分级基金场内/分级基金页面/查询当日委托')
    结果 = False
    if 获取控件对象(device, '/系统提示'):
        实际结果 = 获取文本信息(device, '/系统提示')
        预期结果 = '该功能禁止在目前系统状态下运行'
        结果 = 预期结果 in 实际结果
        错误校验点 = 非空校验(device, 预期结果, 实际结果)
    else:
        实际结果 = 获取控件对象(device, '/交易/普通交易/基金交易/分级基金场内/分级基金页面/查询当日委托/无记录')
        错误校验点 = 为空校验(device)
        结果 = 实际结果 != ''
    return 结果, 错误校验点


@casescript('交易/基金交易/分级基金-场内/查询当日成交')
def _8f2dc9738554d7dd9ababdfc81872c64(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    错误校验点 = ''
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    上滑(device)
    sleep(5)
    点击(device, '/交易/普通交易/普通交易首页/基金交易')
    点击(device, '/交易/普通交易/普通交易首页/分级基金场内')
    目录校验(device, '二', '分级基金', '/页面标题')
    点击(device, '/交易/普通交易/基金交易/分级基金场内/分级基金页面/查询当日成交')
    结果 = False
    if 获取控件对象(device, '/系统提示'):
        实际结果 = 获取文本信息(device, '/系统提示')
        预期结果 = '该功能禁止在目前系统状态下运行'
        结果 = 预期结果 in 实际结果
        错误校验点 = 非空校验(device, 预期结果, 实际结果)
    else:
        try:
            实际结果 = 获取文本信息(device, '/交易/普通交易/基金交易/分级基金场内/分级基金页面/查询当日成交/第一条记录')
        except:
            实际结果 = 获取控件对象(device, '/交易/普通交易/基金交易/分级基金场内/分级基金页面/查询当日成交/无记录')
        错误校验点 = 为空校验(device)
        结果 = 实际结果 != ''
    return 结果, 错误校验点


@casescript('交易/基金交易/分级基金-场内/母基金分拆')
def _76ede5e3b2cd07ed7618cbb52b0982fe(device, 账号='07072328', 交易密码='123123', 通信密码='123123', 证券代码='167601', 交易数量='100', 预期结果='成功'):
    # 账号 = '666621794831'
    # 交易密码 = '110119'
    # 通信密码 = 'rf110119'
    # 证券代码 = '150239'
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    上滑(device)
    sleep(5)
    点击(device, '/交易/普通交易/普通交易首页/基金交易')
    点击(device, '/交易/普通交易/普通交易首页/分级基金场内')
    目录校验(device, '二', '分级基金', '/页面标题')
    点击(device, '/交易/普通交易/基金交易/分级基金场内/分级基金页面/母基金分拆')
    输入文本(device, '/交易/普通交易/基金交易/分级基金场内/母基金分拆/基金分拆页面/请输入基金代码框', 证券代码)
    输入文本(device, '/交易/普通交易/基金交易/分级基金场内/母基金分拆/基金分拆页面/请输入拆分数量代码框', 交易数量)
    点击(device, '/交易/普通交易/基金交易/分级基金场内/母基金分拆/基金分拆页面/确定按钮')
    点击(device, '/交易/普通交易/基金交易/分级基金场内/母基金分拆/基金分拆页面/再次确定')
    点击(device, '/交易/普通交易/基金交易/分级基金场内/母基金分拆/基金分拆页面/拆分基金确认按钮')
    实际结果 = 获取文本信息(device, '/交易/普通交易/基金交易/分级基金场内/母基金分拆/基金分拆页面/提交页面信息')
    错误校验点 = 非空校验(device, 预期结果, 实际结果)
    点击(device, '/交易/普通交易/基金交易/分级基金场内/母基金分拆/基金分拆页面/提交页面信息确定')
    结果 = 预期结果 in 实际结果
    return 结果, 错误校验点


@casescript('交易/基金交易/分级基金-场内/认购申购')
def _6c8fdeaf3d8f4686e6e7395ed8f24fea(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119', 证券代码='160217', 交易数量='100', 购买金额='1000', 预期结果='委托已提交'):
    错误校验点 = ''
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    上滑(device)
    sleep(5)
    点击(device, '/交易/普通交易/普通交易首页/基金交易')
    点击(device, '/交易/普通交易/普通交易首页/分级基金场内')
    目录校验(device, '二', '分级基金', '/页面标题')
    # 点击('/交易/普通交易/普通交易首页/分级基金场内')
    点击(device, '/交易/普通交易/基金交易/分级基金场内/分级基金页面/认购申购')
    输入文本(device, '/交易/普通交易/基金交易/分级基金场内/认购申购/场内认购页面/请输入基金代码', 证券代码)
    输入文本(device, '/交易/普通交易/基金交易/分级基金场内/认购申购/场内认购页面/请输入购买金额', 购买金额)
    可用资金1 = 获取文本信息(device, '/交易/基金交易/分级基金场内/认购申购/场内认购页面/可用资金')
    点击(device, '/交易/普通交易/基金交易/分级基金场内/认购申购/场内认购页面/确定按钮')
    点击(device, '/交易/普通交易/基金交易/分级基金场内/认购申购/场内认购页面/场内基金认购确定')
    实际结果 = 获取文本信息(device, '/交易/普通交易/基金交易/分级基金场内/认购申购/场内认购页面/提示信息')
    print("实际结果为", 实际结果)
    错误校验点 = 非空校验(device, 预期结果, 实际结果)
    结果 = 预期结果 in 实际结果
    点击(device, '/交易/普通交易/基金交易/分级基金场内/认购申购/场内认购页面/提示信息_确定')
    if 结果:
        # 返回()
        输入文本(device, '/交易/普通交易/基金交易/分级基金场内/认购申购/场内认购页面/请输入基金代码', 证券代码)
        可用资金2 = 获取文本信息(device, '/交易/基金交易/分级基金场内/认购申购/场内认购页面/可用资金')
        结果 = 可用资金2 < 可用资金1
        错误校验点 = '之前可买卖数量为%s,买卖后可买卖数量为%s' % (可用资金1, 可用资金2)
        if 结果:
            返回(device)
            返回(device)
            循环滑动(device, '/交易/普通交易/普通交易首页/撤单按钮', '下')
            点击(device, '/交易/普通交易/普通交易首页/撤单按钮')
            点击(device, '/交易/普通交易/撤单/撤单页面/第一条')
            while True:
                委托代码 = 获取文本信息(device, '/交易/普通交易/交易/委托/第一条记录/委托明细/证券代码')
                备注 = 获取文本信息(device, '/交易/普通交易/交易/委托/第一条记录/委托明细/备注')
                结果 = '申赎' in 备注 and 证券代码 == 委托代码
                print('委托代码:%s----------备注:%s-------------结果：%s' %(委托代码, 备注, 结果))
                if not 结果:
                    点击(device, '/交易/普通交易/交易/委托/第一条记录/委托明细/下一条')
                    try:
                        最后提示 = 获取文本信息(device, '/交易/普通交易/买入页面/系统提示')
                        print(最后提示)
                        # 结果 = False
                        错误校验点 = 非空校验(device, '申购：%s' % 证券代码, '获取不到')
                        点击(device, '/提示确认/确认按钮')
                    except:
                        pass
                else:
                    点击(device, '/交易/普通交易/撤单/撤单页面/撤单按钮')
                    点击(device, '/交易/普通交易/撤单/撤单页面/确定按钮')
                    break
    return 结果, 错误校验点


