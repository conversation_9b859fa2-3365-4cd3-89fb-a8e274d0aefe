from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('行情/市场/港股/热门AH股/跳转单券页面/获取单券页面的价格')
def _058413cb7a7c9bc0b5a78b483f07ac4f(device):
    登录前页面处理(device)
    点击(device, '/底部导航栏/行情界面跳转按钮')
    目录校验(device, '一', '行情', '/行情/选中状态')
    点击(device, '/行情/市场/市场标题栏')
    点击(device, '/行情/市场/港股/港股标签')
    目录校验(device, '二', '港股', '/行情/更多/选中状态')
    滑动控件至屏幕内(device, '/行情/市场/港股/热门AH股/AH股/AH股标签')
    sleep(2)
    点击(device, '/行情/市场/港股/热门AH股/AH股/AH股标签')
    sleep(3)
    点击(device, '/行情/市场/港股/热门AH股/AH股/首只AH股名称')
    获取单券页面的价格 = 获取文本信息(device, '/行情/市场/港股/热门AH股/跳转单券页面/获取单券页面的价格')
    结果 = 获取单券页面的价格 != '' and 获取单券页面的价格 != '--'
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


