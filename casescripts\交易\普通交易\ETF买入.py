from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('交易/普通交易/ETF买入/ETF买入')
def _ab3b8ba541b09fd2c41c93c96c2dbfe3(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119', 证券代码='510050', 预期结果='委托提交成功'):
    错误校验点 = ''
    # 证券代码 = '162411'
    # 交易数量 = '100'
    # 初始化
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/普通交易')
    点击(device, '/交易/普通交易/普通交易首页/买入按钮')
    目录校验(device, '二', '买入', '/交易/普通交易/买入/选中状态')
    输入文本(device, '/交易/普通交易/买入页面/请输入股票代码框', 证券代码)
    点击(device, '/交易/普通交易/买入页面/跌停价')
    输入价格 = 获取文本信息(device, '/交易/普通交易/买入页面/买入价格输入框')
    # 比较可买数量与最低一手数量
    最大可买卖数量1 = 获取文本信息(device, '/交易/普通交易/最大可买卖数')
    # 一手数量 = 获取文本信息('/交易/普通交易/增加数量')
    # 截屏()
    # # 结果 = False
    # # 如果可买数量小于最低数量，则先进行撤单操作
    # if 最大可买卖数量1 < 一手数量:
    #     点击('/交易/普通交易/交易页面/撤单')
    #     time.sleep(3)
    #     # 判断有无撤单
    #     未撤 = 获取控件对象('/交易/普通交易/撤单/撤单页面/第一条') != ''
    #     点击('/交易/普通交易/撤单/撤单页面/第一条')
    #     while 未撤:
    #         买卖方向 = 获取文本信息('/交易/普通交易/撤单/撤单委托明细页面/买卖方向')
    #         委托代码 = 获取文本信息('/交易/普通交易/撤单/撤单委托明细页面/证券代码')
    #         if '买入' == 买卖方向 and 证券代码 == 委托代码:
    #             if 获取控件对象('/交易/普通交易/撤单/撤单页面/撤单按钮') != '':
    #                 点击('/交易/普通交易/撤单/撤单页面/撤单按钮')
    #                 点击('/交易/普通交易/撤单/撤单页面/确定按钮')
    #                 预期结果 = '提交'
    #                 实际结果 = 获取文本信息('/交易/普通交易/买入页面/系统提示')
    #                 结果 = 预期结果 in 实际结果
    #                 if not 结果:
    #                     全局变量.错误校验点 = 非空校验(预期结果, 实际结果)
    #                     print('撤单失败！')
    #                     break
    #                 # 等待两秒，提示自动关闭
    #                 time.sleep(2)
    #                 # 点击('/提示确认/确认按钮')
    #                 未撤 = 获取控件对象('/交易/普通交易/撤单/撤单页面/第一条') != ''
    #                 print('未撤：：', 未撤)
    #                 if 未撤:
    #                     点击('/交易/普通交易/撤单/撤单页面/第一条')
    #                 else:
    #                     print('全部撤完')
    #                     break
    #         else:
    #             点击('/交易/普通交易/交易/委托/第一条记录/委托明细/下一条')
    #             try:
    #                 最后提示 = 获取文本信息('/交易/普通交易/买入页面/系统提示')
    #                 未撤 = '最后一条' not in 最后提示
    #                 点击('/提示确认/确认按钮')
    #                 返回()
    #             except:
    #                 pass
    #     print('无撤单！！')
    #
    # 点击('/交易/普通交易/交易页面/买入')
    # 点击('/交易/普通交易/买入页面/跌停价')
    点击(device, '/交易/普通交易/增加数量')
    交易数量 = 获取文本信息(device, '/交易/普通交易/买入页面/买入数量输入框')
    # 最大可买卖数量1 = 获取文本信息('/交易/普通交易/最大可买卖数')
    点击(device, '/交易/普通交易/买入页面/买入按钮')
    点击(device, '/交易/普通交易/买入页面/买入确定按钮')
    实际结果 = 获取文本信息(device, '/交易/普通交易/买入页面/系统提示')
    结果 = 预期结果 in 实际结果
    # if 结果:
    #     点击('/交易/普通交易/买入页面/委托确定按钮')
    #     输入文本('/交易/普通交易/买入页面/请输入股票代码框', 证券代码)
    #     点击('/交易/普通交易/买入页面/跌停价')
    #     最大可买卖数量2 = 获取文本信息('/交易/普通交易/最大可买卖数')
    #     全局变量.错误校验点 = '之前可买卖数量为%s,买卖后可买卖数量为%s' % (最大可买卖数量1, 最大可买卖数量2)
    #     截屏()
    #     结果 = 最大可买卖数量2 < 最大可买卖数量1
    # else:
    #     全局变量.错误校验点 = 非空校验(预期结果, 实际结果)
    #     点击('/交易/普通交易/买入页面/委托确定按钮')
    # print(结果)
    if not 结果:
        错误校验点 = 非空校验(device, 预期结果, 实际结果)
        点击(device, '/交易/普通交易/买入页面/委托确定按钮')
    else:
        # 切换到委托页面，进入第一条委托页面，查看合同编号是否等于交易成功的合同号，
        # 如果相等则取证券代码、价格、数量校验并撤单，不等则点击下一条直到最后一条
        委托合同号 = 截取合同号(device, 实际结果)
        # print('合同号:', 委托合同号)
        点击(device, '/交易/普通交易/买入页面/委托确定按钮')
        # 验资
        输入文本(device, '/交易/普通交易/买入页面/请输入股票代码框', 证券代码)
        点击(device, '/交易/普通交易/买入页面/跌停价')
        最大可买卖数量2 = 获取文本信息(device, '/交易/普通交易/最大可买卖数')
        错误校验点 = '之前可买卖数量为%s,买卖后可买卖数量为%s' % (最大可买卖数量1, 最大可买卖数量2)
        print('之前可买卖数量为%s,买卖后可买卖数量为%s' % (最大可买卖数量1, 最大可买卖数量2))
        截屏(device)
        结果 = 最大可买卖数量2 < 最大可买卖数量1
        # 撤单
        if 结果:
            点击(device, '/交易/普通交易/交易页面/委托')
            sleep(3)
            点击(device, '/交易/基金交易/查询委托/当日委托/委托列表第一条记录的业务名称')
            while 结果:
                合同编号 = 获取文本信息(device, '/交易/普通交易/交易/委托/第一条记录/委托明细/合同编号')
                if 合同编号 != 委托合同号:
                    点击(device, '/交易/普通交易/交易/委托/第一条记录/委托明细/下一条')
                    try:
                        最后提示 = 获取文本信息(device, '/交易/普通交易/买入页面/系统提示')
                        print(最后提示)
                        结果 = False
                        错误校验点 = 非空校验(device, '合同编号：%s' % 委托合同号, '获取不到')
                        点击(device, '/提示确认/确认按钮')
                    except:
                        pass
                else:
                    交易代码 = 获取文本信息(device, '/交易/普通交易/交易/委托/第一条记录/委托明细/证券代码')
                    委托价格 = 获取文本信息(device, '/交易/普通交易/交易/委托/第一条记录/委托明细/委托价格')
                    委托数量 = 获取文本信息(device, '/交易/普通交易/交易/委托/第一条记录/委托明细/委托数量')
                    结果 = 交易代码 == 证券代码 and float(委托价格) == float(输入价格) and 委托数量 == 交易数量
                    print('输入的 代码：%s，价格：%s，数量：%s' % (证券代码, 输入价格, 交易数量),
                          '  获取到的 代码：%s，价格：%s，数量：%s' % (交易代码, 委托价格, 委托数量))
                    错误校验点 = 非空校验(device, '代码：%s，价格：%s，数量：%s' % (证券代码, 输入价格, 交易数量),
                                      '代码：%s，价格：%s，数量：%s' % (交易代码, 委托价格, 委托数量))
                    点击(device, '/交易/普通交易/撤单/撤单页面/撤单按钮')
                    点击(device, '/交易/普通交易/撤单/撤单页面/确定按钮')
                    break
    return 结果, 错误校验点


