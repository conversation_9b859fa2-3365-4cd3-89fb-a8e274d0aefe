from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('行情/市场/更多/港股/港股通/获取首只港股通股票名称')
def _1b092681288fcdc0008ad0b42976ef2b(device):
    登录前页面处理(device)
    print(device.appium.driver)
    点击(device, '/底部导航栏/行情界面跳转按钮')
    点击(device, '/行情/市场/市场标题栏')
    控件左滑(device, '/行情/市场/沪深按钮标签')
    点击(device, '/行情/市场/更多/更多标签')
    # 滑动控件至屏幕内('/行情/市场/更多/港股/港股通涨幅榜/港股通标签')
    滑动控件至屏幕内(device, '/行情/市场/更多/港股/港股通/港股通标签')
    点击(device, '/行情/市场/更多/港股/港股通/港股通标签')
    首只港股通股票名称 = 获取文本信息(device, '/行情/市场/更多/港股/港股通/首只港股通股票名称')
    结果 = 首只港股通股票名称 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


