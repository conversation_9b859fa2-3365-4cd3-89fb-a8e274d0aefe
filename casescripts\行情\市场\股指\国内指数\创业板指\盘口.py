from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('行情/市场/股指/国内指数/创业板指/盘口/创业板指盘口上涨家数')
def _f86d7920302342449534a317aa9ee95b(device):
    登录前页面处理(device)
    print(device.appium.driver)
    点击(device, '/底部导航栏/行情界面跳转按钮')
    目录校验(device, '一', '行情', '/行情/选中状态')
    点击(device, '/行情/市场/市场标题栏')
    点击(device, '/行情/市场/股指/股指标签')
    目录校验(device, '二', '股指', '/行情/更多/选中状态')
    点击(device, '/行情/市场/股指/国内指数/指数成分列表/创业板指')
    点击(device, '/股指/国内指数/创业板指/盘口/盘口标签')
    上滑(device)
    上涨家数 = 获取文本信息(device, '/股指/国内指数/创业板指/盘口/上涨家数')
    结果 = 上涨家数 != '' or 上涨家数 != None
    错误校验点 = '上涨家数数据丢失'
    return 结果, 错误校验点


