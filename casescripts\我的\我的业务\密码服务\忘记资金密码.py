from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('我的/我的业务/密码服务/忘记资金密码/忘记资金密码页面检查')
def _370d186b09122bd8913007a7f9633042(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # 初始化
    登录前页面处理(device)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    点击(device, '/我的/我的业务')
    点击(device, '/底部导航栏/理财界面跳转按钮')
    点击(device, '/底部导航栏/我的界面跳转按钮')
    点击(device, '/我的/我的业务/忘记资金密码')
    登录(device, 账号, 交易密码, 通信密码)
    source = device.appium.driver.page_source
    print("source::", source)
    sleep(3)
    if "业务办理须知" in source:
        点击(device, '/我的/我的业务/忘记资金密码/业务办理须知复选框')
        点击(device, '/我的/我的业务/忘记资金密码/业务办理须知确定按钮')
    raise Exception("用例未完成")
    # time.sleep(50)
    # 点击('/理财/90天以下')
    # 实际结果 = 获取文本信息('/理财/90天以下/第一款产品名称')
    # print(实际结果)
    # if 实际结果:
    #     结果 = True
    # else:
    #     结果 = False
    # 全局变量.错误校验点 = 为空校验()
    # 结果校验(结果)


