from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('首页/热点/打新股/跳转到《打新股》页面')
def _8a67f27995452f4565d2909a687b2de7(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # 初始化
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    点击(device, '/底部导航栏/首页界面跳转按钮')
    目录校验(device, '一', '首页', '/首页/选中状态')
    点击(device, '/首页/热点')
    目录校验(device, '二', '热点', '/首页/热点/选中状态')
    点击(device, '/首页/热点/打新股')
    sleep(2)
    # 交易登录(账号, 交易密码, 通信密码)
    实际结果 = 获取文本信息(device, '/首页/热点/打新股/判断')
    结果 = 实际结果 == '打新神器'
    错误校验点 = 非空校验(device, '打新神器', 实际结果)
    return 结果, 错误校验点


