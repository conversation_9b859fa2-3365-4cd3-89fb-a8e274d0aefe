from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('交易/债转股/债转股/债转股')
def _0612756387ea30968ca7971852ccf4e3(device, 账号='01028889', 交易密码='123123', 通信密码='123123', 证券代码='191008', 预期结果='成功'):
    # 交易数量 = 1
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    上滑(device)
    sleep(1)
    点击(device, '/交易/普通交易/普通交易首页/其他交易')
    目录校验(device, '二', '其他交易', '/页面标题')
    点击(device, '/交易/普通交易/其他交易/其他交易首页/债转股')
    输入文本(device, '/交易/普通交易/其他交易/债转股页面/转股代码输入框', 证券代码)
    点击(device, '/系统/加数量')
    点击(device, '/交易/普通交易/其他交易/债转股页面/确定按钮')
    点击(device, '/交易/普通交易/其他交易/债转股页面/委托确认确定按钮')
    实际结果 = 获取文本信息(device, '/交易/普通交易/其他交易/债转股页面/温馨提示')
    print(实际结果)
    错误校验点 = 非空校验(device, 预期结果, 实际结果)
    点击(device, '/交易/普通交易/其他交易/债转股页面/温馨提示确定')
    结果 = 预期结果 in 实际结果
    return 结果, 错误校验点


