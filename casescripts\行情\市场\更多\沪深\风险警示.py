from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('行情/市场/更多/沪深/风险警示/首只风险警示股名称')
def _929beaf63c2a06c83fe5565697099d70(device):
    登录前页面处理(device)
    print(device.appium.driver)
    点击(device, '/底部导航栏/行情界面跳转按钮')
    点击(device, '/行情/市场/市场标题栏')
    控件左滑(device, '/行情/市场/沪深按钮标签')
    点击(device, '/行情/市场/更多/更多标签')
    点击(device, '/行情/市场/更多/沪深/风险警示/风险警示标签')
    首只风险警示股名称 = 获取文本信息(device, '/行情/市场/更多/沪深/风险警示/首只风险警示股名称')
    结果 = 首只风险警示股名称 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


