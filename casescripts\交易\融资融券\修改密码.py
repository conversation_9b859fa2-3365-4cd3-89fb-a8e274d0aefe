from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('交易/融资融券/修改密码/修改密码')
def _e5573cb3bcadd1690bdb8092a84ba378(device, 账号='30002187', 交易密码='123123', 通信密码='123123', 第一次预期结果='成功'):
    错误校验点 = ''
    旧信用资金密码 = int('123123')
    新信用资金密码 = int('123321')
    # 初始化
    登录前页面处理(device)
    sleep(3)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/融资融券')
    sleep(2)
    上滑(device)
    点击(device, '/底部导航栏/首页界面跳转按钮')
    点击(device, '/底部导航栏/交易界面跳转按钮')
    点击(device, '/交易/融资融券/融资融券首页/更多')
    目录校验(device, '二', '更多', '/页面标题')
    点击(device, '/交易/融资融券/更多页面/修改密码')
    # 点击('/交易/融资融券/更多页面/新股申购')
    输入文本(device, '/交易/融资融券/更多页面/修改密码/原信用资金密码', 旧信用资金密码)
    输入文本(device, '/交易/融资融券/更多页面/修改密码/新信用资金密码', 新信用资金密码)
    输入文本(device, '/交易/融资融券/更多页面/修改密码/确认信用资金密码', 新信用资金密码)
    点击(device, '/交易/融资融券/更多页面/修改密码/修改按钮')
    第一次实际结果 = 获取文本信息(device, '/交易/融资融券/更多页面/修改密码/修改按钮/系统提示')
    第一次结果 = 第一次预期结果 in 第一次实际结果
    结果 = False
    if not 第一次结果:
        错误校验点 = 非空校验(device, 第一次预期结果, 第一次实际结果)
        结果 = 第一次结果
    else:
        点击(device, '/底部确定/确定按钮')
        输入文本(device, '/交易/融资融券/更多页面/修改密码/原信用资金密码', 新信用资金密码)
        输入文本(device, '/交易/融资融券/更多页面/修改密码/新信用资金密码', 旧信用资金密码)
        输入文本(device, '/交易/融资融券/更多页面/修改密码/确认信用资金密码', 旧信用资金密码)
        点击(device, '/交易/融资融券/更多页面/修改密码/修改按钮')
        第二次预期结果 = '成功'
        第二次实际结果 = 获取文本信息(device, '/交易/融资融券/更多页面/修改密码/修改按钮/系统提示')
        第二次结果 = 第二次预期结果 in 第二次实际结果
        结果 = 第二次结果
        错误校验点 = 两次校验(device, 第一次预期结果, 第一次实际结果, 第二次预期结果, 第二次实际结果)
    return 结果, 错误校验点


