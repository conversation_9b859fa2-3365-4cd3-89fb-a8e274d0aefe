from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('交易/紫金理财/撤销委托/撤销委托')
def _46c785025ed7d90e70ae4725b5c22ec9(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119', 证券代码='940018'):
    错误校验点 = ''
    # 初始化
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/紫金理财')
    目录校验(device, '二', '紫金理财', '/页面标题')
    sleep(3)
    点击(device, '/交易/普通交易/紫金理财/紫金理财首页/撤销委托')
    if 获取控件对象(device, '/交易/普通交易/紫金理财/紫金理财首页/撤销委托/无产品'):
        实际结果 = 获取文本信息(device, '/交易/普通交易/紫金理财/紫金理财首页/撤销委托/无产品')
        结果 = 实际结果 != ''
        错误校验点 = 为空校验(device)
    else:
        while True:
            撤单按钮 = 获取控件对象(device, '/交易/普通交易/其他交易/ETF网下/委托撤单页面/撤单')
            if 撤单按钮 != '':
                证券代码 = 获取文本信息(device, '/交易/普通交易/紫金理财/撤销委托/证券代码')
                结果 = 证券代码!=''
                错误校验点 = 为空校验(device)
                if 结果:
                    点击(device, '/交易/普通交易/其他交易/ETF网下/委托撤单页面/撤单')
                    点击(device, '/底部确定/确定按钮')
                    实际结果 = 获取文本信息(device, '/交易/普通交易/更多/网络投票/投票查询/投票记录详细页面/提示框内容')
                    预期结果 = '成功'
                    结果 = 预期结果 in 实际结果
                    if not 结果:
                        错误校验点 = 非空校验(device, 预期结果, 实际结果)
                        print('失败！')
                        break
                    点击(device, '/交易/普通交易/撤单/撤单委托明细页面/确定提示按钮')
            else:
                print('全部撤单完毕！')
                break
    return 结果, 错误校验点


