from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('行情/市场/更多/国际汇率/人民币外汇/获取美元离岸人民币价点值')
def _93a01ea4c7f667fc95ec163fc960a4b6(device):
    登录前页面处理(device)
    点击(device, '/底部导航栏/行情界面跳转按钮')
    目录校验(device, '一', '行情', '/行情/选中状态')
    点击(device, '/行情/市场/市场标题栏')
    控件左滑(device, '/行情/市场/沪深按钮标签')
    点击(device, '/行情/市场/更多/更多标签')
    目录校验(device, '二', '更多', '/行情/更多/选中状态')
    滑动控件至屏幕内(device, '/行情/市场/更多/国际汇率/人民币外汇/人民币外汇标签')
    点击(device, '/行情/市场/更多/国际汇率/人民币外汇/人民币外汇标签')
    获取CBOT玉米价格 = 获取文本信息(device, '/行情/市场/更多/国际汇率/人民币外汇/获取美元离岸人民币价点值')
    结果 = 获取CBOT玉米价格 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


