from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('行情/市场/更多/沪深/中小板/获取首只中小板股名称')
def _1da4fb811738c69bc095c55128336f6f(device):
    登录前页面处理(device)
    print(device.appium.driver)
    点击(device, '/底部导航栏/行情界面跳转按钮')
    点击(device, '/行情/市场/市场标题栏')
    控件左滑(device, '/行情/市场/沪深按钮标签')
    点击(device, '/行情/市场/更多/更多标签')
    点击(device, '/行情/市场/更多/沪深/中小板/中小板标签')
    首只中小板股票名称 = 获取文本信息(device, '/行情/市场/更多/沪深/中小板/首只中小板股票名称')
    结果 = 首只中小板股票名称 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


