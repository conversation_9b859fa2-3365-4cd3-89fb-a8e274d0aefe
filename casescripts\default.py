from libs.adb import *
from libs.runcase import define_callback
from libs.sdk import *


@define_callback('before_case')
def before_case(device, name, case_id, func):
    arg_default = func.__defaults__
    if arg_default:
        arg_list = list(func.__code__.co_varnames[1:])
    else:
        arg_default = ()
    arg_map = {}
    for _id, _v in enumerate(arg_default):
        arg_map[arg_list[_id]] = _v
    # if 'git账号' in arg_map:
    #     if len(arg_map['账号']) != 12:
    #         msg = '非正式账号 %s，跳过' % arg_map['账号']
    #         print(msg)
    #         cloud_api('cancel-job', id=case_id)
    #         return 'cancel', msg
    device.log.info('《%s》 已启动：%s' % (name, case_id))
    return None


@define_callback('after_case')
@hide_error
def after_case(device, case_id, success, msg):
    device.log.info('执行结果 %s %s %s' % (case_id, success, msg))


def _back_basic(device):
    # todo 未完成，还有问题。
    while not device.has_element('/底部导航栏/交易界面跳转按钮'):
        device.appium.back()
    if device.has_element('/底部导航栏/交易界面跳转按钮').attr('checked'):
        # 在交易页
        if device.has_element('/交易/普通交易/普通交易首页/基金交易') and device.has_element('/交易/普通交易/普通交易首页/公募及私募基金'):
            device['/交易/普通交易/普通交易首页/基金交易']()
        device.appium.slide_down()
        d = device.has_element('/交易/普通交易/普通交易首页/普通交易')
        d() if d else None
        if device.has_element('/交易/普通交易/普通交易首页/基金交易') and device.has_element('/交易/普通交易/普通交易首页/公募及私募基金'):
            device['/交易/普通交易/普通交易首页/基金交易']()
        device.appium.slide_down()
        # while not device.has_element('/交易/融资融券/融资融券首页/担保品买入按钮') and not device.has_element('/交易/普通交易/普通交易首页/买入按钮'):
    elif device.has_element('/底部导航栏/首页界面跳转按钮').attr('checked'):
        device.has_element("//android.widget.TextView[@text='专家']")()
        device.has_element('要闻')()
        device.has_element('/首页/服务导航栏').swipe_left()
        pass


@define_callback('back_basic')
@hide_error
def back_basic(device):
    if record_type == 'scrcpy' and device.app and device.app['package_name']:
        device.appium.driver.terminate_app(device.app['package_name'])
    
    if APP_RESTART == 'true':
        # 重启appium和app
        device.clean_appium()


def _():
    pass
