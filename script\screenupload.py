import os
import json
import subprocess
import asyncio
import queue
import _thread
from datetime import datetime
import oss2
import minio
from config import Config

import configparser
test_cfg = "./config.ini"
config_raw = configparser.ConfigParser()
config_raw.read(test_cfg)
SOCKET_HOST = config_raw.get('cloud', 'SOCKET_HOST', fallback='*************:3000')


try:
    import websockets
except ModuleNotFoundError:
    print(datetime.now(), 'need websockets')
    os.system('pip install websockets')
    import websockets

Q = queue.Queue()

upload_type = Config.get('IMAGE_UPLOAD_USE')


def upload_storage(remote, local, metadata=None, tag=None):
    if upload_type == 'oss':
        authOss = oss2.Auth(Config.get("ALIYUN_ACCESS_KEY_ID"), Config.get('ALIYUN_ACCESS_KEY_SECRET'))
        bucket = oss2.Bucket(authOss, 'http://oss-cn-qingdao.aliyuncs.com', 'case-video')  # 视频写死
        bucket.put_object_from_file(remote, local)
    else:
        bucket_name = Config.get('S3_IMAGE_BUCKET')
        if bucket_name:
            minioClient = minio.Minio(Config.get("S3_IMAGE_ENDPOINT"),
                                      access_key=Config.get('S3_IMAGE_ACCESS_KEY_ID'),
                                      secret_key=Config.get('S3_IMAGE_ACCESS_KEY_SECRET'),
                                      secure=False)
        else:
            bucket_name = 'image'
            minioClient = minio.Minio('d3wb09097ewy.direct.quickconnect.to:9000',
                                      access_key='AX_USER',
                                      secret_key='B54WcsjnbfDy96E426Cvjwe',
                                      secure=False)
        date = datetime.now().strftime('%Y-%m-%d')
        remote = remote.replace('video/', 'video/' + date + '/')
        from minio.commonconfig import Tags
        with open(local, 'rb') as file_data:
            file_stat = os.stat(local)
            tags = None
            if tag is not None and len(tag) > 0:
                tags = Tags(for_object=True)
                for _k in tag:
                    tags[_k] = tag.get(_k)

            minioClient.put_object(bucket_name, remote, file_data, file_stat.st_size, 'video/mp4', metadata=metadata, tags=tags)
        _size = minioClient.stat_object(bucket_name, remote).size
        if _size != file_stat.st_size:
            assert Exception("upload check failed")


def get_queue():
    while True:
        device, uuid = Q.get()
        now = datetime.now()
        try:
            print(now, '导出 ', device, uuid)
            local_path = 'log/video_cache/%s.mp4' % uuid
            re_path = '/sdcard/zfkj_screen_record/video/%s.mp4' % uuid
            if not os.path.exists(os.path.dirname(local_path)):
                os.makedirs(os.path.dirname(local_path))
            subprocess.call('adb -s %s pull %s %s' % (device, re_path, local_path), timeout=10, shell=True)
            #  stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL,
            print(now, '上传文件', local_path)
            for _ in range(5):
                try:
                    upload_storage('video/%s.mp4' % uuid, local_path, {'Device': device, 'Testcase': uuid, 'Eu': Config.EU_ID},
                                   {'Device': device, 'EU': Config.EU_ID})
                    break
                except:
                    pass
            subprocess.call('adb -s %s shell rm %s ' % (device, re_path), timeout=10, shell=True)
            # os.unlink(local_path)
        except:
            pass
        Q.task_done()


def get_devices_list():
    print(datetime.now(), '更新设备列表')
    p = subprocess.Popen('adb devices', shell=True, stdout=subprocess.PIPE)
    p.stdout.readline()
    devices_list = []
    for line in p.stdout:
        line = line.decode().strip()
        if len(line) > 0:
            d = line.split('\t')
            if len(d) >= 2 and d[1] == 'device':
                devices_list.append(d[0])
    p.wait()
    return devices_list


async def hello(devices_list=None):
    if devices_list is None:
        devices_list = get_devices_list()
    uri = "ws://%s/%s?only=read" % (SOCKET_HOST,'/'.join(devices_list))
    print(datetime.now(), uri)
    async with websockets.connect(uri, timeout=10) as websocket:
        while True:
            message = await websocket.recv()
            try:
                data = json.loads(message)
                print(datetime.now(), data)
                if type(data) is dict and data.get('type') == 'screen' and data.get('device') and data.get('filename'):
                    device = data.get('device')
                    uuid = data.get('filename')
                    Q.put((device, uuid))
            except:
                pass


def run():
    _thread.start_new_thread(get_queue, ())
    while True:
        try:
            asyncio.get_event_loop().run_until_complete(hello())
        except:
            print(datetime.now(), 'reconnect')
            pass


if __name__ == '__main__':
    run()
