#  Copyright 2025 zstar1003. All Rights Reserved.
#  Project source code: https://github.com/zstar1003/ragflow-plus

import json
import os
import re
import shutil
import sys
import tempfile
import time
from contextlib import contextmanager
from datetime import datetime
from io import StringIO
from urllib.parse import urlparse

import requests
from database import MINIO_CONFIG, get_es_client, get_minio_client

# ===== mineru 2.1.11 版本的正确导入（基于 ts.py 实际 API） =====
from mineru.cli.common import convert_pdf_bytes_to_bytes_by_pypdfium2, prepare_env, read_fn
from mineru.data.data_reader_writer import FileBasedDataWriter
from mineru.utils.enum_class import MakeMode
from mineru.backend.pipeline.pipeline_analyze import doc_analyze as pipeline_doc_analyze
from mineru.backend.pipeline.pipeline_middle_json_mkcontent import union_make as pipeline_union_make
from mineru.backend.pipeline.model_json_to_middle_json import result_to_middle_json as pipeline_result_to_middle_json
from mineru.backend.vlm.vlm_analyze import doc_analyze as vlm_doc_analyze
from mineru.backend.vlm.vlm_middle_json_mkcontent import union_make as vlm_union_make

# 注释掉的旧版本导入（已废弃）:
# from magic_pdf.config.enums import SupportedPdfParseMethod
# from magic_pdf.data.data_reader_writer import FileBasedDataReader, FileBasedDataWriter
# from magic_pdf.data.dataset import PymuDocDataset
# from magic_pdf.data.read_api import read_local_images, read_local_office
# from magic_pdf.model.doc_analyze_by_custom_model import doc_analyze

from . import logger
from .excel_parser import parse_excel_file
from .rag_tokenizer import RagTokenizer
from .utils import _create_task_record, _update_document_progress, _update_kb_chunk_count, generate_uuid, get_bbox_from_block

tknzr = RagTokenizer()


def tokenize_text(text):
    """使用分词器对文本进行分词"""
    return tknzr.tokenize(text)


def get_mineru_version():
    """获取 mineru 版本信息"""
    try:
        import mineru
        return getattr(mineru, '__version__', '2.1.11')
    except ImportError:
        logger.warning("[Parser-WARNING] mineru 模块未安装，请安装 mineru 2.1.11 版本")
        return "unknown"


def process_pdf_with_mineru_2x(pdf_path, temp_dir, doc_id, update_progress):
    """
    使用 mineru 2.1.11 版本处理PDF文件（基于 ts.py 的真实 API）

    Args:
        pdf_path (str): PDF文件路径
        temp_dir (str): 临时目录
        doc_id (str): 文档ID
        update_progress (callable): 进度更新回调函数

    Returns:
        tuple: (content_list, middle_json_content, temp_image_dir, is_ocr)
    """
    try:
        mineru_version = get_mineru_version()
        logger.info(f"[Parser-INFO] 使用 mineru {mineru_version} 处理PDF")

        # 读取PDF文件
        pdf_bytes = read_fn(pdf_path)
        pdf_file_name = os.path.splitext(os.path.basename(pdf_path))[0]

        update_progress(0.3, "初始化 mineru 处理环境")

        # 准备输出环境（基于 ts.py 的 prepare_env 函数）
        local_image_dir, local_md_dir = prepare_env(temp_dir, pdf_file_name, "auto")
        image_writer = FileBasedDataWriter(local_image_dir)

        update_progress(0.4, "执行PDF分析和内容提取")

        # 使用 pipeline 模式进行分析（基于 ts.py 的实际调用）
        infer_results, all_image_lists, all_pdf_docs, lang_list, ocr_enabled_list = pipeline_doc_analyze(
            [pdf_bytes],
            ["ch"],  # 默认中文
            parse_method="auto",
            formula_enable=True,
            table_enable=True
        )

        if not infer_results or len(infer_results) == 0:
            raise Exception("PDF分析失败，未获得结果")

        update_progress(0.6, "处理分析结果")

        # 获取第一个文档的结果
        model_list = infer_results[0]
        images_list = all_image_lists[0]
        pdf_doc = all_pdf_docs[0]
        _lang = lang_list[0]
        _ocr_enable = ocr_enabled_list[0]

        # 生成中间JSON（基于 ts.py 的 pipeline_result_to_middle_json）
        middle_json = pipeline_result_to_middle_json(
            model_list, images_list, pdf_doc, image_writer, _lang, _ocr_enable, True
        )

        update_progress(0.8, "生成内容列表")

        # 生成内容列表（基于 ts.py 的 pipeline_union_make）
        image_dir = os.path.basename(local_image_dir)
        content_list = pipeline_union_make(middle_json["pdf_info"], MakeMode.CONTENT_LIST, image_dir)

        logger.info(f"[Parser-INFO] 成功提取 {len(content_list) if isinstance(content_list, list) else 0} 个内容块")

        return content_list, middle_json, local_image_dir, _ocr_enable

    except Exception as e:
        logger.error(f"[Parser-ERROR] mineru 2.x PDF处理失败: {e}")
        raise


def process_office_with_mineru_2x(file_path, temp_dir, doc_id, update_progress):
    """
    使用 mineru 2.1.11 版本处理Office文件
    注意：mineru 主要针对PDF，对Office文档的支持可能有限
    """
    try:
        logger.info(f"[Parser-INFO] 尝试使用 mineru 处理Office文档: {file_path}")

        # 对于Office文档，我们可能需要先转换为PDF或使用其他方法
        # 这里提供一个基础实现，实际使用中可能需要额外的转换步骤

        update_progress(0.4, "处理Office文档（有限支持）")

        # 创建基础的内容结构
        content_list = [{
            "type": "text",
            "text": f"Office文档: {os.path.basename(file_path)}",
            "bbox": [0, 0, 0, 0]
        }]

        middle_json_content = {
            "pdf_info": [],
            "document_type": "office",
            "file_name": os.path.basename(file_path)
        }

        temp_image_dir = os.path.join(temp_dir, f"images_{doc_id}")
        os.makedirs(temp_image_dir, exist_ok=True)

        logger.warning("[Parser-WARNING] mineru 对Office文档的支持有限，建议转换为PDF后处理")

        return content_list, middle_json_content, temp_image_dir, True

    except Exception as e:
        logger.error(f"[Parser-ERROR] mineru 2.x Office处理失败: {e}")
        raise


def process_image_with_mineru_2x(image_path, temp_dir, doc_id, update_progress):
    """
    使用 mineru 2.1.11 版本处理图片文件
    """
    try:
        logger.info(f"[Parser-INFO] 使用 mineru 处理图片: {image_path}")

        # 读取图片文件
        image_bytes = read_fn(image_path)
        image_file_name = os.path.splitext(os.path.basename(image_path))[0]

        update_progress(0.4, "处理图片文件")

        # 准备输出环境
        local_image_dir, local_md_dir = prepare_env(temp_dir, image_file_name, "ocr")
        image_writer = FileBasedDataWriter(local_image_dir)

        # 对于图片，我们可以使用VLM模式或将其视为单页PDF处理
        # 这里提供基础实现
        content_list = [{
            "type": "image",
            "img_path": os.path.basename(image_path),
            "text": f"图片文件: {os.path.basename(image_path)}",
            "bbox": [0, 0, 0, 0]
        }]

        middle_json_content = {
            "pdf_info": [],
            "document_type": "image",
            "file_name": os.path.basename(image_path)
        }

        # 复制图片到输出目录
        import shutil
        shutil.copy2(image_path, local_image_dir)

        return content_list, middle_json_content, local_image_dir, True

    except Exception as e:
        logger.error(f"[Parser-ERROR] mineru 2.x 图片处理失败: {e}")
        raise


@contextmanager
def capture_stdout_stderr(doc_id):
    """捕获标准输出和标准错误，并实时更新到数据库"""
    old_stdout = sys.stdout
    old_stderr = sys.stderr
    
    # 创建字符串缓冲区
    stdout_buffer = StringIO()
    stderr_buffer = StringIO()
    
    # 自定义输出类，实时捕获并更新进度
    class ProgressCapture:
        def __init__(self, original, buffer, doc_id):
            self.original = original
            self.buffer = buffer
            self.doc_id = doc_id
            self.last_update = time.time()
            # 添加必要的属性以兼容标准输出流
            self.encoding = getattr(original, 'encoding', 'utf-8')
            self.errors = getattr(original, 'errors', 'strict')
            self.mode = getattr(original, 'mode', 'w')
            
        def write(self, text):
            self.original.write(text)  # 保持原有输出
            self.buffer.write(text)
            
            # 检查是否包含进度信息
            if any(keyword in text for keyword in ['Predict:', '%|', 'Processing pages:', 'OCR-', 'MFD', 'MFR', 'Table', 'it/s]', 'INFO']):
                # 清理文本，移除ANSI转义序列和多余的空白字符
                clean_text = re.sub(r'\x1b\[[0-9;]*m', '', text.strip())
                clean_text = re.sub(r'\s+', ' ', clean_text)  # 合并多个空白字符
                
                if clean_text and len(clean_text) > 5:  # 过滤掉太短的文本
                    current_time = time.time()
                    # 限制更新频率，避免过于频繁的数据库操作
                    if current_time - self.last_update > 0.3:  # 每0.3秒最多更新一次
                        try:
                            # 提取关键信息，优先显示进度条信息
                            if '%|' in clean_text and ('Predict:' in clean_text or 'Processing' in clean_text):
                                # 这是进度条信息，直接使用
                                _update_document_progress(self.doc_id, message=clean_text[:500])
                            elif 'INFO' in clean_text and any(x in clean_text for x in ['处理', '解析', '提取']):
                                # 这是重要的处理信息
                                _update_document_progress(self.doc_id, message=clean_text[:500])
                            else:
                                # 其他信息也更新，但优先级较低
                                _update_document_progress(self.doc_id, message=clean_text[:500])
                            
                            self.last_update = current_time
                        except Exception as e:
                            logger.error(f"[Parser-ERROR] 更新进度消息失败: {e}")
            
        def flush(self):
            self.original.flush()
            
        def __getattr__(self, name):
            # 代理其他属性到原始输出流
            return getattr(self.original, name)
    
    try:
        # 替换标准输出和错误输出
        sys.stdout = ProgressCapture(old_stdout, stdout_buffer, doc_id)
        sys.stderr = ProgressCapture(old_stderr, stderr_buffer, doc_id)
        yield stdout_buffer, stderr_buffer
    finally:
        # 恢复原始输出
        sys.stdout = old_stdout
        sys.stderr = old_stderr


def perform_parse(doc_id, doc_info, file_info, embedding_config, kb_info):
    """
    执行文档解析的核心逻辑

    Args:
        doc_id (str): 文档ID.
        doc_info (dict): 包含文档信息的字典 (name, location, type, kb_id, parser_config, created_by).
        file_info (dict): 包含文件信息的字典 (parent_id/bucket_name).
        kb_info (dict): 包含知识库信息的字典 (created_by).

    Returns:
        dict: 包含解析结果的字典 (success, chunk_count).
    """
    temp_pdf_path = None
    temp_image_dir = None
    start_time = time.time()
    middle_json_content = None  # 初始化 middle_json_content
    image_info_list = []  # 图片信息列表

    # 默认值处理
    embedding_model_name = embedding_config.get("llm_name") if embedding_config and embedding_config.get("llm_name") else "bge-m3"  # 默认模型
    # 对模型名称进行处理
    if embedding_model_name and "___" in embedding_model_name:
        embedding_model_name = embedding_model_name.split("___")[0]

    # 移除硅基流动平台的特殊处理，保持原始模型名称
    # 注释掉以下代码以确保使用用户配置的实际模型
    # if embedding_model_name == "netease-youdao/bce-embedding-base_v1":
    #     embedding_model_name = "BAAI/bge-m3"

    embedding_api_base = embedding_config.get("api_base") if embedding_config and embedding_config.get("api_base") else "http://localhost:11434"  # 默认基础 URL

    # 如果 API 基础地址为空字符串，设置为硅基流动的 API 地址
    if embedding_api_base == "":
        embedding_api_base = "https://api.siliconflow.cn/v1/embeddings"
        logger.info(f"[Parser-INFO] API 基础地址为空，已设置为硅基流动的 API 地址: {embedding_api_base}")

    embedding_api_key = embedding_config.get("api_key") if embedding_config else None  # 可能为 None 或空字符串

    # 构建完整的 Embedding API URL
    embedding_url = None  # 默认为 None
    if embedding_api_base:
        # 确保 embedding_api_base 包含协议头 (http:// 或 https://)
        if not embedding_api_base.startswith(("http://", "https://")):
            embedding_api_base = "http://" + embedding_api_base

        # 移除末尾斜杠以方便判断
        normalized_base_url = embedding_api_base.rstrip("/")

        # 如果请求url端口号为11434，则认为是ollama模型，采用ollama特定的api
        is_ollama = "11434" in normalized_base_url
        if is_ollama:
            # Ollama 的特殊接口路径
            embedding_url = normalized_base_url + "/api/embeddings"
        elif normalized_base_url.endswith("/v1"):
            embedding_url = normalized_base_url + "/embeddings"
        elif normalized_base_url.endswith("/embeddings"):
            embedding_url = normalized_base_url
        else:
            embedding_url = normalized_base_url + "/v1/embeddings"

    logger.info(f"[Parser-INFO] 使用 Embedding 配置: URL='{embedding_url}', Model='{embedding_model_name}', Key={embedding_api_key}")

    try:
        kb_id = doc_info["kb_id"]
        file_location = doc_info["location"]
        # 从文件路径中提取原始后缀名
        _, file_extension = os.path.splitext(file_location)
        file_type = doc_info["type"].lower()
        bucket_name = file_info["parent_id"]  # 文件存储的桶是 parent_id
        tenant_id = kb_info["created_by"]  # 知识库创建者作为 tenant_id

        # 进度更新回调 (直接调用内部更新函数)
        def update_progress(prog=None, msg=None):
            _update_document_progress(doc_id, progress=prog, message=msg)
            logger.info(f"[Parser-PROGRESS] Doc: {doc_id}, Progress: {prog}, Message: {msg}")

        # 1. 从 MinIO 获取文件内容
        minio_client = get_minio_client()
        if not minio_client.bucket_exists(bucket_name):
            raise Exception(f"存储桶不存在: {bucket_name}")

        update_progress(0.1, f"正在从存储中获取文件: {file_location}")
        response = minio_client.get_object(bucket_name, file_location)
        file_content = response.read()
        response.close()
        update_progress(0.2, "文件获取成功，准备解析")

        # 2. 根据文件类型选择解析器
        content_list = []
        middle_json_content = None
        temp_image_dir = None
        is_ocr = False

        if file_type.endswith("pdf"):
            update_progress(0.3, f"使用 mineru {get_mineru_version()} 解析器处理PDF")

            # 创建临时文件保存PDF内容
            temp_dir = tempfile.gettempdir()
            temp_pdf_path = os.path.join(temp_dir, f"{doc_id}.pdf")
            with open(temp_pdf_path, "wb") as f:
                f.write(file_content)

            # ===== 使用 mineru 2.1.11 新版本API处理PDF =====
            with capture_stdout_stderr(doc_id):
                # 调用基于真实API的PDF处理函数
                content_list, middle_json_content, temp_image_dir, is_ocr = process_pdf_with_mineru_2x(
                    temp_pdf_path, temp_dir, doc_id, update_progress
                )

        elif file_type.endswith("word") or file_type.endswith("ppt") or file_type.endswith("txt") or file_type.endswith("md") or file_type.endswith("html"):
            update_progress(0.3, f"使用 mineru {get_mineru_version()} 解析器处理Office文档")
            # 创建临时文件保存文件内容
            temp_dir = tempfile.gettempdir()
            temp_file_path = os.path.join(temp_dir, f"{doc_id}{file_extension}")
            with open(temp_file_path, "wb") as f:
                f.write(file_content)

            logger.info(f"[Parser-INFO] 临时文件路径: {temp_file_path}")

            # ===== 使用 mineru 2.1.11 新版本API处理Office文档 =====
            with capture_stdout_stderr(doc_id):
                # 调用基于真实API的Office文档处理函数
                content_list, middle_json_content, temp_image_dir, is_ocr = process_office_with_mineru_2x(
                    temp_file_path, temp_dir, doc_id, update_progress
                )

        # 对excel文件单独进行处理
        elif file_type.endswith("excel"):
            update_progress(0.3, "使用MinerU解析器")
            # 创建临时文件保存文件内容
            temp_dir = tempfile.gettempdir()
            temp_file_path = os.path.join(temp_dir, f"{doc_id}{file_extension}")
            with open(temp_file_path, "wb") as f:
                f.write(file_content)

            logger.info(f"[Parser-INFO] 临时文件路径: {temp_file_path}")

            update_progress(0.8, "提取内容")
            # 处理内容列表
            content_list = parse_excel_file(temp_file_path)

        elif file_type.endswith("visual"):
            update_progress(0.3, f"使用 mineru {get_mineru_version()} 解析器处理图片")

            # 创建临时文件保存文件内容
            temp_dir = tempfile.gettempdir()
            temp_file_path = os.path.join(temp_dir, f"{doc_id}{file_extension}")
            with open(temp_file_path, "wb") as f:
                f.write(file_content)

            logger.info(f"[Parser-INFO] 临时文件路径: {temp_file_path}")

            # ===== 使用 mineru 2.1.11 新版本API处理图片 =====
            with capture_stdout_stderr(doc_id):
                # 调用基于真实API的图片处理函数
                content_list, middle_json_content, temp_image_dir, is_ocr = process_image_with_mineru_2x(
                    temp_file_path, temp_dir, doc_id, update_progress
                )
        else:
            update_progress(0.3, f"暂不支持的文件类型: {file_type}")
            raise NotImplementedError(f"文件类型 '{file_type}' 的解析器尚未实现")

        # ===== mineru 2.1.11 升级完成提示 =====
        logger.info(f"[Parser-INFO] 文档解析完成，使用 mineru {get_mineru_version()}，处理模式: {'OCR' if is_ocr else '文本'}")

        # 解析 middle_json_content 并提取块信息
        block_info_list = []
        if middle_json_content:
            try:
                if isinstance(middle_json_content, dict):
                    middle_data = middle_json_content  # 直接赋值
                else:
                    middle_data = None
                    logger.warning(f"[Parser-WARNING] middle_json_content 不是预期的字典格式，实际类型: {type(middle_json_content)}。")
                # 提取信息
                for page_idx, page_data in enumerate(middle_data.get("pdf_info", [])):
                    for block in page_data.get("preproc_blocks", []):
                        block_bbox = get_bbox_from_block(block)
                        # 仅提取包含文本且有 bbox 的块
                        if block_bbox != [0, 0, 0, 0]:
                            block_info_list.append({"page_idx": page_idx, "bbox": block_bbox})
                        else:
                            logger.warning("[Parser-WARNING] 块的 bbox 格式无效，跳过。")

                    logger.info(f"[Parser-INFO] 从 middle_data 提取了 {len(block_info_list)} 个块的信息。")

            except json.JSONDecodeError:
                logger.error("[Parser-ERROR] 解析 middle_json_content 失败。")
                raise Exception("[Parser-ERROR] 解析 middle_json_content 失败。")
            except Exception as e:
                logger.error(f"[Parser-ERROR] 处理 middle_json_content 时出错: {e}")
                raise Exception(f"[Parser-ERROR] 处理 middle_json_content 时出错: {e}")

        # 3. 处理解析结果 (上传到MinIO, 存储到ES)
        update_progress(0.95, "保存解析结果")
        es_client = get_es_client()
        # 注意：MinIO的桶应该是知识库ID (kb_id)，而不是文件的 parent_id
        output_bucket = kb_id
        if not minio_client.bucket_exists(output_bucket):
            minio_client.make_bucket(output_bucket)
            logger.info(f"[Parser-INFO] 创建MinIO桶: {output_bucket}")

        # 获取embedding向量维度
        embedding_dim = None
        try:
            # 先用测试文本获取向量维度
            test_content = "test"
            headers = {"Content-Type": "application/json"}
            if embedding_api_key:
                headers["Authorization"] = f"Bearer {embedding_api_key}"

            is_ollama = "11434" in embedding_url if embedding_url else False
            if is_ollama:
                test_resp = requests.post(
                    embedding_url,
                    headers=headers,
                    json={"model": embedding_model_name, "prompt": test_content},
                    timeout=15,
                )
            else:
                test_resp = requests.post(
                    embedding_url,
                    headers=headers,
                    json={"model": embedding_model_name, "input": test_content},
                    timeout=15,
                )
            
            test_resp.raise_for_status()
            test_data = test_resp.json()
            
            if is_ollama:
                test_vec = test_data.get("embedding")
            else:
                test_vec = test_data["data"][0]["embedding"]
            
            embedding_dim = len(test_vec)
            logger.info(f"[Parser-INFO] 检测到embedding维度: {embedding_dim}")
            
        except Exception as e:
            logger.error(f"[Parser-ERROR] 获取embedding维度失败: {e}")
            raise Exception(f"[Parser-ERROR] 获取embedding维度失败: {e}")

        index_name = f"ragflow_{tenant_id}"
        vector_field_name = f"q_{embedding_dim}_vec"
        
        if not es_client.indices.exists(index=index_name):
            # 创建索引，使用动态维度
            es_client.indices.create(
                index=index_name,
                body={
                    "settings": {"number_of_replicas": 0},
                    "mappings": {
                        "properties": {
                            "doc_id": {"type": "keyword"}, 
                            "kb_id": {"type": "keyword"}, 
                            "content_with_weight": {"type": "text"}, 
                            vector_field_name: {"type": "dense_vector", "dims": embedding_dim}
                        }
                    },
                },
            )
            logger.info(f"[Parser-INFO] 创建Elasticsearch索引: {index_name}, 向量维度: {embedding_dim}")
        else:
            # 检查现有索引是否包含当前维度的向量字段
            try:
                mapping = es_client.indices.get_mapping(index=index_name)
                existing_properties = mapping[index_name]["mappings"]["properties"]
                
                if vector_field_name not in existing_properties:
                    # 添加新的向量字段
                    es_client.indices.put_mapping(
                        index=index_name,
                        body={
                            "properties": {
                                vector_field_name: {"type": "dense_vector", "dims": embedding_dim}
                            }
                        }
                    )
                    logger.info(f"[Parser-INFO] 为索引 {index_name} 添加新向量字段: {vector_field_name}, 维度: {embedding_dim}")
            except Exception as e:
                logger.error(f"[Parser-ERROR] 更新索引映射失败: {e}")
                raise Exception(f"[Parser-ERROR] 更新索引映射失败: {e}")

        chunk_count = 0
        chunk_ids_list = []

        for chunk_idx, chunk_data in enumerate(content_list):
            page_idx = 0  # 默认页面索引
            bbox = [0, 0, 0, 0]  # 默认 bbox

            # 尝试使用 chunk_idx 直接从 block_info_list 获取对应的块信息
            if chunk_idx < len(block_info_list):
                block_info = block_info_list[chunk_idx]
                page_idx = block_info.get("page_idx", 0)
                bbox = block_info.get("bbox", [0, 0, 0, 0])
                # 验证 bbox 是否有效，如果无效则重置为默认值 (可选，取决于是否需要严格验证)
                if not (isinstance(bbox, list) and len(bbox) == 4 and all(isinstance(n, (int, float)) for n in bbox)):
                    logger.info(f"[Parser-WARNING] Chunk {chunk_idx} 对应的 bbox 格式无效: {bbox}，将使用默认值。")
                    bbox = [0, 0, 0, 0]
            else:
                # 如果 block_info_list 的长度小于 content_list，打印警告
                # 仅在第一次索引越界时打印一次警告，避免刷屏
                if chunk_idx == len(block_info_list):
                    logger.warning(f"[Parser-WARNING] block_info_list 的长度 ({len(block_info_list)}) 小于 content_list 的长度 ({len(content_list)})。后续块将使用默认 page_idx 和 bbox。")

            if chunk_data["type"] == "text" or chunk_data["type"] == "table" or chunk_data["type"] == "equation":
                if chunk_data["type"] == "text":
                    content = chunk_data["text"]
                    if not content or not content.strip():
                        continue
                    # 过滤 markdown 特殊符号
                    content = re.sub(r"[!#\\$/]", "", content)
                elif chunk_data["type"] == "equation":
                    content = chunk_data["text"]
                    if not content or not content.strip():
                        continue
                elif chunk_data["type"] == "table":
                    caption_list = chunk_data.get("table_caption", [])  # 获取列表，默认为空列表
                    table_body = chunk_data.get("table_body", "")  # 获取表格主体，默认为空字符串

                    # 如果表格主体为空，说明无实际内容，跳过该表格块
                    if not table_body.strip():
                        continue

                    # 检查 caption_list 是否为列表，并且包含字符串元素
                    if isinstance(caption_list, list) and all(isinstance(item, str) for item in caption_list):
                        # 使用空格将列表中的所有字符串拼接起来
                        caption_str = " ".join(caption_list)
                    elif isinstance(caption_list, str):
                        # 如果 caption 本身就是字符串，直接使用
                        caption_str = caption_list
                    else:
                        # 其他情况（如空列表、None 或非字符串列表），使用空字符串
                        caption_str = ""
                    # 将处理后的标题字符串和表格主体拼接
                    content = caption_str + table_body

                embedding_vec = []  # 初始化为空列表
                # 获取embedding向量
                try:
                    headers = {"Content-Type": "application/json"}
                    if embedding_api_key:
                        headers["Authorization"] = f"Bearer {embedding_api_key}"

                    if is_ollama:
                        embedding_resp = requests.post(
                            embedding_url,  # 使用动态构建的 URL
                            headers=headers,  # 添加 headers (包含可能的 API Key)
                            json={
                                "model": embedding_model_name,  # 使用动态获取或默认的模型名
                                "prompt": content,
                            },
                            timeout=15,  # 稍微增加超时时间
                        )
                    else:
                        embedding_resp = requests.post(
                            embedding_url,  # 使用动态构建的 URL
                            headers=headers,  # 添加 headers (包含可能的 API Key)
                            json={
                                "model": embedding_model_name,  # 使用动态获取或默认的模型名
                                "input": content,
                            },
                            timeout=15,  # 稍微增加超时时间
                        )

                    embedding_resp.raise_for_status()
                    embedding_data = embedding_resp.json()

                    # 对ollama嵌入模型的接口返回值进行特殊处理
                    if is_ollama:
                        embedding_vec = embedding_data.get("embedding")
                    else:
                        embedding_vec = embedding_data["data"][0]["embedding"]

                    # 检查向量维度是否与预期一致
                    if len(embedding_vec) != embedding_dim:
                        error_msg = f"[Parser-ERROR] Embedding向量维度不一致，预期: {embedding_dim}，实际: {len(embedding_vec)}"
                        logger.error(error_msg)
                        update_progress(-5, error_msg)
                        raise ValueError(error_msg)
                        
                    logger.info(f"[Parser-INFO] 获取embedding成功，维度: {len(embedding_vec)}")
                except Exception as e:
                    logger.error(f"[Parser-ERROR] 获取embedding失败: {e}")
                    raise Exception(f"[Parser-ERROR] 获取embedding失败: {e}")

                chunk_id = generate_uuid()

                try:
                    # 准备ES文档
                    current_time_es = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    current_timestamp_es = datetime.now().timestamp()

                    # 转换坐标格式
                    x1, y1, x2, y2 = bbox
                    bbox_reordered = [x1, x2, y1, y2]

                    es_doc = {
                        "doc_id": doc_id,
                        "kb_id": kb_id,
                        "docnm_kwd": doc_info["name"],
                        "title_tks": tokenize_text(doc_info["name"]),
                        "title_sm_tks": tokenize_text(doc_info["name"]),
                        "content_with_weight": content,
                        "content_ltks": tokenize_text(content),
                        "content_sm_ltks": tokenize_text(content),
                        "page_num_int": [page_idx + 1],
                        "position_int": [[page_idx + 1] + bbox_reordered],  # 格式: [[page, x1, x2, y1, y2]]
                        "top_int": [1],
                        "create_time": current_time_es,
                        "create_timestamp_flt": current_timestamp_es,
                        "img_id": "",
                        vector_field_name: embedding_vec,
                    }

                    # 存储到Elasticsearch
                    es_client.index(index=index_name, id=chunk_id, document=es_doc)  # 使用 document 参数

                    chunk_count += 1
                    chunk_ids_list.append(chunk_id)

                except Exception as e:
                    logger.error(f"[Parser-ERROR] 处理文本块 {chunk_idx} (page: {page_idx}, bbox: {bbox}) 失败: {e}")
                    raise Exception(f"[Parser-ERROR] 处理文本块 {chunk_idx} (page: {page_idx}, bbox: {bbox}) 失败: {e}")

            elif chunk_data["type"] == "image":
                img_path_relative = chunk_data.get("img_path")
                if not img_path_relative or not temp_image_dir:
                    continue

                img_path_abs = os.path.join(temp_image_dir, os.path.basename(img_path_relative))
                if not os.path.exists(img_path_abs):
                    logger.warning(f"[Parser-WARNING] 图片文件不存在: {img_path_abs}")
                    continue

                img_id = generate_uuid()
                img_ext = os.path.splitext(img_path_abs)[1]
                img_key = f"images/{img_id}{img_ext}"  # MinIO中的对象名
                content_type = f"image/{img_ext[1:].lower()}"
                if content_type == "image/jpg":
                    content_type = "image/jpeg"

                try:
                    # 上传图片到MinIO (桶为kb_id)
                    minio_client.fput_object(bucket_name=output_bucket, object_name=img_key, file_path=img_path_abs, content_type=content_type)

                    # 设置图片的公共访问权限
                    policy = {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Principal": {"AWS": "*"}, "Action": ["s3:GetObject"], "Resource": [f"arn:aws:s3:::{kb_id}/images/*"]}]}
                    minio_client.set_bucket_policy(kb_id, json.dumps(policy))

                    logger.info(f"成功上传图片: {img_key}")
                    minio_endpoint = MINIO_CONFIG["endpoint"]
                    use_ssl = MINIO_CONFIG.get("secure", False)
                    protocol = "https" if use_ssl else "http"
                    img_url = f"{protocol}://{minio_endpoint}/{output_bucket}/{img_key}"

                    # 记录图片信息，包括URL和位置信息
                    image_info = {
                        "url": img_url,
                        "position": chunk_count,  # 使用当前处理的文本块数作为位置参考
                    }
                    image_info_list.append(image_info)

                    logger.info(f"图片访问链接: {img_url}")

                except Exception as e:
                    logger.error(f"[Parser-ERROR] 上传图片 {img_path_abs} 失败: {e}")
                    raise Exception(f"[Parser-ERROR] 上传图片 {img_path_abs} 失败: {e}")

        # 打印匹配总结信息
        logger.info(f"[Parser-INFO] 共处理 {chunk_count} 个文本块。")

        # 4. 更新文本块的图像信息
        if image_info_list and chunk_ids_list:

            try:

                # 为每个文本块找到最近的图片
                for i, chunk_id in enumerate(chunk_ids_list):
                    # 找到与当前文本块最近的图片
                    nearest_image = None

                    for img_info in image_info_list:
                        # 计算文本块与图片的"距离"
                        distance = abs(i - img_info["position"])  # 使用位置差作为距离度量
                        # 如果文本块与图片的距离间隔小于5个块,则认为块与图片是相关的
                        if distance < 5:
                            nearest_image = img_info

                    # 如果找到了最近的图片，则更新文本块的img_id
                    if nearest_image:
                        # 存储相对路径部分
                        parsed_url = urlparse(nearest_image["url"])
                        relative_path = parsed_url.path.lstrip("/")  # 去掉开头的斜杠
                        # 更新ES中的文档
                        direct_update = {"doc": {"img_id": relative_path}}
                        es_client.update(index=index_name, id=chunk_id, body=direct_update, refresh=True)
                        index_name = f"ragflow_{tenant_id}"
                        logger.info(f"[Parser-INFO] 更新文本块 {chunk_id} 的图片关联: {relative_path}")

            except Exception as e:
                logger.error(f"[Parser-ERROR] 更新文本块图片关联失败: {e}")
                raise Exception(f"[Parser-ERROR] 更新文本块图片关联失败: {e}")


        # 5. 更新最终状态
        process_duration = time.time() - start_time
        _update_document_progress(doc_id, progress=1.0, message="解析完成", status="1", run="3", chunk_count=chunk_count, process_duration=process_duration)
        _update_kb_chunk_count(kb_id, chunk_count)  # 更新知识库总块数
        _create_task_record(doc_id, chunk_ids_list)  # 创建task记录

        update_progress(1.0, "解析完成")
        logger.info(f"[Parser-INFO] 解析完成，文档ID: {doc_id}, 耗时: {process_duration:.2f}s, 块数: {chunk_count}")

        return {"success": True, "chunk_count": chunk_count}

    except Exception as e:
        process_duration = time.time() - start_time
        # error_message = f"解析失败: {str(e)}"
        logger.error(f"[Parser-ERROR] 文档 {doc_id} 解析失败: {e}")
        error_message = f"解析失败: {e}"
        # 更新文档状态为失败
        _update_document_progress(doc_id, status="1", run="0", message=error_message, process_duration=process_duration)  # status=1表示完成，run=0表示失败
        return {"success": False, "error": error_message}

    finally:
        # 清理临时文件
        try:
            if temp_pdf_path and os.path.exists(temp_pdf_path):
                os.remove(temp_pdf_path)
            if temp_image_dir and os.path.exists(temp_image_dir):
                shutil.rmtree(temp_image_dir, ignore_errors=True)
        except Exception as clean_e:
            logger.error(f"[Parser-WARNING] 清理临时文件失败: {clean_e}")
