from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('理财/基金严选/基金严选/基金严选')
def _a68fc3a95349188f3d16701e4fd6b7fb(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # 初始化
    登录前页面处理(device)
    理财登陆(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '理财', '/理财/选中状态')
    滑动控件至屏幕内_理财(device, '/理财/首发基金')
    # 滑动控件至屏幕内_理财('/理财/货币增强')
    控件左滑_理财(device, '/理财/首发基金')
    点击(device, '/理财/自定义添加')
    # time.sleep(2)
    上滑(device)
    # time.sleep(2)
    上滑(device)
    # 循环滑动('/理财/基金严选', '上')
    # time.sleep(5)
    device.appium.driver.background_app(3)
    点击(device, '/理财/基金严选')
    目录校验(device, '二', '基金严选', '/页面标题')
    try:
        实际结果 = 获取文本信息(device, '/理财/理财首页/公募基金/基金严选/第一条记录收益率')
    except:
        返回(device)
        点击(device, '/理财/基金严选')
        sleep(5)
        实际结果 = 获取文本信息(device, '/理财/理财首页/公募基金/基金严选/第一条记录收益率')
    print(实际结果)
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


