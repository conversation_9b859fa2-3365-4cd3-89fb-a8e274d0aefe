from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('理财/稳健收益/灵活期限_天天财/灵活期限_天天财')
def _05e803c5577f8c60d4e755dff3e0a045(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # 初始化
    登录前页面处理(device)
    理财登陆(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '理财', '/理财/选中状态')
    点击(device, '/理财/理财首页/稳健收益')
    目录校验(device, '二', '稳健收益', '/页面标题')
    sleep(3)
    点击(device, '/理财/理财/稳健收益/灵活期限')
    sleep(2)
    上滑(device)
    点击(device, '/理财/理财首页/加载检查')
    返回(device)
    点击(device, '/理财/理财/稳健收益/灵活期限/天天财')
    # 收益率 = 获取文本信息('/理财/理财/稳健收益/灵活期限/天天财/收益率')
    # 收益率 = 获取文本信息('/理财/理财/稳健收益/灵活期限/天天财/第一条记录')
    # 实际结果 = float(str(收益率).strip('%'))
    实际结果 = 获取文本信息(device, '/理财/理财/稳健收益/灵活期限/天天财/第一条记录')
    print(实际结果)
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


