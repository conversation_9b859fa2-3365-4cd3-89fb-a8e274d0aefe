import subprocess
import re
from loguru import logger

class Info(object):


    def get_model(self):
        """
        型号
        """
        sp = subprocess.Popen("adb shell getprop ro.product.model", stdin=subprocess.PIPE, stdout=subprocess.PIPE, shell=True)
        return str(sp.stdout.read(),'utf-8').split()[0]

    def get_screen(self):
        """
        屏幕
        """
        sp = subprocess.Popen("adb shell wm size", stdin=subprocess.PIPE, stdout=subprocess.PIPE, shell=True)
        return str(sp.stdout.read(),'utf-8').split(' ')[-1].strip()

    def get_RAM(self):
        """
        运行内存
        """
        sp = subprocess.Popen("adb shell dumpsys meminfo", stdin=subprocess.PIPE, stdout=subprocess.PIPE, shell=True)
        free,used = 0,0
        for i in str(sp.stdout.read(), 'utf-8').split('\r\n'):
            if 'Free RAM' in i:
                free = int(re.findall(': (.*?)K',i)[0].replace(',',''))
            if 'Used RAM' in i:
                used = int(re.findall(': (.*?)K',i)[0].replace(',',''))
        return (free+used)/1024/1024

    def get_sys_version(self):
        """
        系统版本号
        UI版本
        CPU型号
        """
        sp = subprocess.Popen("adb shell getprop", stdin=subprocess.PIPE, stdout=subprocess.PIPE, shell=True)
        dicts = {}
        for i in str(sp.stdout.read(), 'utf-8').split('\r\n'):
            print(i)
            if 'ro.build.ver.physical' in i:
                dicts['系统版本号'] = i.split(': ')[-1].replace('[','').replace(']','')
            if 'ro.build.version.emui' in i:
                dicts['UI版本'] = i.split(': ')[-1].replace('[','').replace(']','')
            if 'ro.config.cpu_info_display' in i:
                dicts['CPU型号'] = i.split(': ')[-1].replace('[', '').replace(']', '')
            if 'ro.product.cpu.abi' in i:
                dicts['CPU架构'] = i.split(': ')[-1].replace('[', '').replace(']', '')
            if 'ro.gpu' in i:
                dicts['GPU型号'] = i.split(': ')[-1].replace('[', '').replace(']', '')
            if 'ro.config.marketing_name' in i:
                dicts['手机名称'] = i.split(': ')[-1].replace('[', '').replace(']', '')
            if 'ro.product.brand' in i:
                dicts['生产厂商'] = i.split(': ')[-1].replace('[', '').replace(']', '')
        return dicts

    def get_cpu_nums(self):
        """
        cpu 核心数
        """
        sp = subprocess.Popen("adb shell cat /proc/cpuinfo", stdin=subprocess.PIPE, stdout=subprocess.PIPE, shell=True)
        result = []
        for i in str(sp.stdout.read(), 'utf-8').split('\r\n'):
            if 'processor' in i:
                result.append(i)
        return len(result)

    def get_honor_x30(self):
        """
        荣耀x30
        """
        dicts = {
            '型号': self.get_model(),
            '屏幕': self.get_screen(),
            '运行内存': self.get_RAM(),
            'cpu核心数': self.get_cpu_nums()
        }
        dicts.update(self.get_sys_version())
        logger.debug(dicts)

if __name__ == '__main__':
    obj = Info()
    obj.get_honor_x30()

