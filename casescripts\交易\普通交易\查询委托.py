from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('交易/普通交易/查询委托/当日委托')
def _e4c992aa3da0dccf2c5c1c762c5b36ed(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # 初始化
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/普通交易')
    点击(device, '/交易/普通交易/普通交易首页/查询委托')
    目录校验(device, '二', '委托', '/交易/普通交易/委托/选中状态')
    实际结果 = ''
    # 如果没获取到第一条记录则判断是否暂无记录
    try:
        实际结果 = 获取文本信息(device, '/交易/基金交易/查询委托/当日委托/委托列表第一条记录的业务名称')
    except:
        实际结果 = 获取控件对象(device, '/交易/普通交易/查询成交/当日成交/暂无记录')
    错误校验点 = 为空校验(device)
    结果 = 实际结果 != ''
    return 结果, 错误校验点


