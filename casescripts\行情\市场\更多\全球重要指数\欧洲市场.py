from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('行情/市场/更多/全球重要指数/欧洲市场/获取英国富时100指数点值')
def _e11ab1c0577baa44c0c7c587a2466eab(device):
    登录前页面处理(device)
    点击(device, '/底部导航栏/行情界面跳转按钮')
    目录校验(device, '一', '行情', '/行情/选中状态')
    点击(device, '/行情/市场/市场标题栏')
    控件左滑(device, '/行情/市场/沪深按钮标签')
    点击(device, '/行情/市场/更多/更多标签')
    目录校验(device, '二', '更多', '/行情/更多/选中状态')
    滑动控件至屏幕内(device, '/行情/市场/更多/全球重要指数/欧洲市场/欧洲市场标签')
    点击(device, '/行情/市场/更多/全球重要指数/欧洲市场/欧洲市场标签')
    获取英国富时100指数点值 = 获取文本信息(device, '/行情/市场/更多/全球重要指数/欧洲市场/获取英国富时100指数点值')
    结果 = 获取英国富时100指数点值 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


