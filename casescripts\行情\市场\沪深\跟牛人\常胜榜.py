from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('行情/市场/沪深/跟牛人/常胜榜/常胜榜第一人名')
def _d3a0587121cf4cb61cd5aa56b76058ae(device):
    登录前页面处理(device)
    点击(device, '/底部导航栏/行情界面跳转按钮')
    目录校验(device, '一', '行情', '/行情/选中状态')
    点击(device, '/行情/市场/市场标题栏')
    目录校验(device, '二', '沪深', '/行情/更多/选中状态')
    滑动控件至屏幕内(device, '/行情/市场/沪深/跟牛人/进入牛人榜列表')
    点击(device, '/行情/市场/沪深/跟牛人/进入牛人榜列表')
    sleep(5)
    点击(device, '/行情/市场/沪深/跟牛人_常胜榜/常胜榜标签')
    常胜榜第一人名 = 获取文本信息(device, '/行情/市场/沪深/跟牛人_常胜榜/常胜榜第一人名')
    结果 = 常胜榜第一人名 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


