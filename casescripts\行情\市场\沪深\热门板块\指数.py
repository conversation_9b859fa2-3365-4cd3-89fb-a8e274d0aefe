from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('行情/市场/沪深/热门板块/指数/首只指数成分股最新价')
def _fa4b0f8ce43321887ccb90aa690b657b(device):
    登录前页面处理(device)
    点击(device, '/底部导航栏/行情界面跳转按钮')
    目录校验(device, '一', '行情', '/行情/选中状态')
    点击(device, '/行情/市场/市场标题栏')
    目录校验(device, '二', '沪深', '/行情/更多/选中状态')
    上滑(device, 5000)
    点击(device, '/行情/市场/沪深/热门板块/更多版块按钮')
    点击(device, '/行情/市场/沪深/热门板块/更多/指数板块/指数版块标签')
    点击(device, '/行情/市场/沪深/热门板块/更多/指数板块/首只指数')
    行业首行股票最新价 = 获取文本信息(device, '/行情/市场/沪深/热门板块/更多/指数板块/首只指数成分股最新价')
    结果 = 行业首行股票最新价 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


