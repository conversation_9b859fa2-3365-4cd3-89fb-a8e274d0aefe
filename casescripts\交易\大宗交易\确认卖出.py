from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('交易/大宗交易/确认卖出/确认卖出')
def _d71651afaca3e80ec48e9402345ca15e(device, 账号='01028889', 交易密码='123123', 通信密码='123123', 证券代码='600123', 买卖价格='6.25', 交易数量='100', 联系人='123', 联系方式='178', 约定序号='12345', 对方席位号='123', 预期结果='确认卖出'):
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    上滑(device)
    sleep(1)
    点击(device, '/交易/普通交易/普通交易首页/其他交易')
    目录校验(device, '二', '其他交易', '/页面标题')
    点击(device, '/交易/普通交易/其他交易/其他交易首页/大宗交易')
    点击(device, '/交易/普通交易/其他交易/其他交易首页/大宗交易_确认卖出')
    输入文本(device, '/交易/普通交易/其他交易/大宗交易/确认卖出页面/请输入股票代码', 证券代码)
    点击(device, '/交易/普通交易/其他交易/大宗交易/确认卖出页面/选择股份类型按钮')
    点击(device, '/交易/普通交易/其他交易/大宗交易/确认卖出页面/受限股份')
    # 点击('/交易/普通交易/其他交易/大宗交易/确认卖出页面/非受限股份')
    输入文本(device, '/交易/普通交易/其他交易/大宗交易/确认卖出页面/输入委托价格', 交易数量)
    输入文本(device, '/交易/普通交易/其他交易/大宗交易/确认卖出页面/输入委托数量', 交易数量)
    输入文本(device, '/交易/普通交易/其他交易/大宗交易/确认卖出页面/输入约定序号', 约定序号)
    输入文本(device, '/交易/普通交易/其他交易/大宗交易/确认卖出页面/输入对方席位号', 对方席位号)
    # 点击('/交易/普通交易/其他交易/大宗交易/确认卖出页面/卖出按钮')
    实际结果 = 获取文本信息(device, '/页面标题')
    结果 = 预期结果 in 实际结果
    错误校验点 = 非空校验(device, 预期结果, 实际结果)
    return 结果, 错误校验点


