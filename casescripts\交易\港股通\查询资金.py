from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('交易/港股通/查询资金/查询资金')
def _e18e495405700556c46c6b8ae01a2dd0(device, 账号='60000361', 交易密码='123123', 通信密码='123123'):
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/港股通')
    目录校验(device, '二', '港股通', '/页面标题')
    点击(device, '/交易/普通交易/港股通/港股通首页/查询资金')
    总资产 = 获取文本信息(device, '/交易/普通交易/港股通/港股查询资金页面/总资产')
    # print(总资产)
    错误校验点 = 数字校验(device, 总资产)
    结果 = float(总资产) > 0
    return 结果, 错误校验点


