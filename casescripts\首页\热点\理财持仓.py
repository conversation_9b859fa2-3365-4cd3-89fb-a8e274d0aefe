from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('首页/热点/理财持仓/我的理财')
def _0f2d4f0c5735dc58d0bbee12ffa1eedf(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # 业务交易数据 = 获取业务交易信息('交易/普通交易/场内基金/场内申购')
    # 证券代码 = 业务交易数据['证券代码']
    # 初始化
    登录前页面处理(device)
    # time.sleep(15)
    # 交易登录( 账号, 交易密码, 通信密码 )
    # ---------------------------------------------------------#
    点击(device, '/底部导航栏/首页界面跳转按钮')
    目录校验(device, '一', '首页', '/首页/选中状态')
    点击(device, '/首页/热点')
    目录校验(device, '二', '热点', '/首页/热点/选中状态')
    点击(device, '/首页/热点/向右点击按钮')
    点击(device, '/首页/热点/理财持仓')
    登录(device, 账号, 交易密码, 通信密码)
    实际结果 = 获取文本信息(device, '/首页/热点/理财持仓/我的理财/持有金额')
    print(实际结果)
    结果 = float(实际结果) >= 0
    错误校验点 = 数字校验(device, 实际结果)
    return 结果, 错误校验点


@casescript('首页/热点/理财持仓/更多_交易记录')
def _296e1ad95ff8b8f9d6b40a856aa3498e(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # 业务交易数据 = 获取业务交易信息('交易/普通交易/场内基金/场内申购')
    # 证券代码 = 业务交易数据['证券代码']
    # 初始化
    登录前页面处理(device)
    # time.sleep(15)
    # 交易登录( 账号, 交易密码, 通信密码 )
    # ---------------------------------------------------------#
    点击(device, '/底部导航栏/首页界面跳转按钮')
    目录校验(device, '一', '首页', '/首页/选中状态')
    点击(device, '/首页/热点')
    目录校验(device, '二', '热点', '/首页/热点/选中状态')
    点击(device, '/首页/热点/向右点击按钮')
    点击(device, '/首页/热点/理财持仓')
    登录(device, 账号, 交易密码, 通信密码)
    点击(device, '/首页/热点/理财持仓/我的理财/更多')
    点击(device, '/首页/热点/理财持仓/我的理财/更多/交易记录')
    try:
        实际结果 = 获取文本信息(device, '/首页/热点/理财持仓/我的理财/更多/交易记录/无记录')
    except:
        # 有记录情况没有
        pass
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


@casescript('首页/热点/理财持仓/更多_收益明细_已结清产品')
def _9b71924c6b636e7887a718f6f92bede6(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # 业务交易数据 = 获取业务交易信息('交易/普通交易/场内基金/场内申购')
    # 证券代码 = 业务交易数据['证券代码']
    # 初始化
    登录前页面处理(device)
    sleep(15)
    # 交易登录( 账号, 交易密码, 通信密码 )
    # ---------------------------------------------------------#
    点击(device, '/底部导航栏/首页界面跳转按钮')
    目录校验(device, '一', '首页', '/首页/选中状态')
    点击(device, '/首页/热点')
    目录校验(device, '二', '热点', '/首页/热点/选中状态')
    点击(device, '/首页/热点/向右点击按钮')
    点击(device, '/首页/热点/理财持仓')
    登录(device, 账号, 交易密码, 通信密码)
    点击(device, '/首页/热点/理财持仓/我的理财/更多')
    点击(device, '/首页/热点/理财持仓/我的理财/更多/收益明细')
    sleep(3)
    点击(device, '/首页/热点/理财持仓/我的理财/更多/收益明细/已结清产品')
    try:
        实际结果 = 获取文本信息(device, '/首页/热点/理财持仓/我的理财/更多/收益明细/已结清产品/无记录')
    except:
        # 有记录情况没有
        pass
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


@casescript('首页/热点/理财持仓/更多_收益明细_当前持仓产品')
def _04f71ed4d56ad6b166702c12704ed533(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # 业务交易数据 = 获取业务交易信息('交易/普通交易/场内基金/场内申购')
    # 证券代码 = 业务交易数据['证券代码']
    # 初始化
    登录前页面处理(device)
    # time.sleep(15)
    # 交易登录( 账号, 交易密码, 通信密码 )
    # ---------------------------------------------------------#
    点击(device, '/底部导航栏/首页界面跳转按钮')
    目录校验(device, '一', '首页', '/首页/选中状态')
    点击(device, '/首页/热点')
    目录校验(device, '二', '热点', '/首页/热点/选中状态')
    点击(device, '/首页/热点/向右点击按钮')
    点击(device, '/首页/热点/理财持仓')
    登录(device, 账号, 交易密码, 通信密码)
    点击(device, '/首页/热点/理财持仓/我的理财/更多')
    点击(device, '/首页/热点/理财持仓/我的理财/更多/收益明细')
    try:
        实际结果 = 获取文本信息(device, '/交易/普通交易/基金交易/公募及私募基金/公募及私募基金首页/撤销委托/无记录')
    except:
        # 有记录情况没有
        pass
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


@casescript('首页/热点/理财持仓/更多_版本说明')
def _ae7a472f443804476b4facccbbe61569(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # 业务交易数据 = 获取业务交易信息('交易/普通交易/场内基金/场内申购')
    # 证券代码 = 业务交易数据['证券代码']
    # 初始化
    登录前页面处理(device)
    # time.sleep(15)
    # 交易登录( 账号, 交易密码, 通信密码 )
    # ---------------------------------------------------------#
    点击(device, '/底部导航栏/首页界面跳转按钮')
    目录校验(device, '一', '首页', '/首页/选中状态')
    点击(device, '/首页/热点')
    目录校验(device, '二', '热点', '/首页/热点/选中状态')
    点击(device, '/首页/热点/向右点击按钮')
    点击(device, '/首页/热点/理财持仓')
    登录(device, 账号, 交易密码, 通信密码)
    点击(device, '/首页/热点/理财持仓/我的理财/更多')
    点击(device, '/首页/热点/理财持仓/我的理财/更多/版本说明')
    实际结果 = 获取文本信息(device, '/首页/热点/理财持仓/我的理财/更多/版本说明/判断')
    结果 = 实际结果 == '版本选择'
    错误校验点 = 数字校验(device, 实际结果)
    return 结果, 错误校验点


