from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('交易/紫金理财/更多/分红设置')
def _80373bb0ebd645fbbbab9825a63626dd(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    错误校验点 = ''
    # 初始化
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/紫金理财')
    目录校验(device, '二', '紫金理财', '/页面标题')
    点击(device, '/交易/普通交易/紫金理财/紫金理财首页/更多')
    点击(device, '/交易/普通交易/紫金理财/更多/更多首页/分红设置')
    sleep(3)
    点击(device, '/交易/普通交易/基金交易/紫金基金/分红设置页面/请选择产品')
    try:
        实际结果 = 获取文本信息(device, '/交易/普通交易/基金交易/紫金基金/分红设置页面/请选择产品/无记录')
        预期结果 = '亲，暂无持仓'
        错误校验点 = 非空校验(device, 预期结果, 实际结果)
        结果 = 实际结果 == 预期结果
    except:
        点击(device, '/交易/普通交易/基金交易/紫金基金/分红设置/选择产品/第一个产品')
        sleep(1)
        点击(device, '/交易/普通交易/基金交易/紫金基金/分红设置页面/请选择分红方式')
        点击(device, '/交易/普通交易/基金交易/紫金基金/分红设置/分红方式/现金分红')
        点击(device, '/交易/普通交易/基金交易/紫金基金/分红设置页面/确定')
        实际结果 = 获取文本信息(device, '/交易/普通交易/更多/网络投票/投票查询/投票记录详细页面/提示框内容')
        # print("实际结果为", 实际结果)
        预期结果 = '成功'
        结果 = 预期结果 in 实际结果 or '不足' in 实际结果
        错误校验点 = 非空校验(device, 预期结果, 实际结果)
    return 结果, 错误校验点


@casescript('交易/紫金理财/更多/预约申购')
def _bb5c10ac6eabecc0438db5c4e9e8852b(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119', 金额='1'):
    错误校验点 = ''
    # 初始化
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/紫金理财')
    目录校验(device, '二', '紫金理财', '/页面标题')
    点击(device, '/交易/普通交易/紫金理财/紫金理财首页/更多')
    点击(device, '/交易/普通交易/紫金理财/更多/更多首页/预约申购')
    点击(device, '/交易/普通交易/紫金理财/更多/预约申购/预约申购页面/查看可申购产品')
    try:
        实际结果 = 获取文本信息(device, '/交易/普通交易/紫金理财/更多/预约申购/预约申购页面/查看可申购产品/无记录')
        预期结果 = '暂无记录'
        错误校验点 = 非空校验(device, 预期结果, 实际结果)
        结果 = 实际结果 == 预期结果
    except:
        点击(device, '/交易/普通交易/紫金理财/更多/预约申购/预约申购页面/选择第一个产品')
        点击(device, '/交易/普通交易/紫金理财/更多/预约申购/预约申购页面/金额输入框')
        输入文本(device, '/交易/普通交易/紫金理财/更多/预约申购/预约申购页面/金额输入框', 金额)
        点击(device, '/交易/普通交易/紫金理财/更多/预约申购/预约申购页面/随便点')
        点击(device, '/交易/普通交易/紫金理财/更多/预约申购/预约申购页面/确定按钮')
        sleep(3)
        点击(device, '/交易/普通交易/基金交易/公募及私募基金/基金购买/认购申购页面/我已知悉并同意')
        点击(device, '/底部确认/确认按钮')
        try:
            实际结果 = 获取文本信息(device, '/交易/普通交易/基金交易/公募及私募基金/基金购买/认购申购页面/未满足要求')
            点击(device, '/交易/普通交易/基金交易/公募及私募基金/基金购买/认购申购页面/未满足要求确定')
            预期结果 = '暂未满足'
            错误校验点 = 非空校验(device, 预期结果, 实际结果)
            结果 = 预期结果 in 实际结果
        except:
            点击(device, '/交易/普通交易/紫金理财/更多/预约申购/预约申购页面/点勾')
            点击(device, '/交易/普通交易/紫金理财/更多/预约申购/预约申购页面/确认上述已签署内容')
            点击(device, '/交易/普通交易/紫金理财/更多/预约申购/预约申购页面/我已知晓')
            点击(device, '/交易/普通交易/紫金理财/更多/预约申购/预约申购页面/确定')
            点击(device, '/交易/普通交易/紫金理财/更多/预约申购/预约申购页面/下一步')
            点击(device, '/确认/确认按钮')
            sleep(2)
            实际结果 = 获取文本信息(device, '/交易/普通交易/紫金理财/更多/预约申购/结果页面')
            预期结果 = '成功'
            错误校验点 = 非空校验(device, 预期结果, 实际结果)
            结果 = 预期结果 in 实际结果
    return 结果, 错误校验点


@casescript('交易/紫金理财/更多/预约赎回')
def _b4787894879b7c88d516b423352518d8(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119', 金额='1'):
    错误校验点 = ''
    # 初始化
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/紫金理财')
    目录校验(device, '二', '紫金理财', '/页面标题')
    点击(device, '/交易/普通交易/紫金理财/紫金理财首页/更多')
    点击(device, '/交易/普通交易/紫金理财/更多/更多首页/预约赎回')
    点击(device, '/交易/普通交易/紫金理财/更多/预约赎回页面/查看可赎回产品')
    try:
        实际结果 = 获取文本信息(device, '/交易/普通交易/紫金理财/更多/预约赎回页面/查看可赎回产品/无记录')
        预期结果 = '暂无记录'
        错误校验点 = 非空校验(device, 预期结果, 实际结果)
        结果 = 实际结果 == 预期结果
    except:
        点击(device, '/交易/普通交易/紫金理财/更多/预约申购/预约申购页面/选择第一个产品')
        点击(device, '/交易/普通交易/紫金理财/更多/预约赎回页面/金额输入框')
        输入文本(device, '/交易/普通交易/紫金理财/更多/预约赎回页面/金额输入框', 金额)
        点击(device, '/交易/普通交易/紫金理财/更多/预约赎回/预约赎回页面/随便点')
        点击(device, '/交易/普通交易/紫金理财/更多/预约赎回页面/确定按钮')
        # 获取文本信息('/交易/普通交易/紫金理财/更多/预约赎回页面/系统提示')
        点击(device, '/底部确定/确定按钮')
        实际结果 = 获取文本信息(device, '/交易/普通交易/紫金理财/更多/预约赎回页面/系统提示')
        预期结果 = '成功'
        错误校验点 = 非空校验(device, 预期结果, 实际结果)
        结果 = 预期结果 in 实际结果
        # 点击('/底部确定/确定按钮')
    return 结果, 错误校验点


