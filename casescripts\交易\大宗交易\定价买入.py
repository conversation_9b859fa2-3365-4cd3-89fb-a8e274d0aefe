from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('交易/大宗交易/定价买入/定价买入')
def _f088b30df80f7aefa9b1580c27d4f0aa(device, 账号='01028889', 交易密码='123123', 通信密码='123123', 证券代码='010107', 联系人='123', 联系方式='178', 预期结果='定价买入'):
    # 交易数量 = '100'
    # 买卖价格 = '6.25'
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    点击(device, '/交易/普通交易/普通交易首页/普通交易')
    目录校验(device, '一', '交易', '/交易/选中状态')
    上滑(device)
    sleep(1)
    点击(device, '/交易/普通交易/普通交易首页/其他交易')
    目录校验(device, '二', '其他交易', '/页面标题')
    点击(device, '/交易/普通交易/其他交易/其他交易首页/大宗交易')
    点击(device, '/交易/普通交易/其他交易/其他交易首页/大宗交易_定价买入')
    输入文本(device, '/交易/普通交易/其他交易/大宗交易/定价买入页面/请输入股票代码', 证券代码)
    # 点击('/交易/普通交易/大宗交易/跌停价')
    点击(device, '/系统/加数量')
    #
    输入文本(device, '/交易/普通交易/其他交易/大宗交易/定价买入页面/输入联系人', 联系人)
    输入文本(device, '/交易/普通交易/其他交易/大宗交易/定价买入页面/输入联系方式', 联系方式)
    # 点击('/交易/普通交易/其他交易/大宗交易/定价买入页面/买入按钮')
    # 点击('/交易/普通交易/其他交易/大宗交易/我已知晓并同意签署')
    # 点击('/交易/普通交易/其他交易/大宗交易/确定')
    # 点击('/底部确定/确定按钮')
    # 实际结果 = 获取文本信息('/系统提示')
    实际结果 = 获取文本信息(device, '/页面标题')
    # 预期结果 = '成功'
    结果 = 预期结果 in 实际结果
    错误校验点 = 非空校验(device, 预期结果, 实际结果)
    return 结果, 错误校验点


