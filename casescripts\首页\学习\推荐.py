from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('首页/学习/推荐/相关课程')
def _c7093344aadf70ba0271dce405d0fa0b(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # 初始化
    登录前页面处理(device)
    点击(device, '/底部导航栏/首页界面跳转按钮')
    目录校验(device, '一', '首页', '/首页/选中状态')
    点击(device, '/首页/学习')
    目录校验(device, '二', '学习', '/首页/学习/选中状态')
    sleep(3)
    循环滑动(device, '/首页/学习/推荐', '上')
    点击(device, '/首页/学习/推荐')
    点击(device, '/首页/学习/推荐/第一条记录')
    点击(device, '/首页/学习/推荐/第一条记录/相关课程')
    sleep(2)
    实际结果 = 获取文本信息(device, '/首页/学习/推荐/第一条记录/相关课程/第一个课程名称')
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


@casescript('首页/学习/推荐/课程目录')
def _fb874c42686ac0a457df6045f8d126c0(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # 初始化
    登录前页面处理(device)
    点击(device, '/底部导航栏/首页界面跳转按钮')
    目录校验(device, '一', '首页', '/首页/选中状态')
    点击(device, '/首页/学习')
    目录校验(device, '二', '学习', '/首页/学习/选中状态')
    循环滑动(device, '/首页/学习/推荐', '上')
    点击(device, '/首页/学习/推荐')
    点击(device, '/首页/学习/推荐/第一条记录')
    点击(device, '/首页/学习/推荐/第一条记录/课程目录')
    实际结果 = 获取文本信息(device, '/首页/学习/推荐/第一条记录/课程目录/第一个课程')
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


@casescript('首页/学习/推荐/课程简介')
def _fe0b95651ea7524e3a5060a169f3845d(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # 初始化
    登录前页面处理(device)
    点击(device, '/底部导航栏/首页界面跳转按钮')
    目录校验(device, '一', '首页', '/首页/选中状态')
    点击(device, '/首页/学习')
    目录校验(device, '二', '学习', '/首页/学习/选中状态')
    循环滑动(device, '/首页/学习/推荐', '上')
    点击(device, '/首页/学习/推荐')
    点击(device, '/首页/学习/推荐/第一条记录')
    点击(device, '/首页/学习/推荐/第一条记录/课程简介')
    实际结果 = 获取文本信息(device, '/首页/学习/推荐/第一条记录/课程简介/内容')
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


