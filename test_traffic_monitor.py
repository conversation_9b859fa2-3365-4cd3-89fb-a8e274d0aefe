#!/usr/bin/env python3
"""
流量监控功能测试脚本
"""

import sys
import os
import time

# 添加项目路径到sys.path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from libs.traffic_monitor import TrafficMonitor, TrafficData, get_traffic_monitor
from libs.cache import caches


def test_traffic_data():
    """测试TrafficData类"""
    print("=== 测试TrafficData类 ===")

    # 创建测试数据
    data1 = TrafficData(rx_bytes=1000, tx_bytes=500)
    data2 = TrafficData(rx_bytes=1500, tx_bytes=800)

    print(f"数据1: 接收={data1.rx_bytes}, 发送={data1.tx_bytes}, 总计={data1.total_bytes}")
    print(f"数据2: 接收={data2.rx_bytes}, 发送={data2.tx_bytes}, 总计={data2.total_bytes}")

    # 测试差值计算
    diff = data2 - data1
    print(f"差值: 接收={diff.rx_bytes}, 发送={diff.tx_bytes}, 总计={diff.total_bytes}")

    # 测试转换为字典
    print(f"字典格式: {data1.to_dict()}")
    print()


def test_traffic_monitor_basic():
    """测试TrafficMonitor基本功能"""
    print("=== 测试TrafficMonitor基本功能 ===")

    # 设置测试配置
    caches.set('config.TRAFFIC_MONITOR_ENABLED', 'true', 3600)
    caches.set('config.TRAFFIC_LOG_ENABLED', 'true', 3600)
    caches.set('config.TRAFFIC_UPLOAD_ENABLED', 'false', 3600)

    # 创建监控器
    device_id = "test_device_123"
    monitor = TrafficMonitor(device_id)

    print(f"监控器已创建，设备ID: {device_id}")
    print(f"监控启用状态: {monitor.enabled}")
    print(f"日志启用状态: {monitor.log_enabled}")
    print(f"上报启用状态: {monitor.upload_enabled}")

    # 测试获取当前流量（可能会失败，因为没有真实设备）
    current_traffic = monitor.get_current_traffic()
    if current_traffic:
        print(f"当前流量: {current_traffic.to_dict()}")
    else:
        print("无法获取当前流量（正常，因为没有连接真实设备）")

    print()


def test_traffic_monitor_simulation():
    """模拟测试流量监控流程"""
    print("=== 模拟测试流量监控流程 ===")

    # 设置测试配置
    caches.set('config.TRAFFIC_MONITOR_ENABLED', 'true', 3600)
    caches.set('config.TRAFFIC_LOG_ENABLED', 'true', 3600)

    device_id = "test_device_456"
    monitor = TrafficMonitor(device_id)

    # 模拟开始监控
    operation_name = "点击"
    control_path = "/测试/按钮"

    print(f"开始监控操作: {operation_name} {control_path}")
    monitor_id = monitor.start_monitoring(operation_name, control_path)
    print(f"监控ID: {monitor_id}")

    # 模拟操作执行时间
    time.sleep(1)

    # 模拟停止监控（由于没有真实设备，这里会返回None）
    result = monitor.stop_monitoring(monitor_id)
    if result:
        print(f"监控结果: {result}")
    else:
        print("监控结果为空（正常，因为没有连接真实设备）")

    print()


def test_get_traffic_monitor():
    """测试全局监控器获取函数"""
    print("=== 测试全局监控器获取函数 ===")

    device_id = "test_device_789"

    # 第一次获取
    monitor1 = get_traffic_monitor(device_id)
    print(f"第一次获取监控器: {id(monitor1)}")

    # 第二次获取（应该是同一个实例）
    monitor2 = get_traffic_monitor(device_id)
    print(f"第二次获取监控器: {id(monitor2)}")

    print(f"是否为同一实例: {monitor1 is monitor2}")
    print()


def test_decorator_simulation():
    """模拟测试装饰器功能"""
    print("=== 模拟测试装饰器功能 ===")

    from libs.traffic_monitor import traffic_monitor_decorator

    # 模拟设备类
    class MockDevice:
        def __init__(self, device_id):
            self.device_id = device_id

    # 使用装饰器装饰的模拟函数
    @traffic_monitor_decorator('模拟点击')
    def mock_click(device, path):
        print(f"执行模拟点击操作: {path}")
        time.sleep(0.5)  # 模拟操作耗时
        return "点击成功"

    # 设置测试配置
    caches.set('config.TRAFFIC_MONITOR_ENABLED', 'true', 3600)

    # 创建模拟设备
    device = MockDevice("mock_device_001")

    # 执行装饰的函数
    result = mock_click(device, "/模拟/按钮")
    print(f"函数返回结果: {result}")
    print()


def main():
    """主测试函数"""
    print("流量监控功能测试开始")
    print("=" * 50)

    try:
        test_traffic_data()
        test_traffic_monitor_basic()
        test_traffic_monitor_simulation()
        test_get_traffic_monitor()
        test_decorator_simulation()

        print("=" * 50)
        print("所有测试完成！")

    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()