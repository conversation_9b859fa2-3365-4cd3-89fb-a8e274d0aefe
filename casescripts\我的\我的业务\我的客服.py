from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('我的/我的业务/我的客服/密码修改_修改交易密码')
def _895c771afbb467aaa810bc95bfef44c0(device, 账号='666630297341', 旧交易密码='968892', 通信密码='715394', 新交易密码='142569', 第一次预期结果='成功', 第二次预期结果='成功'):
    # fsc
    登录前页面处理(device)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    目录校验(device, '一', '我的', '/我的/选中状态')
    上滑(device)
    点击(device, '/我的/我的客服')
    目录校验(device, '二', '我的客服', '/我的/我的客服/选中状态')
    点击(device, '/我的/我的客服/办业务')
    点击(device, '/我的/办业务/修改交易密码')
    登录(device, 账号, 旧交易密码, 通信密码)
    # 点击( '/首页/热点/我的资产/密码修改/修改交易密码/输入当前密码')
    输入文本(device, '/首页/热点/我的资产/密码修改/修改交易密码/输入当前密码', 旧交易密码)
    输入文本(device, '/首页/热点/我的资产/密码修改/修改交易密码/输入新密码', 新交易密码)
    输入文本(device, '/首页/热点/我的资产/密码修改/修改交易密码/再次输入新密码', 新交易密码)
    点击(device, '/首页/热点/我的资产/密码修改/修改交易密码/显示密码字样')
    点击(device, '/首页/热点/我的资产/密码修改/修改交易密码/确认修改')
    点击(device, '/首页/热点/我的资产/密码修改/修改交易密码/修改密码/确定')
    第一次修改结果 = 获取文本信息(device, '/首页/热点/我的资产/密码修改/修改交易密码/系统提示/结果')
    print('第一次修改结果', 第一次修改结果)
    第一次结果 = 第一次预期结果 in 第一次修改结果
    # 全局变量.错误校验点 = 非空校验(第一次预期结果, 第一次修改结果)
    # print('结果', 结果)
    # 结果校验(第一次结果)
    点击(device, '/首页/热点/我的资产/密码修改/修改交易密码/系统提示/确定')
    # 将密码改回来
    输入文本(device, '/首页/热点/我的资产/密码修改/修改交易密码/输入当前密码', 新交易密码)
    输入文本(device, '/首页/热点/我的资产/密码修改/修改交易密码/输入新密码', 旧交易密码)
    输入文本(device, '/首页/热点/我的资产/密码修改/修改交易密码/再次输入新密码', 旧交易密码)
    点击(device, '/首页/热点/我的资产/密码修改/修改交易密码/显示密码字样')
    点击(device, '/首页/热点/我的资产/密码修改/修改交易密码/确认修改')
    点击(device, '/首页/热点/我的资产/密码修改/修改交易密码/修改密码/确定')
    第二次修改结果 = 获取文本信息(device, '/首页/热点/我的资产/密码修改/修改交易密码/系统提示/结果')
    print('第二次修改结果', 第二次修改结果)
    第二次结果 = 第一次预期结果 in 第一次修改结果 and 第二次预期结果 in 第二次修改结果
    # print('结果', 结果)
    结果 = 第一次结果 and 第二次结果
    错误校验点 = 两次校验(device, 第一次预期结果, 第一次修改结果, 第二次预期结果, 第二次修改结果)
    return 结果, 错误校验点
    点击(device, '/首页/热点/我的资产/密码修改/修改交易密码/系统提示/确定')


@casescript('我的/我的业务/我的客服/密码修改_修改资金密码')
def _aa9f0848f6ffea2578b5b5e692ed69c8(device, 账号='666630297341', 交易密码='968892', 通信密码='715394', 旧资金密码='684268', 新资金密码='137950', 第一次预期结果='成功', 第二次预期结果='成功'):
    # fsc
    登录前页面处理(device)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    目录校验(device, '一', '我的', '/我的/选中状态')
    上滑(device)
    点击(device, '/我的/我的客服')
    目录校验(device, '二', '我的客服', '/我的/我的客服/选中状态')
    点击(device, '/我的/我的客服/办业务')
    点击(device, '/我的/办业务/修改资金密码')
    登录(device, 账号, 交易密码, 通信密码)
    # 点击( '/首页/热点/我的资产/密码修改/修改交易密码/输入当前密码')
    输入文本(device, '/首页/热点/我的资产/密码修改/修改交易密码/输入当前密码', 旧资金密码)
    输入文本(device, '/首页/热点/我的资产/密码修改/修改交易密码/输入新密码', 新资金密码)
    输入文本(device, '/首页/热点/我的资产/密码修改/修改交易密码/再次输入新密码', 新资金密码)
    点击(device, '/首页/热点/我的资产/密码修改/修改交易密码/显示密码字样')
    点击(device, '/首页/热点/我的资产/密码修改/修改交易密码/确认修改')
    点击(device, '/首页/热点/我的资产/密码修改/修改交易密码/修改密码/确定')
    第一次实际结果 = 获取文本信息(device, '/首页/热点/我的资产/密码修改/修改交易密码/系统提示/结果')
    # print('实际结果', 第一次实际结果)
    第一次结果 = 第一次预期结果 in 第一次实际结果
    # print('结果', 结果)
    # 全局变量.错误校验点 = 非空校验(第一次预期结果,第一次实际结果)
    # 结果校验(第一次结果)
    点击(device, '/首页/热点/我的资产/密码修改/修改交易密码/系统提示/确定')
    输入文本(device, '/首页/热点/我的资产/密码修改/修改交易密码/输入当前密码', 新资金密码)
    输入文本(device, '/首页/热点/我的资产/密码修改/修改交易密码/输入新密码', 旧资金密码)
    输入文本(device, '/首页/热点/我的资产/密码修改/修改交易密码/再次输入新密码', 旧资金密码)
    点击(device, '/首页/热点/我的资产/密码修改/修改交易密码/显示密码字样')
    点击(device, '/首页/热点/我的资产/密码修改/修改交易密码/确认修改')
    点击(device, '/首页/热点/我的资产/密码修改/修改交易密码/修改密码/确定')
    第二次实际结果 = 获取文本信息(device, '/首页/热点/我的资产/密码修改/修改交易密码/系统提示/结果')
    第二次结果 = 第二次预期结果 in 第二次实际结果
    结果 = 第一次结果 and 第二次结果
    错误校验点 = 两次校验(device, 第一次预期结果, 第一次实际结果, 第二次预期结果, 第二次实际结果)
    return 结果, 错误校验点
    点击(device, '/首页/热点/我的资产/密码修改/修改交易密码/系统提示/确定')


@casescript('我的/我的业务/我的客服/密码修改_修改通讯密码')
def _2f5feb87c54ba939ea601e9c436b26c7(device, 账号='666630297341', 交易密码='968892', 旧通信密码='715394', 新通信密码='298869', 第一次预期结果='成功', 第二次预期结果='成功'):
    # fsc
    登录前页面处理(device)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    目录校验(device, '一', '我的', '/我的/选中状态')
    上滑(device)
    点击(device, '/我的/我的客服')
    目录校验(device, '二', '我的客服', '/我的/我的客服/选中状态')
    点击(device, '/我的/我的客服/办业务')
    点击(device, '/我的/办业务/修改通讯密码')
    登录(device, 账号, 交易密码, 旧通信密码)
    # 点击( '/首页/热点/我的资产/密码修改/修改交易密码/输入当前密码')
    输入文本(device, '/首页/热点/我的资产/密码修改/修改交易密码/输入当前密码', 旧通信密码)
    输入文本(device, '/首页/热点/我的资产/密码修改/修改交易密码/输入新密码', 新通信密码)
    输入文本(device, '/首页/热点/我的资产/密码修改/修改交易密码/再次输入新密码', 新通信密码)
    点击(device, '/首页/热点/我的资产/密码修改/修改交易密码/显示密码字样')
    点击(device, '/首页/热点/我的资产/密码修改/修改交易密码/确认修改')
    点击(device, '/首页/热点/我的资产/密码修改/修改交易密码/修改密码/确定')
    第一次实际结果 = 获取文本信息(device, '/首页/热点/我的资产/密码修改/修改交易密码/系统提示/结果')
    print('实际结果', 第一次实际结果)
    第一次结果 = 第一次预期结果 in 第一次实际结果
    # print('结果', 第一次结果)
    # 全局变量.错误校验点 = 非空校验(第一次预期结果, 第一次实际结果)
    # 结果校验(第一次结果)
    点击(device, '/首页/热点/我的资产/密码修改/修改交易密码/系统提示/确定')
    # 把密码改回来
    输入文本(device, '/首页/热点/我的资产/密码修改/修改交易密码/输入当前密码', 新通信密码)
    输入文本(device, '/首页/热点/我的资产/密码修改/修改交易密码/输入新密码', 旧通信密码)
    输入文本(device, '/首页/热点/我的资产/密码修改/修改交易密码/再次输入新密码', 旧通信密码)
    点击(device, '/首页/热点/我的资产/密码修改/修改交易密码/显示密码字样')
    点击(device, '/首页/热点/我的资产/密码修改/修改交易密码/确认修改')
    点击(device, '/首页/热点/我的资产/密码修改/修改交易密码/修改密码/确定')
    第二次实际结果 = 获取文本信息(device, '/首页/热点/我的资产/密码修改/修改交易密码/系统提示/结果')
    # print('实际结果', 第二次实际结果)
    第二次结果 = 第二次预期结果 in 第二次实际结果
    # print('结果', 第一次结果)
    结果 = 第一次结果 and 第二次结果
    错误校验点 = 两次校验(device, 第一次预期结果, 第一次实际结果, 第二次预期结果, 第二次实际结果)
    return 结果, 错误校验点
    点击(device, '/首页/热点/我的资产/密码修改/修改交易密码/系统提示/确定')


@casescript('我的/我的业务/我的客服/密码修改_忘记交易密码')
def _ec5e27e7f529c502d8d6023ed649a597(device, 账号='666630296771', 交易密码='124369', 通信密码='159753', 姓名='贺晓飞', 身份证号码='320802199506012511'):
    # fsc
    登录前页面处理(device)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    目录校验(device, '一', '我的', '/我的/选中状态')
    上滑(device)
    点击(device, '/我的/我的客服')
    目录校验(device, '二', '我的客服', '/我的/我的客服/选中状态')
    点击(device, '/我的/我的客服/办业务')
    点击(device, '/我的/我的业务/忘记交易密码')
    # 登录(账号, 交易密码, 通信密码)
    # 点击( '/首页/热点/我的资产/密码修改/修改交易
    点击(device, '/首页/热点/我的资产/密码修改/重置资金密码/下一步')
    输入文本(device, '/我的/我的业务/重置密码/姓名', 姓名)
    输入文本(device, '/我的/我的业务/重置密码/身份证号码', 身份证号码)
    输入文本(device, '/我的/我的业务/重置密码/客户号', 账号)
    点击(device, '/首页/热点/我的资产/密码修改/重置资金密码/下一步')
    点击(device, '/业务办理须知/勾选框')
    点击(device, '/交易/普通交易/公用页面/不适当性确认书页面/确认按钮')
    sleep(10)
    # 点击('/首页/热点/我的资产/密码修改/重置资金密码/下一步')
    点击(device, '/首页/热点/我的资产/密码修改/重置资金密码/验证银证转账金额')
    点击(device, '/首页/热点/我的资产/密码修改/重置资金密码/输入金额')
    实际结果 = 获取文本信息(device, '/页面标题')
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点
    # 点击('/首页/热点/我的资产/密码修改/修改交易密码/系统提示/确定')


@casescript('我的/我的业务/我的客服/密码修改_忘记资金密码')
def _9335f665fe70726c996c6e2c6db8f84f(device, 账号='666630297341', 交易密码='968892', 通信密码='715394', 旧资金密码='684268', 新资金密码='137950'):
    # fsc
    登录前页面处理(device)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    目录校验(device, '一', '我的', '/我的/选中状态')
    上滑(device)
    点击(device, '/我的/我的客服')
    目录校验(device, '二', '我的客服', '/我的/我的客服/选中状态')
    点击(device, '/我的/我的客服/办业务')
    点击(device, '/我的/我的业务/忘记资金密码')
    登录(device, 账号, 交易密码, 通信密码)
    点击(device, '/业务办理须知/勾选框')
    点击(device, '/交易/普通交易/公用页面/不适当性确认书页面/确认按钮')
    sleep(10)
    点击(device, '/首页/热点/我的资产/密码修改/重置资金密码/下一步')
    点击(device, '/首页/热点/我的资产/密码修改/重置资金密码/验证银证转账金额')
    点击(device, '/首页/热点/我的资产/密码修改/重置资金密码/输入金额')
    # 点击( '/首页/热点/我的资产/密码修改/修改交易
    实际结果 = 获取文本信息(device, '/页面标题')
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


@casescript('我的/我的业务/我的客服/密码修改_忘记通讯密码')
def _ab542a6e142e915f1c62f8e01e10bd24(device, 账号='666630296771', 交易密码='124369', 通信密码='159753', 身份证号码='320802199506012511', 姓名='贺晓飞'):
    # fsc
    # 身份证号码 = '320802199506012511'
    登录前页面处理(device)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    目录校验(device, '一', '我的', '/我的/选中状态')
    上滑(device)
    点击(device, '/我的/我的客服')
    目录校验(device, '二', '我的客服', '/我的/我的客服/选中状态')
    点击(device, '/我的/我的客服/办业务')
    点击(device, '/我的/我的业务/忘记通讯密码')
    # 登录(账号, 交易密码, 通信密码)
    # 点击( '/首页/热点/我的资产/密码修改/修改交易
    输入文本(device, '/首页/热点/我的资产/密码修改/重置通讯密码/客户号', 账号)
    输入文本(device, '/首页/热点/我的资产/密码修改/重置通讯密码/身份证号', 身份证号码)
    点击(device, '/首页/热点/我的资产/密码修改/重置通讯密码/点击确定')
    点击(device, '/首页/热点/我的资产/密码修改/重置通讯密码/下一步')
    点击(device, '/首页/热点/我的资产/密码修改/重置通讯密码/下一步')
    实际结果 = 获取文本信息(device, '/页面标题')
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点
    # 点击('/首页/热点/我的资产/密码修改/修改交易密码/系统提示/确定')


@casescript('我的/我的业务/我的客服/常见问题')
def _9e36ef9d04e7e0f25233bacbae6cf2c2(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    登录前页面处理(device)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    目录校验(device, '一', '我的', '/我的/选中状态')
    上滑(device)
    点击(device, '/我的/我的客服')
    目录校验(device, '二', '我的客服', '/我的/我的客服/选中状态')
    点击(device, '我的/我的业务/我的客服/常见问题更多')
    if 获取控件对象(device, '/交易/交易登录页面/登陆按钮') != '':
        登录(device, 账号, 交易密码, 通信密码)
    else:
        print('已登录！')
    # time.sleep(5)
    实际结果 = 获取文本信息(device, '我的/我的业务/我的客服/常见问题更多/第一条')
    print(实际结果)
    点击(device, '我的/我的业务/我的客服/常见问题更多/第一条')
    预期结果 = 获取文本信息(device, '我的/我的业务/我的客服/常见问题更多/第一条/标题')
    print(预期结果)
    错误校验点 = 非空校验(device, 预期结果, 实际结果)
    结果 = 实际结果 == 预期结果
    return 结果, 错误校验点


@casescript('我的/我的业务/我的客服/投资学堂')
def _c65c380f79311e96f2c5e8c11165ec3a(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119', 预期结果='投资学堂'):
    登录前页面处理(device)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    目录校验(device, '一', '我的', '/我的/选中状态')
    上滑(device)
    点击(device, '/我的/我的客服')
    目录校验(device, '二', '我的客服', '/我的/我的客服/选中状态')
    # 点击('我的/我的业务/我的客服')
    滑动控件至屏幕内(device, '我的/我的业务/我的客服/投资学堂更多')
    点击(device, '我的/我的业务/我的客服/投资学堂更多')
    # 登录前页面处理()
    # 登录(账号, 交易密码, 通信密码)
    # time.sleep(5)为空校验
    实际结果 = 获取文本信息(device, '我的/我的业务/我的客服/投资学堂更多/标题')
    print(实际结果)
    错误校验点 = 非空校验(device, 预期结果, 实际结果)
    结果 = 实际结果 == 预期结果
    return 结果, 错误校验点


@casescript('我的/我的业务/我的客服/涨乐特色业务介绍')
def _a32f7b1346c9b1358a333a9f55ad51be(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119', 预期结果='特色业务介绍'):
    登录前页面处理(device)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    目录校验(device, '一', '我的', '/我的/选中状态')
    上滑(device)
    点击(device, '/我的/我的客服')
    目录校验(device, '二', '我的客服', '/我的/我的客服/选中状态')
    # 上滑()
    # time.sleep(2)
    点击(device, '我的/我的业务/我的客服/涨乐特色业务介绍更多')
    # 登录前页面处理()
    # 登录(账号, 交易密码, 通信密码)
    # time.sleep(5)
    实际结果 = 获取文本信息(device, '我的/我的业务/我的客服/涨乐特色业务介绍更多/标题')
    print(实际结果)
    错误校验点 = 非空校验(device, 预期结果, 实际结果)
    结果 = 实际结果 == 预期结果
    return 结果, 错误校验点


@casescript('我的/我的业务/我的客服/网上开户')
def _7c2e3a44154f8b02d5ca85fbc818e220(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119', 预期结果='一键验证'):
    登录前页面处理(device)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    目录校验(device, '一', '我的', '/我的/选中状态')
    上滑(device)
    点击(device, '/我的/我的客服')
    目录校验(device, '二', '我的客服', '/我的/我的客服/选中状态')
    # 点击网上开户
    点击(device, "/我的/我的业务/我的客服/网上开户")
    点击(device, "/我的/我的业务/我的客服/网上开户/立即开户")
    实际结果 = 获取文本信息(device, '/我的/我的业务/我的客服/网上开户/立即开户/一键验证')
    print(实际结果)
    错误校验点 = 非空校验(device, 预期结果, 实际结果)
    结果 = 实际结果 == 预期结果
    return 结果, 错误校验点


