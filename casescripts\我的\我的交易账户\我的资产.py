from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('我的/我的交易账户/我的资产/我的资产')
def _f414d4b164770e17d482ee9c39e69276(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    错误校验点 = ''
    # 初始化
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    目录校验(device, '一', '我的', '/我的/选中状态')
    点击(device, '/我的/我的交易账户/我的资产/展示更多按钮')
    目录校验(device, '二', '账户', '/我的/我的交易账户/页面标题')
    结果 = False
    if 获取控件对象(device, '/我的/我的交易账户/网络不通') != '':
        错误校验点 = 获取文本信息(device, '/我的/我的交易账户/网络不通')
    else:
        账户 = 获取文本信息(device, '/我的/我的交易账户/我的资产/账户姓名')
        总资产 = 获取文本信息(device, '/我的/我的交易账户/我的资产/总资产')
        预估总资产 = 获取文本信息(device, '/我的/我的交易账户/我的资产/预估总资产')
        现金 = 获取文本信息(device, '/我的/我的交易账户/我的资产/现金')
        股票 = 获取文本信息(device, '/我的/我的交易账户/我的资产/股票')
        理财 = 获取文本信息(device, '/我的/我的交易账户/我的资产/理财')
        基金 = 获取文本信息(device, '/我的/我的交易账户/我的资产/基金')
        债券 = 获取文本信息(device, '/我的/我的交易账户/我的资产/债券')
        结果 = 账户 != '' and 总资产 != '' and 预估总资产 != '' and 现金 != '' and 股票 != '' and 理财 != '' and 基金 != '' and 债券 != ''
        错误校验点 = '账户：%s，总资产：%s，预估总资产：%s，现金：%s，股票：%s，理财：%s，基金：%s，债券：%s' %(账户,总资产,预估总资产,现金,股票,理财,基金,债券)
    截屏(device)
    print('结果', 结果)
    return 结果, 错误校验点


