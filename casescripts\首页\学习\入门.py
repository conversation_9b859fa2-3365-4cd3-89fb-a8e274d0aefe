from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('首页/学习/入门/入门')
def _93524a2cd467ed3e0cbf177f7fdca44b(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # 初始化
    登录前页面处理(device)
    点击(device, '/底部导航栏/首页界面跳转按钮')
    目录校验(device, '一', '首页', '/首页/选中状态')
    点击(device, '/首页/学习')
    目录校验(device, '二', '学习', '/首页/学习/选中状态')
    # time.sleep(3)
    # 上滑()
    循环滑动(device, '/首页/学习/入门', '上')
    点击(device, '/首页/学习/入门')
    实际结果 = 获取控件对象(device, '/首页/学习/入门/第一条记录')
    # ( '/首页/学习/推荐/第一条记录/课程目录/第一个课程')
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


