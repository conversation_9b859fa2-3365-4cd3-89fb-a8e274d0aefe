from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('交易/普通交易/设置交易默认的股东号/设置主证券账户')
def _116032a8ac6a46b3f0ac6369d5685c3f(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119', 预期状态='正常'):
    错误校验点 = ''
    # 初始化
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/普通交易')
    点击(device, '/交易/普通交易/普通交易首页/更多')
    目录校验(device, '二', '股票交易', '/页面标题')
    点击(device, '/交易/普通交易/更多/更多交易页面/设置交易默认的股东号')
    # 分别判断上海、深圳账户状态是否正常
    上海账户状态 = 获取文本信息(device, '/交易/普通交易/更多/设置交易默认的股东号/深圳股东账户/状态')
    if 预期状态 != 上海账户状态:
        错误校验点 = 非空校验(device, '上海账户状态预期为' + 预期状态, '上海账户实际预期为' + 上海账户状态)
    点击(device, '/交易/普通交易/更多/设置交易默认的股东号/深圳股东账户')
    深圳账户状态 = 获取文本信息(device, '/交易/普通交易/更多/设置交易默认的股东号/深圳股东账户/状态')
    if 预期状态 != 深圳账户状态 and 错误校验点 == '':
        错误校验点 = 非空校验(device, '深圳账户状态预期为' + 预期状态, '深圳账户实际预期为' + 深圳账户状态)
    结果 = 预期状态 == 上海账户状态 and 预期状态 == 深圳账户状态
    return 结果, 错误校验点


