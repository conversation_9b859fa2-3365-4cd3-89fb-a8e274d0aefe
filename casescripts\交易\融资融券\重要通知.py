from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('交易/融资融券/重要通知/重要通知')
def _b97a15217f7b4ac8d112115a71e57fa1(device, 账号='30002187', 交易密码='123123', 通信密码='123123'):
    错误校验点 = ''
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/融资融券')
    点击(device, '/交易/融资融券/重要通知')
    目录校验(device, '二', '通知', '/页面标题')
    结果 = False
    if 获取控件对象(device, '/登陆/系统提示'):
        print('异常提示！！！')
        为空校验(device)
        错误校验点 = 获取文本信息(device, '/登陆/系统提示内容')
        点击(device, '/提示确认/确认按钮')
    else:
        预期结果 = '暂无'
        实际结果 = 获取文本信息(device, '/交易/融资融券/重要通知/暂无通知')
        结果 = 预期结果 in 实际结果
    return 结果, 错误校验点


