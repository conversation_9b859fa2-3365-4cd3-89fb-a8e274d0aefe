from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('我的/我的服务记录/在线客服')
def _d79783ad6955c50f90eba1ce05b72638(device, 账号='666630296771', 交易密码='124369', 通信密码='159753'):
    登录前页面处理(device)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    上滑(device)
    # time.sleep(3)
    点击(device, '/我的/我的服务记录')
    登录(device, 账号, 交易密码, 通信密码)
    点击(device, '/我的/我的服务记录/在线客服')
    if 获取控件对象(device, '/我的/我的服务记录/在线客服/第一条记录'):
        实际结果 = 获取控件对象(device, '/我的/我的服务记录/在线客服/第一条记录')
    else:
        实际结果 = 获取控件对象(device, '/我的/我的服务记录/在线客服/无记录')
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


@casescript('我的/我的服务记录/投顾服务')
def _54ab3c4cf795575b928ceb3322f10cc7(device, 账号='666630296771', 交易密码='124369', 通信密码='159753'):
    登录前页面处理(device)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    上滑(device)
    点击(device, '/我的/我的服务记录')
    登录(device, 账号, 交易密码, 通信密码)
    点击(device, '/我的/我的服务记录/投顾服务')
    if 获取控件对象(device, '/我的/我的服务记录/投顾服务/第一条记录'):
        实际结果 = 获取控件对象(device, '/我的/我的服务记录/投顾服务/第一条记录')
    else:
        实际结果 = 获取控件对象(device, '/我的/我的服务记录/投顾服务/无记录')
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


@casescript('我的/我的服务记录/电话客服')
def _299c698f937f641ef9f6662366f9e212(device, 账号='666630296771', 交易密码='124369', 通信密码='159753'):
    登录前页面处理(device)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    上滑(device)
    点击(device, '/我的/我的服务记录')
    登录(device, 账号, 交易密码, 通信密码)
    点击(device, '/我的/我的服务记录/电话客服')
    if 获取控件对象(device, '/我的/我的服务记录/电话客服/第一条记录'):
        实际结果 = 获取控件对象(device, '/我的/我的服务记录/电话客服/第一条记录')
    else:
        实际结果 = 获取控件对象(device, '/我的/我的服务记录/电话客服/无记录')
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


@casescript('我的/我的服务记录/营业网点')
def _43661afbe16661de94b496b1e564f55a(device, 账号='666630296771', 交易密码='124369', 通信密码='159753'):
    登录前页面处理(device)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    上滑(device)
    点击(device, '/我的/我的服务记录')
    登录(device, 账号, 交易密码, 通信密码)
    点击(device, '/我的/我的服务记录/营业网点')
    if 获取控件对象(device, '/我的/我的服务记录/营业网点/第一条记录'):
        实际结果 = 获取控件对象(device, '/我的/我的服务记录/营业网点/第一条记录')
    else:
        实际结果 = 获取控件对象(device, '/我的/我的服务记录/营业网点/无记录')
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


