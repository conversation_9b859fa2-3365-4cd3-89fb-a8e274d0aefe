from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('首页/学习/高手/高手')
def _87f2e7581e58c97193b2d3aa83ec4fcb(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # 初始化
    登录前页面处理(device)
    点击(device, '/底部导航栏/首页界面跳转按钮')
    目录校验(device, '一', '首页', '/首页/选中状态')
    点击(device, '/首页/学习')
    目录校验(device, '二', '学习', '/首页/学习/选中状态')
    循环滑动(device, '/首页/学习/高手', '上')
    # time.sleep(3)
    # 上滑()
    点击(device, '/首页/学习/高手')
    实际结果 = 获取控件对象(device, '/首页/学习/高手/第一条记录')
    # ( '/首页/学习/推荐/第一条记录/课程目录/第一个课程')
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


