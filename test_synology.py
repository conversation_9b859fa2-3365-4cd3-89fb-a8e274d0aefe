import os
from datetime import datetime

import requests

account = "cps_ax"
password = "QsgR>Oj6"

dt = datetime.now()
file_pre = '%s/%s/%s' % (dt.year, dt.month, dt.day)

server = 'http://222.190.120.3:5000'
local_dir = 'D:/图片/'
local_file = '7bi3.jpg'
local_video = 'test2.mp4'
nas_video_dir = '/cps_ax/video/' + file_pre
nas_flie_dir = '/cps_ax/screenshots/' + file_pre
nas_file = local_file.rsplit('/', 1)[-1]


def login():
    print("登录")
    url = server + '/webapi/auth.cgi?api=SYNO.API.Auth&version=3&method=login&account=%s&passwd=%s&session=FileStation&format=cookie' % (account, password)
    req = requests.request('GET', url)
    if req.json()['success']:
        print("登录成功")
        sid = req.json()['data']['sid']
        print(sid)
        return sid
    else:
        print('登录失败')
        return ''


def logout(sid):
    print('注销...')
    uri = server + '/webapi/auth.cgi?api=SYNO.API.Auth&version=3&method=logout&session=FileStation&_sid=' + sid
    req = requests.request('GET', uri)
    print(req.text)


# 下载文件
def download(sid):
    print('下载...')
    path = nas_flie_dir + "/" + nas_file
    path = path.replace("/", '%2F')
    print(path)
    # uri = server + r'/webapi/entry.cgi?api=SYNO.FileStation.Download&version=2&method=download&path=%s&mode=%s&sid=%s&&_sid=%s' \
    #       % (path, '%22open%22', sid, sid)
    # print(uri)
    headers = {
        'Content-Type': 'image/jpeg'
    }
    uri = server + '/webapi/entry.cgi?api=SYNO.FileStation.Download&version=2&method=download&' \
                   'path=%5B%22' + path + '%22%5D&mode=%22open%22&_sid=' + sid
    print(uri)
    req = requests.request('GET', uri, headers=headers)
    # req = requests.request('GET', uri)
    print(req)
    if not req.content.startswith(b'<!DOCTYPE html>'):
        print(req.text)
        with open(local_dir + nas_file, 'wb') as f:
            f.write(req.content)
    else:
        print('文件：%s 下载失败' % nas_file)


# 上传文件

def upload(sid):
    print('本地上传.')
    try:
        with open(os.path.join(local_dir, local_file), 'rb') as payload:
            args = {
                'path': nas_flie_dir,
                'create_parents': 'true',
                'overwrite': 'true'
            }
            files = {'file': (local_file, payload, 'application/octet-stream')}
            uri = server + r'/webapi/entry.cgi?api=SYNO.FileStation.Upload&version=2&method=upload&_sid=' + sid
            print(uri)
            req = requests.post(uri, data=args, files=files, verify=True).json()
            if req.get('success'):
                print('上传成功')
            else:
                print('上传失败:%s' % req)
    except Exception as e:
        print('上传%s出错：%s' % (local_file, e))
# 上传文件

def upload2(sid):
    print('本地上传.')
    try:
        with open(os.path.join(local_dir, local_video), 'rb') as payload:
            args = {
                'path': nas_video_dir,
                'create_parents': 'true',
                'overwrite': 'true'
            }
            files = {'file': (local_video, payload, 'application/octet-stream')}
            uri = server + r'/webapi/entry.cgi?api=SYNO.FileStation.Upload&version=2&method=upload&_sid=' + sid
            print(uri)
            req = requests.post(uri, data=args, files=files, verify=True).json()
            if req.get('success'):
                print('上传成功')
            else:
                print('上传失败:%s' % req)
    except Exception as e:
        print('上传%s出错：%s' % (local_file, e))


# 下载文件
def download2(sid):
    print('下载...')
    path = nas_video_dir + "/" + local_video
    path = path.replace("/", '%2F')
    print(path)

    headers = {
        'Content-Type': 'audio/mpeg'
    }
    uri = server + '/webapi/entry.cgi?api=SYNO.FileStation.Download&version=2&method=download&' \
                   'path=%5B%22' + path + '%22%5D&mode=%22open%22&_sid=' + sid
    print(uri)
    req = requests.request('GET', uri, headers=headers)
    # req = requests.request('GET', uri)
    print(req)




def sharing(id):
    print('分享...')
    uri = server + '/webapi/entry.cgi?api=SYNO.FileStation.Sharing&version=3&method=getinfo&id=' + id
    req = requests.request('GET', uri)
    print(req)


def list_share(sid):
    print('分享文件列表')
    url = server + '/webapi/entry.cgi?api=SYNO.FileStation.List&version=2&method=list_share&additional=%5B%22real_path%22%2C%22owner%2Ctime%22%5D&_sid=' + sid
    print(url)
    req = requests.request('GET', url)
    print(req.text)


def list(path, sid):
    print('文件列表')
    path = str('' + path.replace("/", "%2f") + '')
    print(path)
    # url = server + '/webapi/entry.cgi?api=SYNO.FileStation.List&version=2&method=list_share&additional=%5B%22real_path%22%2C%22owner%2Ctime%22%5D&_sid=' + sid
    url = server + '/webapi/entry.cgi?api=SYNO.FileStation.List&version=2&method=list&folder_path=%s&_sid=%s' % (path, sid)
    print(url)
    req = requests.request('GET', url)
    print(req.text)


if __name__ == '__main__':
    sid = login()
    # sid = 's3tncc651GGyqkRtcXrPlcnSZuCTVxaMqrsOmJe_W_s49Ii_GtW9jiTrexbSphjYhuIioEjNSLWs-9boX8e2PA'
    # upload(sid)
    # download(sid)
    # logout(sid)

    upload2(sid)
    download2(sid)
