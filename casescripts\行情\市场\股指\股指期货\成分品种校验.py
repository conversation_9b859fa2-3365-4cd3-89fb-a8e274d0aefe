from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('行情/市场/股指/股指期货/成分品种校验/指数成分校验')
def _0883b8485b52104cd7c06639da6b3f5c(device):
    登录前页面处理(device)
    点击(device, '/底部导航栏/行情界面跳转按钮')
    点击(device, '/行情/市场/市场标题栏')
    点击(device, '/行情/市场/股指/股指标签')
    滑动控件至屏幕内(device, '/行情/市场/股指/股指期货/成分品种校验/IC主力品种')
    IC品种 = 获取控件对象(device, '/行情/市场/股指/股指期货/成分品种校验/IC主力品种')
    滑动控件至屏幕内(device, '/行情/市场/股指/股指期货/成分品种校验/IF主力品种')
    IF品种 = 获取控件对象(device, '/行情/市场/股指/股指期货/成分品种校验/IF主力品种')
    滑动控件至屏幕内(device, '/行情/市场/股指/股指期货/成分品种校验/IH主力品种')
    IH品种 = 获取控件对象(device, '/行情/市场/股指/股指期货/成分品种校验/IH主力品种')
    结果 = IC品种 != '' and IF品种 != '' and IH品种 != ''
    错误校验点 = '列表中丢失部分指数'
    return 结果, 错误校验点


