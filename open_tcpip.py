# 日常运维手机关机重启后，执行
# 手机设备通过usb线连接eu，执行脚本，开启手机的adb tcpip端口
import subprocess
from libs.adb import get_adb_devices


# 获取usb连线的手机设备
device_list = get_adb_devices()

# 开启adb tcpip
for device_id in device_list:
    if ':' not in device_id:
        # 创建设备与eu本地端口 监听关系
        p_c = subprocess.Popen('adb -s %s tcpip %s' % (device_id, 5555), shell=True, stdout=subprocess.PIPE)
        d_c = p_c.stdout.readline()
