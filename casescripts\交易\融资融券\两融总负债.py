from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('交易/融资融券/两融总负债/两融总负债')
def _3eaa3a04f6a93d29715ab4c4c341d3f0(device, 账号='30002187', 交易密码='123123', 通信密码='123123'):
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/融资融券')
    点击(device, '/交易/融资融券/融资融券首页/两融总负债')
    目录校验(device, '二', '两融-总负债', '/页面标题')
    实际结果 = 获取文本信息(device, '/交易/融资融券/两融总负债/两融总负债首页/总负债')
    print(实际结果)
    错误校验点 = 数字校验(device, 实际结果)
    结果 = 实际结果 != ''
    return 结果, 错误校验点


