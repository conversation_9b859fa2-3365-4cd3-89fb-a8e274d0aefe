from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('交易/港股通/我的港股交割/我的港股交割')
def _4878a53dfcb567bfa3405804869e2dc0(device, 账号='60000361', 交易密码='123123', 通信密码='123123'):
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/港股通')
    目录校验(device, '二', '港股通', '/页面标题')
    点击(device, '/交易/普通交易/港股通/港股通首页/我的港股交割')
    点击(device, '/交易/普通交易/港股通/港股通首页/我的港股交割/查询')
    登录前页面处理(device)
    实际结果 = ''
    if 获取控件对象(device, '/交易/普通交易/港股通/港股通首页/我的港股交割/查询/无记录') != '':
        实际结果 = True
    else:
        # 暂时无有记录情况
        # if 获取控件对象('/系统提示'):
        #     实际结果 = True
        pass
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


