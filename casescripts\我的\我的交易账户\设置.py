from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('我的/我的交易账户/设置/协议条款')
def _dfa01046f53ab997fe43347ca6133342(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # 初始化
    登录前页面处理(device)
    # 理财登陆(账号, 交易密码, 通信密码)
    交易登录(device, 账号, 交易密码, 通信密码)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    # time.sleep(3)
    # 上滑()
    目录校验(device, '一', '我的', '/我的/选中状态')
    循环滑动(device, '/我的/我的交易账户/设置', "上")
    点击(device, '/我的/我的交易账户/设置')
    目录校验(device, '二', '系统设置', '/页面标题')
    点击(device, '/我的/我的交易账户/设置/协议条款')
    点击(device, '/我的/我的交易账户/设置/协议条款/涨乐财富通服务协议')
    实际结果1 = 获取控件对象(device, '/我的/我的交易账户/设置/协议条款/涨乐财富通服务协议/显示') != ''
    点击(device, '/我的/我的交易账户/设置/协议条款/返回')
    点击(device, '/我的/我的交易账户/设置/协议条款/免责条款')
    实际结果2 = 获取控件对象(device, '/我的/我的交易账户/设置/协议条款/涨乐财富通服务协议/显示') != ''
    点击(device, '/我的/我的交易账户/设置/协议条款/返回')
    点击(device, '/我的/我的交易账户/设置/协议条款/涨乐财富通信息服务及使用条款')
    实际结果3 = 获取控件对象(device, '/我的/我的交易账户/设置/协议条款/涨乐财富通服务协议/显示') != ''
    结果 = 实际结果1 and 实际结果2 and 实际结果3
    # 实际结果 = 获取文本信息('/我的/我的交易账户/设置/清理缓存/系统提示')
    # 点击('/我的/我的交易账户/设置/服务器设置/系统提示/确定')
    # 点击('/底部确定/确定按钮')
    错误校验点 = 为空校验(device)
    点击(device, '/我的/我的交易账户/设置/协议条款/返回')
    return 结果, 错误校验点


@casescript('我的/我的交易账户/设置/投资者教育')
def _bdbe60d044d9d3084ac9a206e32a28ad(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119', 预期结果='有内容'):
    # 初始化
    登录前页面处理(device)
    # 理财登陆(账号, 交易密码, 通信密码)
    交易登录(device, 账号, 交易密码, 通信密码)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    # time.sleep(3)
    # 上滑()
    目录校验(device, '一', '我的', '/我的/选中状态')
    循环滑动(device, '/我的/我的交易账户/设置', "上")
    点击(device, '/我的/我的交易账户/设置')
    目录校验(device, '二', '系统设置', '/页面标题')
    点击(device, '/我的/我的交易账户/设置/投资者教育')
    try:
        实际结果 = 获取文本信息(device, '/我的/我的交易账户/设置/投资者教育/内容')
    except:
        实际结果 = False
    # 点击('/我的/我的交易账户/设置/协议条款/返回')
    # 实际结果 = 获取文本信息('/我的/我的交易账户/设置/清理缓存/系统提示')
    # 点击('/我的/我的交易账户/设置/服务器设置/系统提示/确定')
    # 点击('/底部确定/确定按钮')
    if 实际结果:
        结果 = True
    else:
        实际结果 = '无内容'
        结果 = False
    错误校验点 = 非空校验(device, 实际结果, 预期结果)
    return 结果, 错误校验点


@casescript('我的/我的交易账户/设置/服务器设置')
def _7c4eadc784abb20bc755fd0a93ab6517(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119', 预期结果='成功'):
    # 初始化
    登录前页面处理(device)
    # 理财登陆(账号, 交易密码, 通信密码)
    交易登录(device, 账号, 交易密码, 通信密码)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    # time.sleep(3)
    # 上滑()
    目录校验(device, '一', '我的', '/我的/选中状态')
    循环滑动(device, '/我的/我的交易账户/设置', "上")
    点击(device, '/我的/我的交易账户/设置')
    目录校验(device, '二', '系统设置', '/页面标题')
    点击(device, '/我的/我的交易账户/设置/服务器设置')
    if 获取控件对象(device, '/我的/我的交易账户/设置/服务器设置/未选'):
        点击(device, '/我的/我的交易账户/设置/服务器设置/未选')
    点击(device, '/我的/我的交易账户/设置/服务器设置/行情服务器地址')
    点击(device, '/我的/我的交易账户/设置/服务器设置/行情服务器地址/第一个地址')
    点击(device, '/我的/我的交易账户/设置/服务器设置/交易服务器地址')
    点击(device, '/我的/我的交易账户/设置/服务器设置/交易服务器地址/第一个地址')
    点击(device, '/我的/我的交易账户/设置/服务器设置/资讯服务器地址')
    点击(device, '/我的/我的交易账户/设置/服务器设置/交易服务器地址/第一个地址')
    点击(device, '/我的/我的交易账户/设置/服务器设置/确定')
    实际结果 = 获取文本信息(device, '/我的/我的交易账户/设置/默认首页设置/系统提示')
    结果 = 预期结果 in 实际结果
    # 点击('/底部确定/确定按钮')
    错误校验点 = 非空校验(device, 预期结果, 实际结果)
    点击(device, '/我的/我的交易账户/设置/服务器设置/系统提示/确定')
    return 结果, 错误校验点


@casescript('我的/我的交易账户/设置/清理缓存')
def _1bef59ec61ba149dcd99dfcc48e8f25c(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # 初始化
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    # time.sleep(3)
    # 上滑()
    目录校验(device, '一', '我的', '/我的/选中状态')
    循环滑动(device, '/我的/我的交易账户/设置', "上")
    点击(device, '/我的/我的交易账户/设置')
    目录校验(device, '二', '系统设置', '/页面标题')
    sleep(3)
    点击(device, '/我的/我的交易账户/设置/清理缓存')
    错误校验点 = 为空校验(device)
    # 实际结果 = 获取控件对象('/我的/我的交易账户/设置/清理缓存/系统提示')
    # 点击('/我的/我的交易账户/设置/服务器设置/系统提示/确定')
    结果 = True
    # 点击('/底部确定/确定按钮')
    return 结果, 错误校验点


@casescript('我的/我的交易账户/设置/版本')
def _2ff211e71042dd85cc0806eb51a4245a(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # 初始化
    登录前页面处理(device)
    # 理财登陆(账号, 交易密码, 通信密码)
    交易登录(device, 账号, 交易密码, 通信密码)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    # time.sleep(3)
    # 上滑()
    目录校验(device, '一', '我的', '/我的/选中状态')
    循环滑动(device, '/我的/我的交易账户/设置', "上")
    点击(device, '/我的/我的交易账户/设置')
    目录校验(device, '二', '系统设置', '/页面标题')
    点击(device, '/我的/我的交易账户/设置/版本')
    实际结果 = 获取文本信息(device, '/我的/我的交易账户/设置/版本/版本信息')
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


@casescript('我的/我的交易账户/设置/行情设置')
def _36dd610a2b50fa7e8201702f055c4f08(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # 初始化
    登录前页面处理(device)
    # 理财登陆(账号, 交易密码, 通信密码)
    交易登录(device, 账号, 交易密码, 通信密码)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    # time.sleep(3)
    # 上滑()
    目录校验(device, '一', '我的', '/我的/选中状态')
    循环滑动(device, '/我的/我的交易账户/设置', "上")
    点击(device, '/我的/我的交易账户/设置')
    目录校验(device, '二', '系统设置', '/页面标题')
    点击(device, '/我的/我的交易账户/设置/行情设置')
    try:
        获取控件对象(device, '/我的/我的交易账户/设置/行情设置/行情颜色选择智能')
        点击(device, '/我的/我的交易账户/设置/行情设置/行情颜色选择智能')
        点击(device, '/我的/我的交易账户/设置/行情设置/我知道了')
    except:
        try:
            # 获取控件对象('/我的/我的交易账户/设置/行情设置/行情颜色选择白色')
            点击(device, '/我的/我的交易账户/设置/行情设置/行情颜色选择白色')
            点击(device, '/我的/我的交易账户/设置/行情设置/立即体验')
        except:
            # 获取控件对象('/我的/我的交易账户/设置/行情设置/行情颜色选择黑色')
            点击(device, '/我的/我的交易账户/设置/行情设置/行情颜色选择黑色')
            点击(device, '/我的/我的交易账户/设置/行情设置/立即体验')
    # if 获取控件对象('/我的/我的交易账户/设置/行情设置/行情颜色选择智能'):
    #     点击('/我的/我的交易账户/设置/行情设置/行情颜色选择智能')
    # elif 获取控件对象('/我的/我的交易账户/设置/行情设置/行情颜色选择白色'):
    #     点击('/我的/我的交易账户/设置/行情设置/行情颜色选择白色')
    # else:
    #     点击('/我的/我的交易账户/设置/行情设置/行情颜色选择黑色')
    # 实际结果 = 获取文本信息('/我的/我的交易账户/设置/默认首页设置/系统提示')
    # 预期结果 = '成功'
    # 点击('/底部确定/确定按钮')
    # 结果 = 预期结果 in 实际结果
    结果 = True
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


@casescript('我的/我的交易账户/设置/账户与安全_华泰交易账户管理')
def _926775b74576530011f1828bc15183dd(device, 登录账户='666621794831', 交易密码='110119', 通信密码='rf110119', 预期结果='历史登录信息'):
    # 初始化
    登录前页面处理(device)
    # 理财登陆(账号, 交易密码, 通信密码)
    交易登录(device, 登录账户, 交易密码, 通信密码)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    目录校验(device, '一', '我的', '/我的/选中状态')
    循环滑动(device, '/我的/我的交易账户/设置', "上")
    点击(device, '/我的/我的交易账户/设置')
    目录校验(device, '二', '系统设置', '/页面标题')
    点击(device, '/我的/我的交易账户/设置/账户与安全')
    点击(device, '/我的/我的交易账户/设置/账户与安全/华泰交易账户管理')
    sleep(2)
    点击(device, '/我的/我的交易账户/设置/账户与安全/华泰交易账户管理/编辑')
    点击(device, '/我的/我的交易账户/设置/账户与安全/华泰交易账户管理/编辑/删除当前账户')
    点击(device, '/我的/我的交易账户/设置/账户与安全/华泰交易账户管理/编辑/确认删除当前账户')
    实际结果 = 获取文本信息(device, '/我的/我的交易账户/设置/账户与安全/华泰交易账户管理/编辑/获取标题')
    结果 = 实际结果 == 预期结果
    错误校验点 = 非空校验(device, 预期结果, 实际结果)
    return 结果, 错误校验点


@casescript('我的/我的交易账户/设置/账户与安全_密码安全设置')
def _4cc20c2c5cab68f554e4f0787ac4cff6(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # 初始化
    登录前页面处理(device)
    # 理财登陆(账号, 交易密码, 通信密码)
    交易登录(device, 账号, 交易密码, 通信密码)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    目录校验(device, '一', '我的', '/我的/选中状态')
    循环滑动(device, '/我的/我的交易账户/设置', "上")
    点击(device, '/我的/我的交易账户/设置')
    目录校验(device, '二', '系统设置', '/页面标题')
    点击(device, '/我的/我的交易账户/设置/账户与安全')
    点击(device, '/我的/我的交易账户/设置/账户与安全/密码安全设置')
    实际结果1 = 获取文本信息(device, '/我的/我的交易账户/设置/账户与安全/密码安全设置/交易密码权限')
    实际结果2 = 获取文本信息(device, '/我的/我的交易账户/设置/账户与安全/密码安全设置/资金密码权限')
    结果 = 实际结果1 == '已开启' or 实际结果2 == '已开启'
    错误校验点 = 非空校验(device, '已开启, 已开启', 实际结果1 + ', ' + 实际结果2)
    return 结果, 错误校验点


@casescript('我的/我的交易账户/设置/账户与安全_第三方账户绑定')
def _110a7df6bd1f015e0a5d9131ce18afde(device, 登录账户='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # 初始化
    登录前页面处理(device)
    # 理财登陆(账号, 交易密码, 通信密码)
    交易登录(device, 登录账户, 交易密码, 通信密码)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    目录校验(device, '一', '我的', '/我的/选中状态')
    循环滑动(device, '/我的/我的交易账户/设置', "上")
    点击(device, '/我的/我的交易账户/设置')
    目录校验(device, '二', '系统设置', '/页面标题')
    点击(device, '/我的/我的交易账户/设置/账户与安全')
    点击(device, '/我的/我的交易账户/设置/账户与安全/第三方账户绑定')
    实际结果 = 获取文本信息(device, '/登录标题')
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


@casescript('我的/我的交易账户/设置/音频自动播放')
def _0f1236c31d35a8407d33b8c9b98590b2(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # 初始化
    登录前页面处理(device)
    # 理财登陆(账号, 交易密码, 通信密码)
    交易登录(device, 账号, 交易密码, 通信密码)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    # time.sleep(3)
    # 上滑()
    目录校验(device, '一', '我的', '/我的/选中状态')
    循环滑动(device, '/我的/我的交易账户/设置', "上")
    点击(device, '/我的/我的交易账户/设置')
    目录校验(device, '二', '系统设置', '/页面标题')
    点击(device, '/我的/我的交易账户/设置/音频自动播放')
    点击(device, '/我的/我的交易账户/设置/音频自动播放/切换按钮')
    # 点击('/我的/我的交易账户/设置/协议条款/返回')
    # 实际结果 = 获取文本信息('/我的/我的交易账户/设置/清理缓存/系统提示')
    # 点击('/我的/我的交易账户/设置/服务器设置/系统提示/确定')
    预期结果 = True
    # 点击('/底部确定/确定按钮')
    结果 = True
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


@casescript('我的/我的交易账户/设置/默认首页设置')
def _48646f3511bab9398b60f96385004529(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119', 预期结果='成功'):
    # 初始化
    登录前页面处理(device)
    # 理财登陆(账号, 交易密码, 通信密码)
    交易登录(device, 账号, 交易密码, 通信密码)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    # time.sleep(3)
    # 上滑()
    目录校验(device, '一', '我的', '/我的/选中状态')
    循环滑动(device, '/我的/我的交易账户/设置', "上")
    点击(device, '/我的/我的交易账户/设置')
    目录校验(device, '二', '系统设置', '/页面标题')
    点击(device, '/我的/我的交易账户/设置/默认首页设置')
    sleep(2)
    点击(device, '/我的/我的交易账户/设置/默认首页设置/首页')
    实际结果 = 获取文本信息(device, '/我的/我的交易账户/设置/默认首页设置/系统提示')
    # 点击('/底部确定/确定按钮')
    结果 = 预期结果 in 实际结果
    错误校验点 = 非空校验(device, 预期结果, 实际结果)
    return 结果, 错误校验点


