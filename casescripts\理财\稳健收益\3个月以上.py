from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('理财/稳健收益/3个月以上/3个月以上')
def _06858a0932324d6039acebda406af2ff(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    理财登陆(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '理财', '/理财/选中状态')
    点击(device, '/理财/理财首页/稳健收益')
    目录校验(device, '二', '稳健收益', '/页面标题')
    sleep(5)
    try:
        点击(device, '/理财/理财/稳健收益/3个月以上')
    except:
        返回(device)
        点击(device, '/理财/理财首页/稳健收益')
        sleep(5)
        点击(device, '/理财/理财/稳健收益/3个月以上')
    try:
        实际结果 = 获取文本信息(device, '/理财/理财/稳健收益/3个月以上/当前在售产品数量')
        结果 = 实际结果 != ''
    except:
        实际结果 = 获取控件对象(device, '/理财/理财/稳健收益/3个月以上/无产品')
        结果 = 实际结果 != ''
    return 结果, None
    错误校验点 = 数字校验(device, 实际结果)


