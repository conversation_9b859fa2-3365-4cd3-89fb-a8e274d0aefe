from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('首页/热点/泰牛智投/小知识')
def _ddb9776825743c3c9c43a3bb78147117(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119', 预期结果='泰牛智投小知识'):
    # 业务交易数据 = 获取业务交易信息('交易/普通交易/场内基金/场内申购')
    # 证券代码 = 业务交易数据['证券代码']
    # 初始化
    登录前页面处理(device)
    # time.sleep(15)
    # 交易登录( 账号, 交易密码, 通信密码 )
    # ---------------------------------------------------------#
    点击(device, '/底部导航栏/首页界面跳转按钮')
    目录校验(device, '一', '首页', '/首页/选中状态')
    点击(device, '/首页/热点')
    目录校验(device, '二', '热点', '/首页/热点/选中状态')
    点击(device, '/首页/热点/向右点击按钮')
    # 交易登录( 账号, 交易密码, 通信密码 )
    点击(device, '/首页/热点/泰牛智投')
    点击(device, '/首页/热点/泰牛智投/小知识')
    sleep(3)
    实际结果 = 获取文本信息(device, '/首页/热点/泰牛智投/小知识/标题')
    print(实际结果)
    错误校验点 = 非空校验(device, 预期结果, 实际结果)
    结果 = 预期结果 == 实际结果
    return 结果, 错误校验点


@casescript('首页/热点/泰牛智投/我的订阅')
def _66b4021ef935118899770743f76f222e(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # 业务交易数据 = 获取业务交易信息('交易/普通交易/场内基金/场内申购')
    # 证券代码 = 业务交易数据['证券代码']
    # 初始化
    登录前页面处理(device)
    # time.sleep(15)
    # 交易登录( 账号, 交易密码, 通信密码 )
    # ---------------------------------------------------------#
    点击(device, '/底部导航栏/首页界面跳转按钮')
    目录校验(device, '一', '首页', '/首页/选中状态')
    点击(device, '/首页/热点')
    目录校验(device, '二', '热点', '/首页/热点/选中状态')
    点击(device, '/首页/热点/向右点击按钮')
    # 交易登录( 账号, 交易密码, 通信密码 )
    点击(device, '/首页/热点/泰牛智投')
    sleep(3)
    点击(device, '/首页/热点/泰牛智投/我的订阅')
    sleep(3)
    实际结果 = 获取文本信息(device, '/首页/热点/泰牛智投/我的订阅/订阅信息')
    print(实际结果)
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


@casescript('首页/热点/泰牛智投/精选策略_大数据-大数据-中长期多空信号')
def _a639a00fa8fb553b01c718ee65111624(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119', 预期结果='成功'):
    # 初始化
    登录前页面处理(device)
    # time.sleep(15)
    点击(device, '/底部导航栏/首页界面跳转按钮')
    目录校验(device, '一', '首页', '/首页/选中状态')
    点击(device, '/首页/热点')
    目录校验(device, '二', '热点', '/首页/热点/选中状态')
    点击(device, '/首页/热点/向右点击按钮')
    点击(device, '/首页/热点/泰牛智投')
    滑动控件至屏幕内(device, '/首页/热点/泰牛智投/精选策略/大数据-中长期多空信号')
    点击(device, '/首页/热点/泰牛智投/精选策略/大数据-中长期多空信号')
    sleep(2)
    点击(device, '/首页/热点/泰牛智投/精选策略/大数据-中长期多空信号/立即订阅')
    登录(device, 账号, 交易密码, 通信密码)
    点击(device, '/首页/热点/泰牛智投/精选策略/大数据-中长期多空信号/适当性确定')
    点击(device, '/首页/热点/泰牛智投/精选策略/大数据-中长期多空信号/提示确定')
    # 点击( '/首页/热点/泰牛智投/精选策略/大数据-中长期多空信号/风险确定' )
    实际结果 = 获取文本信息(device, '/首页/热点/泰牛智投/精选策略/大数据-中长期多空信号/订阅提示')
    print(实际结果)
    结果 = 预期结果 in 实际结果
    错误校验点 = 非空校验(device, 预期结果, 实际结果)
    点击(device, '/首页/热点/泰牛智投/精选策略/大数据-中长期多空信号/最终确定')
    滑动控件至屏幕内(device, '/首页/热点/泰牛智投/精选策略/大数据-中长期多空信号/取消订阅')
    点击(device, '/首页/热点/泰牛智投/精选策略/大数据-中长期多空信号/取消订阅')
    return 结果, 错误校验点


@casescript('首页/热点/泰牛智投/精选策略_大数据-大数据-市场热度')
def _cbd66904478ea730b1a983f4f275a47d(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    错误校验点 = ''
    # 业务交易数据 = 获取业务交易信息('交易/普通交易/场内基金/场内申购')
    # 证券代码 = 业务交易数据['证券代码']
    # 初始化
    登录前页面处理(device)
    # time.sleep(15)
    点击(device, '/底部导航栏/首页界面跳转按钮')
    目录校验(device, '一', '首页', '/首页/选中状态')
    点击(device, '/首页/热点')
    目录校验(device, '二', '热点', '/首页/热点/选中状态')
    点击(device, '/首页/热点/向右点击按钮')
    点击(device, '/首页/热点/泰牛智投')
    滑动控件至屏幕内(device, '/首页/热点/泰牛智投/精选策略/大数据-市场热度')
    点击(device, '/首页/热点/泰牛智投/精选策略/大数据-市场热度')
    sleep(2)
    if 获取控件对象(device, '/首页/热点/泰牛智投/精选策略/大数据-市场热度/立即订阅'):
        点击(device, '/首页/热点/泰牛智投/精选策略/大数据-市场热度/立即订阅')
        登录(device, 账号, 交易密码, 通信密码)
        # 点击( '/首页/热点/泰牛智投/精选策略/大数据-市场热度/协议确定', 用例名称)
        # 点击( '/首页/热点/泰牛智投/精选策略/大数据-市场热度/电子书约定确定', 用例名称)
        sleep(2)
        点击(device, '/首页/热点/泰牛智投/精选策略/大数据-市场热度/适当性确定')
        点击(device, '/首页/热点/泰牛智投/精选策略/大数据-市场热度/提示确定')
        sleep(6)
        # 点击( '/首页/热点/泰牛智投/精选策略/大数据-市场热度/风险确定', 用例名称)#
        实际结果 = 获取文本信息(device, '/首页/热点/泰牛智投/精选策略/大数据-市场热度/订阅提示')
        print(实际结果)
        预期结果 = '成功'
        结果 = 预期结果 in 实际结果
        错误校验点 = 非空校验(device, 预期结果, 实际结果)
        点击(device, '/首页/热点/泰牛智投/精选策略/大数据-市场热度/最终确定')
        滑动控件至屏幕内(device, '/首页/热点/泰牛智投/精选策略/大数据-市场热度/取消订阅')
        点击(device, '/首页/热点/泰牛智投/精选策略/大数据-市场热度/取消订阅')
        点击(device, '/首页/热点/泰牛智投/精选策略/大数据-市场热度/确定取消订阅')
    else:
        滑动控件至屏幕内(device, '/首页/热点/泰牛智投/精选策略/大数据-市场热度/取消订阅')
        点击(device, '/首页/热点/泰牛智投/精选策略/大数据-市场热度/取消订阅')
        点击(device, '/首页/热点/泰牛智投/精选策略/大数据-市场热度/确定取消订阅')
        实际结果 = 获取文本信息(device, '/系统提示')
        预期结果 = '已取消订阅'
        结果 = 预期结果 in 实际结果
        错误校验点 = 非空校验(device, 预期结果, 实际结果)
    return 结果, 错误校验点


@casescript('首页/热点/泰牛智投/精选策略_投顾策略')
def _af447ff7e4d860ff04cbfa61f00d8a8a(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119', 预期结果='安泰1号'):
    # 业务交易数据 = 获取业务交易信息('交易/普通交易/场内基金/场内申购')
    # 证券代码 = 业务交易数据['证券代码']
    # 初始化
    登录前页面处理(device)
    # time.sleep(15)
    # 交易登录( 账号, 交易密码, 通信密码 )
    # ---------------------------------------------------------#
    点击(device, '/底部导航栏/首页界面跳转按钮')
    目录校验(device, '一', '首页', '/首页/选中状态')
    点击(device, '/首页/热点')
    目录校验(device, '二', '热点', '/首页/热点/选中状态')
    点击(device, '/首页/热点/向右点击按钮')
    # 交易登录( 账号, 交易密码, 通信密码 )
    点击(device, '/首页/热点/泰牛智投')
    sleep(2)
    上滑(device)
    sleep(2)
    # 点击( '/首页/热点/泰牛智投/精选策略/全部投顾策略' )
    # 循环滑动( '/首页/热点/泰牛智投/精选策略/全部投顾策略', '上' )
    点击(device, '/首页/热点/泰牛智投/精选策略/全部投顾策略')
    实际结果 = 获取文本信息(device, '/首页/热点/泰牛智投/精选策略/全部投顾策略/第一条标题')
    print(实际结果)
    结果 = 预期结果 in 实际结果
    错误校验点 = 非空校验(device, "安泰1号", 实际结果)
    return 结果, 错误校验点


@casescript('首页/热点/泰牛智投/精选策略_盯盘T策略')
def _8bbf136395f48f3625704363f826c99c(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119', 预期结果='策略机器'):
    # 业务交易数据 = 获取业务交易信息('交易/普通交易/场内基金/场内申购')
    # 证券代码 = 业务交易数据['证券代码']
    # 初始化
    登录前页面处理(device)
    # time.sleep(15)
    # 交易登录( 账号, 交易密码, 通信密码 )
    # ---------------------------------------------------------#
    点击(device, '/底部导航栏/首页界面跳转按钮')
    目录校验(device, '一', '首页', '/首页/选中状态')
    点击(device, '/首页/热点')
    目录校验(device, '二', '热点', '/首页/热点/选中状态')
    点击(device, '/首页/热点/向右点击按钮')
    # 交易登录( 账号, 交易密码, 通信密码 )
    点击(device, '/首页/热点/泰牛智投')
    sleep(2)
    上滑(device)
    sleep(2)
    # 点击( '/首页/热点/泰牛智投/精选策略/全部投顾策略' )
    # 循环滑动( '/首页/热点/泰牛智投/精选策略/全部投顾策略', '上' )
    点击(device, '/首页/热点/泰牛智投/精选策略/盯盘T策略')
    实际结果 = 获取文本信息(device, '/首页/热点/泰牛智投/精选策略/盯盘T策略/标题')
    print(实际结果)
    错误校验点 = 非空校验(device, "策略机器", 实际结果)
    结果 = 预期结果 in 实际结果
    return 结果, 错误校验点


@casescript('首页/热点/泰牛智投/精选策略_策略擂台')
def _f516d3f7fc44e2ae632237b32b0bc2d6(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # 业务交易数据 = 获取业务交易信息('交易/普通交易/场内基金/场内申购')
    # 证券代码 = 业务交易数据['证券代码']
    # 初始化
    登录前页面处理(device)
    # time.sleep(15)
    # 交易登录( 账号, 交易密码, 通信密码 )
    # ---------------------------------------------------------#
    点击(device, '/底部导航栏/首页界面跳转按钮')
    目录校验(device, '一', '首页', '/首页/选中状态')
    点击(device, '/首页/热点')
    目录校验(device, '二', '热点', '/首页/热点/选中状态')
    点击(device, '/首页/热点/向右点击按钮')
    # 交易登录( 账号, 交易密码, 通信密码 )
    点击(device, '/首页/热点/泰牛智投')
    sleep(2)
    # 循环滑动('/首页/热点/泰牛智投/精选策略/全部策略擂台', '上')
    滑动控件至屏幕内(device, '/首页/热点/泰牛智投/精选策略/全部策略擂台')
    sleep(1)
    # 点击( '/首页/热点/泰牛智投/精选策略/全部投顾策略' )
    # 循环滑动( '/首页/热点/泰牛智投/精选策略/全部投顾策略', '上' )
    点击(device, '/首页/热点/泰牛智投/精选策略/全部策略擂台')
    sleep(2)
    实际结果 = 获取文本信息(device, '/首页/热点/泰牛智投/精选策略/全部策略擂台/判断')
    # 最后信息控件获取不到
    print(实际结果)
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


