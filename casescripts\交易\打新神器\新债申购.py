from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('交易/打新神器/新债申购/新债申购')
def _d516bb80853ff5e7b8a57c3695b3b9bc(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    错误校验点 = ''
    # 初始化
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/打新神器')
    目录校验(device, '二', '打新神器', '/页面标题')
    sleep(1)
    点击(device, '/交易/普通交易/打新神器/打新神器页面/新债申购')
    try:
        点击(device, '/交易/普通交易/打新神器/新债申购页面/申购数量')
        sleep(1)
        点击(device, '/交易/普通交易/打新神器/新债申购/申购数量弹窗/加号')
        # 点击('/交易/普通交易/打新神器/新债申购/申购数量弹窗/加号')
        点击(device, '/交易/普通交易/打新神器/新债申购/申购数量弹窗/确定')
        点击(device, '/交易/普通交易/打新神器/新债申购页面/立即申购')
        点击(device, '/交易/普通交易/打新神器/新债申购/确认申购')
        预期结果 = '提交成功'
        实际结果 = 获取文本信息(device, '/交易/普通交易/打新神器/新债申购/提交成功')
        错误校验点 = 非空校验(device, 预期结果, 实际结果)
        结果 = 预期结果 in 实际结果
        if 结果:
            点击(device, '/交易/普通交易/打新神器/新债申购/完成')
        else:
            点击(device, '/交易/普通交易/打新神器/新债申购/返回')
    except:
        实际结果 = 获取文本信息(device, '/交易/普通交易/打新神器/新债申购/无记录')
        错误校验点 = 为空校验(device)
        结果 = 实际结果 != ''
    return 结果, 错误校验点


