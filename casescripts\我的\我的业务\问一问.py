from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('我的/我的业务/问一问/问一问')
def _362f830fc0f12e13451380a8c4895810(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119', 预期结果='问一问'):
    登录前页面处理(device)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    上滑(device)
    点击(device, '/底部导航栏/理财界面跳转按钮')
    点击(device, '/底部导航栏/我的界面跳转按钮')
    目录校验(device, '一', '我的', '/我的/选中状态')
    点击(device, '/我的/我的业务')
    目录校验(device, '二', '办业务', '/我的/我的业务/页面标题')
    点击(device, '/我的/我的业务/问一问')
    实际结果 = 获取文本信息(device, '/我的/我的业务/问一问/标题')
    print(实际结果)
    错误校验点 = 非空校验(device, 预期结果, 实际结果)
    结果 = 实际结果 == 预期结果
    return 结果, 错误校验点


