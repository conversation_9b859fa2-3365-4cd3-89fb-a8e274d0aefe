from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('交易/紫金理财/紫金购买/认购申购')
def _d3a1e3e79c290c3aca0c907f59dbb182(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119', 证券代码='940018', 交易数量='1', 预期结果='已提交'):
    错误校验点 = ''
    # 初始化
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/紫金理财')
    目录校验(device, '二', '紫金理财', '/页面标题')
    点击(device, '/交易/普通交易/紫金理财/紫金理财首页/紫金购买')
    sleep(5)
    输入文本(device, '/交易/普通交易/紫金理财/紫金购买/认购申购页面/请输入紫金理财产品代码', 证券代码)
    sleep(1)
    点击(device, '/交易/普通交易/紫金理财/紫金购买/认购申购页面/立即购买按钮')
    sleep(1)
    点击(device, '/交易/普通交易/基金交易/紫金基金/基金购买/适当性确认/同意')
    sleep(1)
    点击(device, '/交易/普通交易/基金交易/紫金基金/基金购买/适当性确认/同意')  # 这里可以取巧用上一个按钮
    # 点击('/交易/普通交易/基金交易/紫金基金/基金购买/适当性确认/投资者确认书/同意')
    # 点击('/交易/普通交易/基金交易/紫金基金/基金购买/合同签署页面/同意勾选框')
    # 点击('/交易/普通交易/基金交易/紫金基金/基金购买/合同签署页面/确定')
    sleep(1)
    点击(device, '/交易/普通交易/紫金理财/紫金购买/协议告知页面/产品事项告知书/同意勾选框')
    sleep(1)
    点击(device, '/交易/普通交易/紫金理财/紫金购买/协议告知页面/产品事项告知书/确定')
    sleep(1)
    点击(device, '/交易/普通交易/紫金理财/紫金购买/协议告知页面/风险揭示书/同意勾选框')
    sleep(1)
    点击(device, '/交易/普通交易/紫金理财/紫金购买/协议告知页面/风险揭示书/确定')
    sleep(1)
    可用资金一 = 获取文本信息(device, '/交易/普通交易/紫金理财/紫金购买/可用资金')
    点击(device, '/交易/普通交易/基金交易/公募及私募基金/基金购买/认购申购页面/金额输入框1')
    点击(device, '/交易/普通交易/紫金理财/紫金购买/输入购买金额/确定')
    点击(device, '/交易/普通交易/基金交易/公募及私募基金/基金购买/认购申购页面/确定购买')
    sleep(2)
    实际结果 = 获取文本信息(device, '/交易/普通交易/紫金理财/紫金购买/委托提交结果页面/结果')
    错误校验点 = 非空校验(device, 预期结果, 实际结果)
    结果 = 预期结果 in 实际结果
    if 结果:
        返回(device)
        返回(device)
        返回(device)
        点击(device, '/底部导航栏/我的界面跳转按钮')
        点击(device, '/我的/现金')
        可用资金二 = 获取文本信息(device, '/我的/现金/可用资金')
        结果 = 可用资金二<可用资金一
        错误校验点 = '之前可用资金为%s,买卖后可用资金为%s' % (可用资金一, 可用资金二)
        if 结果:
            返回(device)
            点击(device, '/底部导航栏/交易界面跳转按钮')
            点击(device, '/交易/普通交易/普通交易首页/紫金理财')
            点击(device, '/交易/普通交易/紫金理财/紫金理财首页/撤销委托')
            # 点击('/交易/普通交易/其他交易/ETF网下/委托撤单页面/撤单确定按钮')
            # time.sleep(5)
            # 点击('/交易/普通交易/紫金理财/紫金购买/委托提交结果页面/确认中交易/交易内容')
            # 点击('/交易/普通交易/撤销委托/申购撤单')
            sleep(3)
            点击(device, '/交易/普通交易/其他交易/ETF网下/委托撤单页面/撤单')
            点击(device, '/底部确定/确定按钮')
            实际结果 = 获取文本信息(device, '/交易/普通交易/更多/网络投票/投票查询/投票记录详细页面/提示框内容')
            预期结果 = '成功'
            # 点击('/底部确定/确定按钮')
            # 实际结果 = 获取文本信息('/交易/普通交易/更多/网络投票/投票查询/投票记录详细页面/提示框内容')
            # 预期结果 = '成功'
            结果 = 预期结果 in 实际结果
            错误校验点 = 非空校验(device, 预期结果, 实际结果)
    elif '暂停' in 实际结果:
        结果 = 实际结果 != ''
        错误校验点 = 为空校验(device)
    return 结果, 错误校验点


