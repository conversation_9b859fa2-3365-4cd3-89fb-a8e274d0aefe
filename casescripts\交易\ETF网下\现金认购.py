from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('交易/ETF网下/现金认购/现金认购')
def _1ded388fcc49db48f1c3f27f466e53a9(device, 账号='01002878', 交易密码='123123', 通信密码='123123', 预期结果='现金认购'):
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    上滑(device)
    sleep(1)
    点击(device, '/交易/普通交易/普通交易首页/其他交易')
    目录校验(device, '二', '其他交易', '/页面标题')
    点击(device, '/交易/普通交易/其他交易/其他交易首页/ETF网下')
    点击(device, '/交易/普通交易/其他交易/其他交易首页/ETF网下_现金认购')
    实际结果 = 获取文本信息(device, '/页面标题')
    错误校验点 = 非空校验(device, 预期结果, 实际结果)
    结果 = 预期结果 in 实际结果
    return 结果, 错误校验点


