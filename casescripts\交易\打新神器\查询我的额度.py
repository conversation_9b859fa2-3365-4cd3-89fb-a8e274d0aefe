from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('交易/打新神器/查询我的额度/查询我的额度')
def _9816e96cdf6bbcef69c104a78d741e53(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # hxf 生产账户
    # 初始化
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/打新神器')
    目录校验(device, '二', '打新神器', '/页面标题')
    点击(device, '/交易/普通交易/打新神器/打新神器页面/查询我的额度')
    sleep(2)
    上海市场可申购额度 = 获取文本信息(device, '/交易/普通交易/打新神器/新股申购/查询额度/上海市场')
    深圳市场可申购额度 = 获取文本信息(device, '/交易/普通交易/打新神器/新股申购/查询额度/深圳市场')
    科创板可申购额度 = 获取文本信息(device, '/交易/普通交易/打新神器/新股申购/查询额度/科创板')
    print('上海市场可申购额度：%s  || 深圳市场可申购额度：%s || 科创板可申购额度：%s' % (上海市场可申购额度, 深圳市场可申购额度, 科创板可申购额度))
    错误校验点 = 为空校验(device)
    结果 = (len(上海市场可申购额度) > 0 and len(深圳市场可申购额度) > 0 and len(科创板可申购额度) > 0)
    return 结果, 错误校验点


