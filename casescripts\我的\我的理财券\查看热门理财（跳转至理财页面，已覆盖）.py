from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('我的/我的理财券/查看热门理财（跳转至理财页面，已覆盖）/查看热门理财（跳转至理财页面，已覆盖）')
def _1165a50b775b3658716aa84cbfcc602c(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119', 预期结果='严选理财'):
    点击(device, '/底部导航栏/我的界面跳转按钮')
    目录校验(device, '一', '我的', '/我的/选中状态')
    点击(device, '/我的/我的理财券')
    登录前页面处理(device)
    登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '二', '我的理财券', '/页面标题')
    # 登录(账号, 交易密码, 通信密码)
    sleep(5)
    点击(device, '/我的/我的理财券/历史卡券')
    点击(device, '/我的/我的理财券/历史卡券/已过期/查看热门理财')
    实际结果 = 获取文本信息(device, '/首页/热点/理财/判断')
    错误校验点 = 非空校验(device, 预期结果, 实际结果)
    结果 = 实际结果 == 预期结果
    return 结果, 错误校验点


