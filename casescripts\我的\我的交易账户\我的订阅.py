from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('我的/我的交易账户/我的订阅/查看可订阅内容')
def _01d812dc5aebc381460774bb515c3055(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # 初始化
    登录前页面处理(device)
    # 理财登陆(账号, 交易密码, 通信密码)
    交易登录(device, 账号, 交易密码, 通信密码)
    点击(device, '/底部导航栏/我的界面跳转按钮')
    目录校验(device, '一', '我的', '/我的/选中状态')
    # time.sleep(3)
    # 上滑()
    循环滑动(device, '/我的/我的交易账户/我的订阅', "上")
    点击(device, '/我的/我的交易账户/我的订阅')
    目录校验(device, '二', '资讯订阅服务', '/页面标题')
    sleep(5)
    点击(device, '/我的/我的交易账户/我的订阅/第一条订阅内容')
    实际结果 = 获取文本信息(device, '/我的/我的交易账户/我的订阅/第一条订阅内容/标题')
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


