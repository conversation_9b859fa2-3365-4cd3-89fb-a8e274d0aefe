# 流量监控功能使用说明

## 概述

流量监控功能用于监控UI自动化操作步骤的网络流量消耗，帮助分析每个操作对网络资源的使用情况。

## 功能特性

- **实时流量监控**: 在UI操作执行前后获取设备网络流量数据
- **多种数据源**: 支持从`/proc/net/dev`、`dumpsys netstats`等多种方式获取流量数据
- **非侵入式集成**: 通过装饰器方式集成到现有UI操作函数中
- **详细日志记录**: 记录每个操作的流量消耗详情
- **云端数据上报**: 可选择将流量数据上报到云端平台
- **灵活配置**: 支持通过配置文件控制功能开关

## 配置说明

在`config.ini`文件中添加以下配置：

```ini
; 流量监控配置
TRAFFIC_MONITOR_ENABLED = true    ; 是否启用流量监控
TRAFFIC_UPLOAD_ENABLED = false    ; 是否上报流量数据到云端
TRAFFIC_LOG_ENABLED = true        ; 是否记录流量日志
```

## 使用方法

### 1. 自动监控（推荐）

系统已为主要的UI操作函数添加了流量监控装饰器，包括：
- `点击()`
- `输入文本()`
- `上滑()`、`下滑()`、`左滑()`、`右滑()`
- `坐标点击()`

这些函数在执行时会自动进行流量监控，无需额外代码。

### 2. 手动监控

对于自定义操作，可以手动使用流量监控：

```python
from libs.traffic_monitor import get_traffic_monitor

# 获取设备的流量监控器
monitor = get_traffic_monitor(device.device_id)

# 开始监控
monitor_id = monitor.start_monitoring("自定义操作", "/控件/路径")

# 执行你的操作
# ... 你的代码 ...

# 停止监控并获取结果
result = monitor.stop_monitoring(monitor_id)
if result:
    print(f"流量消耗: {result['traffic_consumed']['total_bytes']} bytes")
```

### 3. 装饰器方式

为自定义函数添加流量监控：

```python
from libs.traffic_monitor import traffic_monitor_decorator

@traffic_monitor_decorator('我的操作')
def my_custom_operation(device, path):
    # 你的操作代码
    device[path]()
    return "操作完成"
```

## 日志输出格式

流量监控会产生以下格式的日志：

```
[流量监控] 点击 /交易/买入按钮 消耗流量: 1024 bytes (接收: 512 bytes, 发送: 512 bytes) 耗时: 1.23s
```

详细的JSON格式日志会保存在`log/traffic/{device_id}/traffic_YYYYMMDD.log`文件中。