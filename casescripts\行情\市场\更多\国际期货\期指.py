from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('行情/市场/更多/国际期货/期指/获取美元指数点值')
def _9b54d02d85e21797631d915b1e640409(device):
    登录前页面处理(device)
    print(device.appium.driver)
    点击(device, '/底部导航栏/行情界面跳转按钮')
    目录校验(device, '一', '行情', '/行情/选中状态')
    点击(device, '/行情/市场/市场标题栏')
    控件左滑(device, '/行情/市场/沪深按钮标签')
    点击(device, '/行情/市场/更多/更多标签')
    目录校验(device, '二', '更多', '/行情/更多/选中状态')
    滑动控件至屏幕内(device, '/行情/市场/更多/国际期货/期指/期指标签')
    点击(device, '/行情/市场/更多/国际期货/期指/期指标签')
    sleep(3)
    获取COMEX黄金价格 = 获取文本信息(device, '/行情/市场/更多/国际期货/期指/获取美元指数点值')
    结果 = 获取COMEX黄金价格 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


