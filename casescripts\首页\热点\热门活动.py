from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('首页/热点/热门活动/热门活动')
def _4df3295d6ea665ff87219d50b2f4e3ac(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # 初始化
    登录前页面处理(device)
    点击(device, '/底部导航栏/首页界面跳转按钮')
    目录校验(device, '一', '首页', '/首页/选中状态')
    点击(device, '/首页/热点')
    目录校验(device, '二', '热点', '/首页/热点/选中状态')
    滑动控件至屏幕内(device, '/首页/热点/热门活动/第一条标题')
    点击(device, '/首页/热点/热门活动/第一条标题')
    sleep(2)
    实际结果 = 获取文本信息(device, '/首页/热点/热门活动/第一条标题/详情')
    print(实际结果)
    错误校验点 = 为空校验(device)
    结果 = 实际结果 != ''
    return 结果, 错误校验点


