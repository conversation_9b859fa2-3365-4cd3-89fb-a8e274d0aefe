from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('首页/热点/热门主题/热门专题')
def _d8a5065d228a118ed6c66cc9baedf499(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # 业务交易数据 = 获取业务交易信息('交易/普通交易/场内基金/场内申购')
    # 证券代码 = 业务交易数据['证券代码']
    # 初始化
    登录前页面处理(device)
    # time.sleep(15)
    # 交易登录( 账号, 交易密码, 通信密码 )
    # ---------------------------------------------------------#
    # 登录( 账号, 交易密码, 通信密码 )
    点击(device, '/底部导航栏/首页界面跳转按钮')
    目录校验(device, '一', '首页', '/首页/选中状态')
    点击(device, '/首页/热点')
    目录校验(device, '二', '热点', '/首页/热点/选中状态')
    循环滑动(device, '/首页/热点/热门专题', '上')
    # 预期结果 = 获取文本信息('/首页/热点/热门专题/第一条')
    # print(预期结果)
    点击(device, '/首页/热点/热门专题/第一条')
    sleep(2)
    实际结果 = 获取文本信息(device, '/首页/热点/热门专题/第一条详情/标题')
    print(实际结果)
    错误校验点 = 为空校验(device)
    结果 = 实际结果 != ''
    return 结果, 错误校验点


