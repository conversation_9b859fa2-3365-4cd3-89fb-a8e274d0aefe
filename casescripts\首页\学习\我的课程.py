from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('首页/学习/我的课程/已完成')
def _8a882ac88509d85b4b66e58db5d38172(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # 初始化
    登录前页面处理(device)
    点击(device, '/底部导航栏/首页界面跳转按钮')
    目录校验(device, '一', '首页', '/首页/选中状态')
    点击(device, '/首页/学习')
    目录校验(device, '二', '学习', '/首页/学习/选中状态')
    点击(device, '/首页/学习/我的课程')
    sleep(2)
    点击(device, '/首页/学习/我的课程/已完成')
    sleep(2)
    实际结果 = ''
    try:
        实际结果 = 获取文本信息(device, '/首页/学习/我的课程/已完成/第一条记录')
    except:
        实际结果 = 获取文本信息(device, '/首页/学习/我的课程/已完成/无记录')
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


@casescript('首页/学习/我的课程/进行中')
def _31fcfcfd4040eb9c592735d5049f434c(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # 初始化
    登录前页面处理(device)
    点击(device, '/底部导航栏/首页界面跳转按钮')
    目录校验(device, '一', '首页', '/首页/选中状态')
    点击(device, '/首页/学习')
    目录校验(device, '二', '学习', '/首页/学习/选中状态')
    点击(device, '/首页/学习/我的课程')
    # 点击('/首页/学习/我的课程/进行中')
    sleep(2)
    实际结果 = ''
    try:
        点击(device, '/首页/学习/我的课程/进行中/第一条记录')
        实际结果 = 获取文本信息(device, '/首页/学习/我的课程/进行中/第一条/第一课')
    except:
        实际结果 = 获取文本信息(device, '/首页/学习/我的课程/进行中/无记录')
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


