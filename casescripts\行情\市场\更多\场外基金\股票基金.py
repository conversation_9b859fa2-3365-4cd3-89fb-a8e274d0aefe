from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('行情/市场/更多/场外基金/股票基金/获取首只股票基金名称')
def _6f731314282734a48a85a1f1d3cba3b4(device):
    登录前页面处理(device)
    点击(device, '/底部导航栏/行情界面跳转按钮')
    目录校验(device, '一', '行情', '/行情/选中状态')
    点击(device, '/行情/市场/市场标题栏')
    控件左滑(device, '/行情/市场/沪深按钮标签')
    点击(device, '/行情/市场/更多/更多标签')
    目录校验(device, '二', '更多', '/行情/更多/选中状态')
    滑动控件至屏幕内(device, '/行情/市场/更多/场外基金/股票基金/股票基金标签')
    点击(device, '/行情/市场/更多/场外基金/股票基金/股票基金标签')
    获取控件对象(device, '/行情/市场/更多/场外基金/登录后查看按钮')
    if 获取控件对象 != '':
        sleep(3)
        点击(device, '/行情/市场/更多/场外基金/登录后查看按钮')
        账号 = '666621794831'
        交易密码 = '110119'
        通信密码 = 'rf110119'
        登录(device, 账号, 交易密码, 通信密码)
    获取首只股票基金名称 = 获取文本信息(device, '/行情/市场/更多/场外基金/股票基金/获取首只股票基金名称')
    结果 = 获取首只股票基金名称 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


