from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('首页/热点/股市日历/股市日历')
def _cc3018bf2f4fb78955b192f60a763362(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # 业务交易数据 = 获取业务交易信息('交易/普通交易/场内基金/场内申购')
    # 证券代码 = 业务交易数据['证券代码']
    # 初始化
    登录前页面处理(device)
    # time.sleep(15)
    点击(device, '/底部导航栏/首页界面跳转按钮')
    目录校验(device, '一', '首页', '/首页/选中状态')
    点击(device, '/首页/热点')
    目录校验(device, '二', '热点', '/首页/热点/选中状态')
    上滑(device)
    # 循环滑动('/首页/热点/股市日历','上')
    点击(device, '/首页/热点/股市日历')
    sleep(3)
    点击(device, '/首页/热点/股市日历/进入日历')
    sleep(2)
    实际结果 = 获取文本信息(device, '/首页/热点/股市日历/进入日历/第一条标题')
    print(实际结果)
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


