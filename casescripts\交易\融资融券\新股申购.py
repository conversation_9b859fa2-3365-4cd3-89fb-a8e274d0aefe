from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('交易/融资融券/新股申购/新股申购')
def _f87b12dfc97c699edc0b76843dd68267(device, 账号='30002187', 交易密码='123123', 通信密码='123123'):
    # 初始化
    登录前页面处理(device)
    sleep(3)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/融资融券')
    sleep(2)
    上滑(device)
    点击(device, '/底部导航栏/首页界面跳转按钮')
    点击(device, '/底部导航栏/交易界面跳转按钮')
    点击(device, '/交易/融资融券/融资融券首页/更多')
    目录校验(device, '二', '更多', '/页面标题')
    点击(device, '/交易/融资融券/更多页面/新股申购')
    点击(device, '/交易/融资融券/更多页面/新股申购')


@casescript('交易/融资融券/新股申购/查询可申购额度')
def _9b4ed46c4488d13c03b8c3465d7e72d9(device, 账号='30002187', 交易密码='123123', 通信密码='123123'):
    # 初始化
    登录前页面处理(device)
    sleep(3)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/融资融券')
    sleep(2)
    上滑(device)
    点击(device, '/底部导航栏/首页界面跳转按钮')
    点击(device, '/底部导航栏/交易界面跳转按钮')
    点击(device, '/交易/融资融券/融资融券首页/更多')
    目录校验(device, '二', '更多', '/页面标题')
    点击(device, '/交易/融资融券/更多页面/新股申购')
    点击(device, '/交易/融资融券/更多/新股申购页面/查询可申购额度')
    try:
        上海额度 = 获取文本信息(device, '/交易/融资融券/更多/新股申购/查询可申购额度/上海额度')
        # print('上海额度：%s' % (上海额度))
        深圳额度 = 获取文本信息(device, '/交易/融资融券/更多/新股申购/查询可申购额度/深圳额度')
        print('深圳额度：%s' % (深圳额度))
        结果 = 上海额度 != '' and 深圳额度 != ''
        # 全局变量.错误校验点 = 非空校验(上海额度,深圳额度)
    except:
        实际结果 = 获取控件对象(device, '/交易/融资融券/综合查询/综合查询首页/当日委托/无记录')
        结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


@casescript('交易/融资融券/新股申购/查询新股中签')
def _be2015bddb5ad1f02a09a4548018b2ed(device, 账号='30002187', 交易密码='123123', 通信密码='123123'):
    # 初始化
    登录前页面处理(device)
    sleep(3)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/融资融券')
    sleep(2)
    上滑(device)
    点击(device, '/底部导航栏/首页界面跳转按钮')
    点击(device, '/底部导航栏/交易界面跳转按钮')
    点击(device, '/交易/融资融券/融资融券首页/更多')
    目录校验(device, '二', '更多', '/页面标题')
    点击(device, '/交易/融资融券/更多页面/新股申购')
    点击(device, '/交易/融资融券/更多/新股申购页面/查询新股中签')
    点击(device, '/交易/融资融券/综合查询/查询按钮')
    try:
        实际结果 = 获取控件对象(device, '/交易/融资融券/综合查询/综合查询首页/当日委托/无记录')
    except:
        pass
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


@casescript('交易/融资融券/新股申购/查询新股配号')
def _5eaabf187fee17970483988a9853d26d(device, 账号='30002187', 交易密码='123123', 通信密码='123123'):
    # 初始化
    登录前页面处理(device)
    sleep(3)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/融资融券')
    sleep(2)
    上滑(device)
    点击(device, '/底部导航栏/首页界面跳转按钮')
    点击(device, '/底部导航栏/交易界面跳转按钮')
    点击(device, '/交易/融资融券/融资融券首页/更多')
    目录校验(device, '二', '更多', '/页面标题')
    点击(device, '/交易/融资融券/更多页面/新股申购')
    点击(device, '/交易/融资融券/更多/新股申购页面/查询新股配号')
    点击(device, '/交易/融资融券/综合查询/查询按钮')
    try:
        实际结果 = 获取控件对象(device, '/交易/融资融券/综合查询/综合查询首页/当日委托/无记录')
    except:
        pass
    结果 = 实际结果 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


