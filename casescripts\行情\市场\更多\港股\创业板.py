from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('行情/市场/更多/港股/创业板/获取首只创业板股票名称')
def _ee7f02518c746487e8c6844a304b7c1b(device):
    登录前页面处理(device)
    print(device.appium.driver)
    点击(device, '/底部导航栏/行情界面跳转按钮')
    点击(device, '/行情/市场/市场标题栏')
    控件左滑(device, '/行情/市场/沪深按钮标签')
    点击(device, '/行情/市场/更多/更多标签')
    滑动控件至屏幕内(device, '/行情/市场/更多/港股/创业板/创业板标签')
    点击(device, '/行情/市场/更多/港股/创业板/创业板标签')
    首只沪港通股票名称 = 获取文本信息(device, '/行情/市场/更多/港股/创业板/首只创业板股票名称')
    结果 = 首只沪港通股票名称 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


