from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('行情/自选股/上证指数/上证指数')
def _90e06f0945a4c31c0a770a1b48aa0468(device):
    登录前页面处理(device)
    点击(device, '/底部导航栏/行情界面跳转按钮')
    目录校验(device, '一', '行情', '/行情/选中状态')
    点击(device, '/行情/自选股/自选股标签')
    目录校验(device, '二', '自选股', '/行情/自选股')
    if 获取文本信息(device, '/行情/自选股/上证指数/行情点数') == '':
        print('刷新页面_行情')
        点击(device, '/底部导航栏/交易界面跳转按钮')
        点击(device, '/底部导航栏/行情界面跳转按钮')
        sleep(5)
    上证指数行情点数 = 获取文本信息(device, '/行情/自选股/上证指数/行情点数')
    结果 = 上证指数行情点数 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


