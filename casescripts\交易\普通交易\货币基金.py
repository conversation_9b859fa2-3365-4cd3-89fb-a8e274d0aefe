from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('交易/普通交易/货币基金/基金申购')
def _4c16a89f176e451ece638be70b0e3407(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119', 证券代码='511651', 申购金额='100', 预期结果='操作成功'):
    错误校验点 = ''
    # 初始化
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/普通交易')
    点击(device, '/交易/普通交易/普通交易首页/更多')
    目录校验(device, '二', '股票交易', '/页面标题')
    点击(device, '/交易/普通交易/更多/更多交易页面/货币基金')
    点击(device, '/交易/普通交易/更多/更多交易页面/基金申购')
    输入文本(device, '/交易/普通交易/更多/货币基金/货币申购页面/基金代码输入框', 证券代码)
    输入文本(device, '/交易/普通交易/更多/货币基金/货币申购页面/申购金额输入框', 申购金额)
    可用资金一 = 获取文本信息(device, '/交易/普通交易/更多/更多交易页面/基金申购/可用资金')
    点击(device, '/交易/普通交易/更多/货币基金/货币申购页面/提交确定')
    点击(device, '/交易/普通交易/更多/货币基金/货币申购页面/申购确定按钮')
    实际结果 = 获取文本信息(device, '/交易/普通交易/更多/货币基金/货币赎回页面/委托结果字符串')
    # print(实际结果)
    错误校验点 = 非空校验(device, 预期结果, 实际结果)
    点击(device, '/交易/普通交易/更多/货币基金/货币申购页面/申购委托确定按钮')
    结果 = 预期结果 in 实际结果
    if 结果:
        输入文本(device, '/交易/普通交易/更多/货币基金/货币申购页面/基金代码输入框', 证券代码)
        可用资金二 = 获取文本信息(device, '/交易/普通交易/更多/更多交易页面/基金申购/可用资金')
        结果 = 可用资金二<可用资金一
        错误校验点 = '之前可用资金为%s,买卖后可用资金为%s' % (可用资金一, 可用资金二)
    return 结果, 错误校验点


@casescript('交易/普通交易/货币基金/基金赎回')
def _0d8d10a49914c30a83810177412a1dd7(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119', 证券代码='511651', 赎回份额='1', 预期结果='委托提交成功'):
    错误校验点 = ''
    # 初始化
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/普通交易')
    点击(device, '/交易/普通交易/普通交易首页/更多')
    目录校验(device, '二', '股票交易', '/页面标题')
    点击(device, '/交易/普通交易/更多/更多交易页面/货币基金')
    点击(device, '/交易/普通交易/更多/更多交易页面/基金赎回')
    输入文本(device, '/交易/普通交易/更多/货币基金/货币赎回页面/基金代码输入框', 证券代码)
    输入文本(device, '/交易/普通交易/更多/货币基金/货币赎回页面/赎回份额输入框', 赎回份额)
    可用份额一 = 获取文本信息(device, '/交易/普通交易/更多/更多交易页面/基金申购/可用资金')
    点击(device, '/交易/普通交易/更多/货币基金/货币赎回页面/提交确定')
    点击(device, '/交易/普通交易/更多/货币基金/货币赎回页面/委托确定按钮')
    实际结果 = 获取文本信息(device, '/交易/普通交易/更多/货币基金/货币赎回页面/委托结果字符串')
    # print(实际结果)
    # 点击('/交易/普通交易/更多/货币基金/货币申购页面/委托结果确定按钮')
    错误校验点 = 非空校验(device, 预期结果, 实际结果)
    结果 = 预期结果 in 实际结果
    if 结果:
        输入文本(device, '/交易/普通交易/更多/货币基金/货币赎回页面/基金代码输入框', 证券代码)
        可用份额二 = 获取文本信息(device, '/交易/普通交易/更多/更多交易页面/基金申购/可用资金')
        结果 = 可用份额二<= 可用份额一
        错误校验点 = '之前可用份额为%s,买卖后可用份额为%s' % (可用份额一, 可用份额二)
    return 结果, 错误校验点


@casescript('交易/普通交易/货币基金/查撤委托')
def _7bd11b1760df7d5f1f7fa043d380612b(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    错误校验点 = ''
    # 初始化
    登录前页面处理(device)
    交易登录(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '交易', '/交易/选中状态')
    点击(device, '/交易/普通交易/普通交易首页/普通交易')
    点击(device, '/交易/普通交易/普通交易首页/更多')
    目录校验(device, '二', '股票交易', '/页面标题')
    点击(device, '/交易/普通交易/更多/更多交易页面/货币基金')
    点击(device, '/交易/普通交易/更多/更多交易页面/查撤委托')
    try:
        点击(device, '/交易/普通交易/更多/货币基金/查撤委托/第一条记录')
        合同号 = 获取文本信息(device, '/交易/普通交易/更多/货币基金/查撤委托/详细页面/合同号')
        结果 = 合同号!=''
        错误校验点 = 为空校验(device)
        if 结果:
            可撤 = 获取文本信息(device, '/交易/普通交易/更多/货币基金/查撤委托/详细页面/可撤')
            预期结果 = '成功'
            if 可撤 == '不可撤':
                预期结果 = '不可撤'
            点击(device, '/交易/普通交易/更多/货币基金/委托详细页面/撤单按钮')
            实际结果 = 获取文本信息(device, '/交易/普通交易/更多/货币基金/查撤委托/详细页面/撤单/提示信息')
            错误校验点 = 非空校验(device, 预期结果, 实际结果)
            点击(device, '/交易/普通交易/更多/场内基金/场内申购页面/提示确定')
            结果 = 预期结果 in 实际结果
    except:
        暂无记录 = 获取控件对象(device, '/交易/普通交易/更多/货币基金/查撤委托/无记录')
        print('暂无记录')
        错误校验点 = 非空校验(device, '暂无记录', '')
        结果 = 暂无记录 != ''
    return 结果, 错误校验点


