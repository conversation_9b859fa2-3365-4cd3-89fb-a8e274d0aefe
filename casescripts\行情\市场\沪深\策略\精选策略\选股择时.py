from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('行情/市场/沪深/策略/精选策略/选股择时/首条策略名称')
def _408ba99d58cf2107569a9c2512ddeb50(device):
    登录前页面处理(device)
    点击(device, '/底部导航栏/行情界面跳转按钮')
    目录校验(device, '一', '行情', '/行情/选中状态')
    点击(device, '/行情/市场/市场标题栏')
    目录校验(device, '二', '沪深', '/行情/更多/选中状态')
    点击(device, '/行情/市场/泰牛智投')
    首条策略名称 = 获取文本信息(device, '/行情/市场/策略/选股择时/首条策略名称')
    结果 = 首条策略名称 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点


