from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('理财/本金保障/本金保障/本金保障')
def _ae63b848693da0edf3775d4f3e4a7168(device, 账号='666621794831', 交易密码='110119', 通信密码='rf110119'):
    # 初始化
    登录前页面处理(device)
    理财登陆(device, 账号, 交易密码, 通信密码)
    目录校验(device, '一', '理财', '/理财/选中状态')
    点击(device, '/理财/理财首页/本金保障')
    目录校验(device, '二', '本金保障型收益凭证', '/页面标题')
    sleep(3)
    文本 = 获取文本信息(device, '/理财/理财首页/本金保障/当前在售数量')
    实际结果 = 截取合同号(device, 文本)
    print('------------', 实际结果)
    结果 = int(实际结果) >= 0
    错误校验点 = 类型校验(device, int, 实际结果)
    return 结果, 错误校验点


