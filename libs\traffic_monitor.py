"""
流量监控模块
用于监控UI操作步骤的网络流量消耗
"""

import time
import subprocess
import threading
import json
import os
from functools import wraps
from typing import Dict, Tuple, Optional, Any
from libs.sdk import cloud_api, caches
from libs.log import log_control


class TrafficData:
    """流量数据结构"""
    def __init__(self, rx_bytes: int = 0, tx_bytes: int = 0, timestamp: float = None):
        self.rx_bytes = rx_bytes  # 接收字节数
        self.tx_bytes = tx_bytes  # 发送字节数
        self.timestamp = timestamp or time.time()

    def __sub__(self, other):
        """计算流量差值"""
        if not isinstance(other, TrafficData):
            raise TypeError("Can only subtract TrafficData from TrafficData")
        return TrafficData(
            rx_bytes=self.rx_bytes - other.rx_bytes,
            tx_bytes=self.tx_bytes - other.tx_bytes,
            timestamp=self.timestamp
        )

    @property
    def total_bytes(self):
        """总流量"""
        return self.rx_bytes + self.tx_bytes

    def to_dict(self):
        """转换为字典"""
        return {
            'rx_bytes': self.rx_bytes,
            'tx_bytes': self.tx_bytes,
            'total_bytes': self.total_bytes,
            'timestamp': self.timestamp
        }


class TrafficMonitor:
    """流量监控核心类"""

    def __init__(self, device_id: str):
        self.device_id = device_id
        self.enabled = caches.get('config.TRAFFIC_MONITOR_ENABLED', 'false') == 'true'
        self.upload_enabled = caches.get('config.TRAFFIC_UPLOAD_ENABLED', 'false') == 'true'
        self.log_enabled = caches.get('config.TRAFFIC_LOG_ENABLED', 'true') == 'true'
        self._lock = threading.Lock()

        # 流量数据缓存
        self._traffic_cache = {}

        # 创建日志目录
        self.log_dir = f'log/traffic/{device_id.replace(":", "")}'
        if not os.path.exists(self.log_dir):
            os.makedirs(self.log_dir)

    def _execute_adb_command(self, command: str) -> str:
        """执行ADB命令"""
        try:
            full_command = f'adb -s {self.device_id} shell "{command}"'
            result = subprocess.run(
                full_command,
                shell=True,
                capture_output=True,
                text=True,
                timeout=10
            )
            return result.stdout.strip()
        except subprocess.TimeoutExpired:
            log_control.warning(f"ADB命令超时: {command}")
            return ""
        except Exception as e:
            log_control.error(f"执行ADB命令失败: {command}, 错误: {e}")
            return ""

    def _get_traffic_from_proc_net_dev(self) -> Optional[TrafficData]:
        """从/proc/net/dev获取流量数据"""
        try:
            output = self._execute_adb_command("cat /proc/net/dev")
            if not output:
                return None

            total_rx = 0
            total_tx = 0

            for line in output.split('\n'):
                line = line.strip()
                if ':' in line and not line.startswith('lo:'):  # 排除回环接口
                    parts = line.split()
                    if len(parts) >= 10:
                        # /proc/net/dev格式: interface: rx_bytes rx_packets ... tx_bytes tx_packets ...
                        rx_bytes = int(parts[1])
                        tx_bytes = int(parts[9])
                        total_rx += rx_bytes
                        total_tx += tx_bytes

            return TrafficData(rx_bytes=total_rx, tx_bytes=total_tx)
        except Exception as e:
            log_control.error(f"从/proc/net/dev获取流量数据失败: {e}")
            return None

    def _get_traffic_from_dumpsys(self) -> Optional[TrafficData]:
        """从dumpsys netstats获取流量数据"""
        try:
            output = self._execute_adb_command("dumpsys netstats summary")
            if not output:
                return None

            # 解析dumpsys输出，查找总流量数据
            total_rx = 0
            total_tx = 0

            for line in output.split('\n'):
                line = line.strip()
                if 'Total bytes' in line or 'rb=' in line:
                    # 尝试解析不同格式的输出
                    if 'rb=' in line and 'tb=' in line:
                        # 格式: rb=123456 tb=789012
                        parts = line.split()
                        for part in parts:
                            if part.startswith('rb='):
                                total_rx += int(part.split('=')[1])
                            elif part.startswith('tb='):
                                total_tx += int(part.split('=')[1])

            if total_rx > 0 or total_tx > 0:
                return TrafficData(rx_bytes=total_rx, tx_bytes=total_tx)

            return None
        except Exception as e:
            log_control.error(f"从dumpsys获取流量数据失败: {e}")
            return None

    def _get_traffic_from_uid_stat(self, uid: str = None) -> Optional[TrafficData]:
        """从/proc/uid_stat获取特定应用流量数据"""
        try:
            if not uid:
                # 获取当前前台应用的UID
                uid_output = self._execute_adb_command("dumpsys activity activities | grep 'mResumedActivity'")
                if not uid_output:
                    return None
                # 这里需要进一步解析获取UID，暂时跳过
                return None

            rx_output = self._execute_adb_command(f"cat /proc/uid_stat/{uid}/tcp_rcv")
            tx_output = self._execute_adb_command(f"cat /proc/uid_stat/{uid}/tcp_snd")

            rx_bytes = int(rx_output) if rx_output.isdigit() else 0
            tx_bytes = int(tx_output) if tx_output.isdigit() else 0

            return TrafficData(rx_bytes=rx_bytes, tx_bytes=tx_bytes)
        except Exception as e:
            log_control.error(f"从uid_stat获取流量数据失败: {e}")
            return None

    def get_current_traffic(self) -> Optional[TrafficData]:
        """获取当前流量数据"""
        if not self.enabled:
            return None

        # 尝试多种方法获取流量数据
        traffic_data = self._get_traffic_from_proc_net_dev()
        if traffic_data is None:
            traffic_data = self._get_traffic_from_dumpsys()
        if traffic_data is None:
            traffic_data = self._get_traffic_from_uid_stat()

        return traffic_data

    def start_monitoring(self, operation_name: str, control_path: str = "") -> str:
        """开始监控操作"""
        if not self.enabled:
            return ""

        monitor_id = f"{operation_name}_{int(time.time() * 1000)}"
        start_traffic = self.get_current_traffic()

        if start_traffic:
            with self._lock:
                self._traffic_cache[monitor_id] = {
                    'operation_name': operation_name,
                    'control_path': control_path,
                    'start_traffic': start_traffic,
                    'start_time': time.time()
                }

        return monitor_id

    def stop_monitoring(self, monitor_id: str) -> Optional[Dict[str, Any]]:
        """停止监控操作并返回流量消耗数据"""
        if not self.enabled or not monitor_id:
            return None

        end_traffic = self.get_current_traffic()
        if not end_traffic:
            return None

        with self._lock:
            if monitor_id not in self._traffic_cache:
                return None

            cache_data = self._traffic_cache.pop(monitor_id)
            start_traffic = cache_data['start_traffic']

            # 计算流量消耗
            traffic_consumed = end_traffic - start_traffic
            duration = time.time() - cache_data['start_time']

            result = {
                'operation_name': cache_data['operation_name'],
                'control_path': cache_data['control_path'],
                'duration': duration,
                'traffic_consumed': traffic_consumed.to_dict(),
                'start_traffic': start_traffic.to_dict(),
                'end_traffic': end_traffic.to_dict(),
                'device_id': self.device_id,
                'timestamp': time.time()
            }

            # 记录日志
            if self.log_enabled:
                self._log_traffic_data(result)

            # 上报到云端
            if self.upload_enabled:
                self._upload_traffic_data(result)

            return result

    def _log_traffic_data(self, data: Dict[str, Any]):
        """记录流量数据到日志"""
        try:
            log_message = (
                f"[流量监控] {data['operation_name']} {data['control_path']} "
                f"消耗流量: {data['traffic_consumed']['total_bytes']} bytes "
                f"(接收: {data['traffic_consumed']['rx_bytes']} bytes, "
                f"发送: {data['traffic_consumed']['tx_bytes']} bytes) "
                f"耗时: {data['duration']:.2f}s"
            )
            log_control.info(log_message)

            # 写入详细日志文件
            log_file = os.path.join(self.log_dir, f"traffic_{time.strftime('%Y%m%d')}.log")
            with open(log_file, 'a', encoding='utf-8') as f:
                f.write(f"{time.strftime('%Y-%m-%d %H:%M:%S')} {json.dumps(data, ensure_ascii=False)}\n")
        except Exception as e:
            log_control.error(f"记录流量日志失败: {e}")

    def _upload_traffic_data(self, data: Dict[str, Any]):
        """上报流量数据到云端"""
        try:
            cloud_api('traffic-monitor', post_data=data)
        except Exception as e:
            log_control.error(f"上报流量数据失败: {e}")


# 全局流量监控器实例
_traffic_monitors = {}
_monitor_lock = threading.Lock()


def get_traffic_monitor(device_id: str) -> TrafficMonitor:
    """获取设备的流量监控器实例"""
    with _monitor_lock:
        if device_id not in _traffic_monitors:
            _traffic_monitors[device_id] = TrafficMonitor(device_id)
        return _traffic_monitors[device_id]


def traffic_monitor_decorator(operation_name: str = None):
    """流量监控装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 获取device参数
            device = None
            control_path = ""

            if args and hasattr(args[0], 'device_id'):
                device = args[0]
            elif len(args) > 1 and hasattr(args[1], 'device_id'):
                device = args[1]

            # 获取控件路径参数
            if len(args) > 1 and isinstance(args[1], str) and args[1].startswith('/'):
                control_path = args[1]
            elif len(args) > 2 and isinstance(args[2], str) and args[2].startswith('/'):
                control_path = args[2]

            if device is None:
                # 如果没有找到device参数，直接执行原函数
                return func(*args, **kwargs)

            # 开始流量监控
            monitor = get_traffic_monitor(device.device_id)
            func_name = operation_name or func.__name__
            monitor_id = monitor.start_monitoring(func_name, control_path)

            try:
                # 执行原函数
                result = func(*args, **kwargs)
                return result
            finally:
                # 停止流量监控
                monitor.stop_monitoring(monitor_id)

        return wrapper
    return decorator