from libs.adb import LocalDevice
from libs.sdk import casescript
from time import sleep
from casescripts.public import *


@casescript('行情/市场/更多/国内期货/上海白银/获取首只白银合约品种名称')
def _3136bc19f31e2e0a51740cec922f5d38(device):
    登录前页面处理(device)
    点击(device, '/底部导航栏/行情界面跳转按钮')
    目录校验(device, '一', '行情', '/行情/选中状态')
    点击(device, '/行情/市场/市场标题栏')
    控件左滑(device, '/行情/市场/沪深按钮标签')
    点击(device, '/行情/市场/更多/更多标签')
    目录校验(device, '二', '更多', '/行情/更多/选中状态')
    滑动控件至屏幕内(device, '/行情/市场/更多/国内期货/上海白银/上海白银标签')
    点击(device, '/行情/市场/更多/国内期货/上海白银/上海白银标签')
    try:
        获取首只黄金合约品种名称 = 获取文本信息(device, '/行情/市场/更多/国内期货/上海白银/获取首只白银合约品种名称')
    except:
        获取首只黄金合约品种名称 = 获取文本信息(device, '/交易/普通交易/买入页面/系统提示')
    结果 = 获取首只黄金合约品种名称 != ''
    错误校验点 = 为空校验(device)
    return 结果, 错误校验点
    点击(device, '/系统提示/通知/确定')


